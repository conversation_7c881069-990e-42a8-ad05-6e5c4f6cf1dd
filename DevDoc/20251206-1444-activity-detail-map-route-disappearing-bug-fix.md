# Activity Detail Map Route Disappearing Bug Fix Plan

## Problem Description

**Bug**: When user views workout details with color-coded speed route on map, then locks/unlocks screen, the color-coded route disappears and map zooms out, showing only start/end markers.

**User Impact**: Poor UX - users lose route visualization context when simply locking their phone.

## Root Cause Analysis

### Technical Analysis

The issue lies in aggressive background optimization implemented in [`ActivityDetailViewModel`](../RunApp/Views/ActivityDetailViewModel.swift). The current flow:

```mermaid
flowchart TD
    A[User views workout details with color-coded route] --> B[Screen turns off/app goes background]
    B --> C[scenePhase changes to .background]
    C --> D[ActivityDetailView.onChange triggers]
    D --> E[viewModel.setBackgroundState true]
    E --> F[suspendHeavyOperations called]
    F --> G[speedSegments.removeAll]
    F --> H[mapRegion = nil]
    H --> I[Screen turns on/app foregrounds]
    I --> J[scenePhase changes to .active]
    J --> K[resumeOperations called]
    K --> L[loadActivityData called BUT...]
    L --> M[Map position resets to .automatic]
    M --> N[Route disappears, only start/end markers remain]
```

### Specific Code Issues

1. **In [`ActivityDetailViewModel.suspendHeavyOperations()`](../RunApp/Views/ActivityDetailViewModel.swift:92-98):**
   ```swift
   private func suspendHeavyOperations() {
       speedSegments.removeAll() // ❌ Removes color-coded route
       mapRegion = nil // ❌ Resets zoom level
   }
   ```

2. **In [`ActivityDetailView`](../RunApp/Views/ActivityRowView.swift:394-398):**
   ```swift
   .onReceive(viewModel.$mapRegion) { newRegion in
       if let region = newRegion {
           mapPosition = .region(region)
       }
       // ❌ No handling when newRegion is nil
   }
   ```

3. **State Synchronization Issue**: View's `mapPosition` and ViewModel's `mapRegion` not properly synchronized during recovery.

## Code Review Assessment

### Previous Suggestions Evaluation

**Good Points:**
- Distinguishing screen lock vs. true backgrounding
- Preserving map region and speed segments during lock
- Improving recovery logic

**Issues with Previous Suggestions:**
- UIApplication.protectedDataWillBecomeUnavailable is overly complex
- Caching "last known region" adds unnecessary state complexity
- Doesn't leverage existing SwiftUI scenePhase properly

## Optimal Solution Plan

### Phase 1: Minimal State Preservation (KISS Principle)

**Priority: HIGH** - Fixes the immediate bug

#### 1.1 Modify Background State Management

**Files to Change:**
- [`ActivityDetailViewModel.swift`](../RunApp/Views/ActivityDetailViewModel.swift)

**Changes:**
```swift
// Add state preservation properties
private var preservedMapRegion: MKCoordinateRegion?
private var preservedSpeedSegments: [SpeedSegment] = []

private func suspendHeavyOperations() {
    // Preserve current state before clearing
    preservedMapRegion = mapRegion
    preservedSpeedSegments = speedSegments
    
    // Only clear for true background, not screen lock
    // Keep data intact for screen lock scenarios
}

private func resumeOperations() {
    // Restore preserved state
    if let preserved = preservedMapRegion {
        mapRegion = preserved
    }
    if !preservedSpeedSegments.isEmpty {
        speedSegments = preservedSpeedSegments
    }
    
    // Only reload if we don't have preserved data
    if mapRegion == nil || speedSegments.isEmpty {
        if !isLoading {
            Task {
                await loadActivityData()
            }
        }
    }
}
```

#### 1.2 Fix Recovery Logic in View

**Files to Change:**
- [`ActivityRowView.swift`](../RunApp/Views/ActivityRowView.swift) (ActivityDetailView section)

**Changes:**
```swift
.onReceive(viewModel.$mapRegion) { newRegion in
    if let region = newRegion {
        mapPosition = .region(region)
    }
    // Don't reset to .automatic when nil - preserve current position
    // This prevents zoom reset during state transitions
}
```

### Phase 2: Smart Background Detection

**Priority: MEDIUM** - Optimization for true backgrounding

#### 2.1 Timer-Based Background Detection

**Implementation:**
```swift
private var backgroundTimer: Timer?
private let TRUE_BACKGROUND_DELAY: TimeInterval = 2.0

func setBackgroundState(_ isBackground: Bool) {
    guard isInBackground != isBackground else { return }
    isInBackground = isBackground
    
    if isBackground {
        // Start timer - if still background after delay, truly backgrounded
        backgroundTimer = Timer.scheduledTimer(withTimeInterval: TRUE_BACKGROUND_DELAY, repeats: false) { _ in
            self.trulySuspendOperations()
        }
    } else {
        // Cancel timer and restore immediately
        backgroundTimer?.invalidate()
        backgroundTimer = nil
        resumeOperations()
    }
}

private func trulySuspendOperations() {
    // Only now truly clear state for memory pressure
    speedSegments.removeAll()
    mapRegion = nil
    preservedMapRegion = nil
    preservedSpeedSegments.removeAll()
}
```

### Phase 3: Enhanced Error Handling

**Priority: LOW** - Additional robustness

#### 3.1 Add State Validation

```swift
private func validateState() -> Bool {
    return !speedSegments.isEmpty && mapRegion != nil
}

func loadActivityData() async {
    // Skip if we already have valid state
    if validateState() && !isLoading {
        return
    }
    
    // Existing load logic...
}
```

## Implementation Steps

### Step 1: Preserve State During Lock (HIGH PRIORITY)
1. Add preservation properties to ViewModel
2. Modify `suspendHeavyOperations()` to preserve instead of clear
3. Modify `resumeOperations()` to restore preserved state
4. **Test**: Lock/unlock screen → route should remain visible

### Step 2: Fix Recovery Logic (HIGH PRIORITY)
1. Update ActivityDetailView's mapRegion handler
2. Prevent automatic reset to `.automatic` position
3. **Test**: Verify map position stays consistent

### Step 3: Smart Background Detection (MEDIUM PRIORITY)
1. Implement timer-based true background detection
2. Only clear state after confirmed true backgrounding
3. **Test**: App switching vs screen lock behavior

### Step 4: Validation & Testing (LOW PRIORITY)
1. Add state validation methods
2. Comprehensive testing scenarios
3. Performance monitoring

## Testing Strategy

### Test Scenarios

1. **Screen Lock/Unlock**
   - View route → lock screen → unlock → verify route visible
   - Expected: Route remains with same zoom level

2. **True App Backgrounding**
   - View route → switch to another app for >2s → return
   - Expected: Route cleared for memory, then reloaded

3. **Quick App Switch**
   - View route → quick app switch <2s → return
   - Expected: Route preserved

4. **Memory Pressure**
   - Monitor memory usage during lock/unlock cycles
   - Expected: No memory leaks

### Success Criteria

- ✅ Color-coded route persists through screen lock/unlock
- ✅ Map zoom level maintains position
- ✅ No performance regression
- ✅ Memory usage remains controlled during true backgrounding
- ✅ Watchdog timeout prevention still works

## Risk Assessment

### Low Risk
- **Memory**: Preserved data is minimal (region + segments array)
- **Performance**: GPU/CPU already suspended during screen lock
- **Compatibility**: Uses existing scenePhase mechanisms

### Mitigation
- **Watchdog**: True backgrounding (after timer) still clears state
- **Memory Pressure**: Monitor and adjust preservation strategy if needed

## Design Principles Applied

- **YAGNI**: Uses existing scenePhase, no new APIs
- **SOLID**: Single responsibility - ViewModel handles state, View handles rendering  
- **KISS**: Simple timer-based detection vs complex OS notifications
- **DRY**: Reuses existing state management patterns

## Files to Modify

1. [`RunApp/Views/ActivityDetailViewModel.swift`](../RunApp/Views/ActivityDetailViewModel.swift)
   - Add state preservation
   - Modify background state management
   - Add timer-based detection

2. [`RunApp/Views/ActivityRowView.swift`](../RunApp/Views/ActivityRowView.swift)
   - Fix mapRegion recovery logic in ActivityDetailView

## Expected Outcome

Users will experience seamless route visualization when locking/unlocking their device, while maintaining existing performance optimizations for true app backgrounding scenarios.