

### **Final Plan: Fixing the Activity Dismissal Performance Issue**

#### **1. Executive Summary**

The application freezes when dismissing the `ActivityDetailView` because the main UI thread is simultaneously tasked with two heavy operations:
1.  **Expensive Re-calculation:** The `ActivityView` list re-renders, and each visible `ActivityRowView` re-calculates the `distance` and `calories` for its workout from scratch.
2.  **Memory Deallocation:** The `ActivityDetailViewModel` is de-initialized, forcing the system to free large data structures from memory.

Our solution is a two-part strategy:
*   **Part 1 (Primary Fix):** We will pre-calculate `distance` and `calories` once when a workout is saved and store them. This eliminates the expensive re-calculation when the list is displayed.
*   **Part 2 (Secondary Fix):** We will proactively clear the ViewModel's memory when the detail view disappears to assist the system with deallocation.

#### **2. Problematic Dismissal Flow**

This diagram illustrates the current bottleneck on the main thread:

```mermaid
graph TD
    A[User Dismisses ActivityDetailView] --> B{Main Thread is Tasked};
    
    subgraph "Main Thread Workload"
        direction TB
        B --> C[1. Deallocate ActivityDetailViewModel];
        C --> D["- Free memory for large data arrays"];
        
        B --> F[2. Re-render ActivityView List];
        F --> G["For each visible row..."];
        G --> H["- Access activity.distance (triggers full calculation)"];
        G --> I["- Access activity.calories (triggers full calculation)"];
    end

    I --> J[UI Becomes Responsive (Heavily Delayed)];

    style H fill:#ffcccc,stroke:#333,stroke-width:2px
    style I fill:#ffcccc,stroke:#333,stroke-width:2px
```

---

### **3. Step-by-Step Implementation Plan**

#### **Part 1: Cache Expensive Properties (The Primary Fix)**

This part moves the heavy lifting from "read time" to "write time".

##### **Step 1.1: Modify the `RunActivity` Data Model**

*   **File:** [`RunApp/Models/RunActivity.swift`](RunApp/Models/RunActivity.swift)
*   **Action:** Add stored properties for `distance` and `calories` and update the logic to use them.

```swift
// In RunApp/Models/RunActivity.swift

@Model
final class RunActivity {
    // ... existing properties like id, startTime, etc. ...
    var coordinates: [Coordinate] = []
    
    // --- ADD THESE NEW STORED PROPERTIES ---
    var storedDistance: Double?
    var storedCalories: Double?
    
    // ... existing init() ...

    // --- MODIFY THE 'distance' COMPUTED PROPERTY ---
    var distance: Double {
        if let storedDistance = storedDistance {
            return storedDistance
        }
        // Fallback for older activities or if not finalized
        let value = calculateDistance()
        self.storedDistance = value
        return value
    }

    // --- MODIFY THE 'calories' COMPUTED PROPERTY ---
    var calories: Double {
        if let storedCalories = storedCalories {
            return storedCalories
        }
        // Fallback for older activities or if not finalized
        let value = calculateCalories()
        self.storedCalories = value
        return value
    }

    // --- ADD THIS NEW PUBLIC METHOD ---
    /// Performs all final, expensive calculations and stores the results.
    /// Call this once before inserting the activity into SwiftData.
    func finalizeActivity() {
        self.storedDistance = calculateDistance()
        self.storedCalories = calculateCalories()
    }

    // --- CREATE THIS PRIVATE METHOD from the old 'distance' logic ---
    private func calculateDistance() -> Double {
        var totalDistance = 0.0
        // Group coordinates into segments by isPaused status
        var currentSegment: [Coordinate] = []
        var segments: [[Coordinate]] = []

        for coordinate in coordinates {
            if currentSegment.isEmpty {
                currentSegment.append(coordinate)
            } else if currentSegment[0].isPaused == coordinate.isPaused {
                currentSegment.append(coordinate)
            } else {
                segments.append(currentSegment)
                currentSegment = [coordinate]
            }
        }
        if !currentSegment.isEmpty {
            segments.append(currentSegment)
        }

        // Only calculate distances for active segments
        let activeSegments = segments.filter { !$0.isEmpty && !$0[0].isPaused }

        for segment in activeSegments {
            guard segment.count > 1 else { continue }

            for i in 0..<(segment.count - 1) {
                let loc1 = CLLocation(latitude: segment[i].latitude,
                                    longitude: segment[i].longitude)
                let loc2 = CLLocation(latitude: segment[i+1].latitude,
                                    longitude: segment[i+1].longitude)

                let segmentDistance = loc1.distance(from: loc2)
                if segmentDistance < 100 {
                    totalDistance += segmentDistance
                }
            }
        }
        return totalDistance
    }

    // --- CREATE THIS PRIVATE METHOD from the old 'calories' logic ---
    private func calculateCalories() -> Double {
        return CalorieCalculator.calculateCalories(
            weight: weight,
            sportType: sportType,
            activeTime: activeRunningTime,
            storedPace: storedPace,
            hasMovement: hadMeaningfulMovement()
        )
    }
    
    // ... rest of the file ...
}
```

##### **Step 1.2: Update the Workout Saving Logic**

*   **File:** Find where you save a completed workout (likely [`RunApp/ContentView.swift`](RunApp/ContentView.swift) or a similar top-level view).
*   **Action:** Call the new `finalizeActivity()` method right before you insert the new `RunActivity` into the `modelContext`.

```swift
// Find the function that saves your workout. It will look something like this:

private func saveActivity() {
    // ... some logic to gather data ...

    let newActivity = RunActivity(
        startTime: startTime,
        endTime: Date(),
        // ... other parameters ...
    )

    // --- ADD THIS CRITICAL LINE ---
    // Perform all expensive calculations now and cache the results.
    newActivity.finalizeActivity()
    
    // Now, insert the fully processed object into SwiftData.
    modelContext.insert(newActivity)
    
    // ... rest of the function (e.g., cleanup) ...
}
```

---

#### **Part 2: Optimize Memory Deallocation (The Secondary Fix)**

This part helps the system by giving it a head start on memory cleanup.

##### **Step 2.1: Add `onDisappear` Logic to the ViewModel**

*   **File:** `RunApp/Views/ActivityDetailViewModel.swift`
*   **Action:** Add a new function to clear the largest data arrays.

```swift
// In ActivityDetailViewModel.swift

@MainActor
final class ActivityDetailViewModel: ObservableObject {
    // ... all existing properties and methods ...

    // --- ADD THIS NEW METHOD ---
    func onDisappear() {
        // Manually release the largest data structures to speed up deallocation.
        // This must run on the MainActor because it modifies @Published properties.
        speedSegments.removeAll()
    }
}
```

##### **Step 2.2: Call the `onDisappear` Method from the View**

*   **File:** [`RunApp/Views/ActivityRowView.swift`](RunApp/Views/ActivityRowView.swift) (inside the `ActivityDetailView` struct)
*   **Action:** Add the `.onDisappear` modifier to the view.

```swift
// In ActivityDetailView inside RunApp/Views/ActivityRowView.swift

struct ActivityDetailView: View {
    @StateObject private var viewModel: ActivityDetailViewModel
    // ... other properties ...

    var body: some View {
        VStack(spacing: 0) {
            // ... your existing view body ...
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbar { /* ... */ }
        .confirmationDialog(/* ... */) { /* ... */ }
        .alert(/* ... */) { /* ... */ }
        .task {
            await viewModel.loadActivityData()
        }
        // --- ADD THIS MODIFIER ---
        .onDisappear {
            viewModel.onDisappear()
        }
    }
}
```

---

#### **4. Expected Outcome**

After implementing both parts of this plan:
1.  **The primary bottleneck will be gone.** The expensive `distance` and `calories` calculations will no longer happen when the list view appears, making the UI fast and responsive.
2.  **Memory pressure will be reduced.** The proactive memory clearing will make the dismissal animation smoother.
3.  The user experience will be significantly improved, with no freezes or hangs when navigating back to the activity list.