# Route Simplification Optimization Implementation Plan

## Executive Summary

This document outlines a comprehensive plan to optimize workout data storage by implementing route simplification during the save process. The optimization will reduce storage size by 80-90% while maintaining data accuracy and eliminating redundant processing overhead. Additionally, it includes comprehensive stats pre-calculation and enhanced ActivityDetailView with tabbed interface.

## Current State Analysis

### What Currently Works ✅
- `storedDistance` and `storedCalories` properties in RunActivity
- `finalizeActivity()` method for pre-calculating stats
- `RouteSimplifier` with Douglas-<PERSON> algorithm
- Timer-based duration and pace calculation

### Critical Issues ❌
- **Raw GPS data stored**: 2000-5000 points per workout
- **Missing altitude data**: Altitude information is lost during save
- **Redundant processing**: Route simplification happens on every view load
- **Storage inefficiency**: Large coordinate arrays in SwiftData
- **Performance overhead**: Repeated simplification of same data
- **Stats recalculation**: ActivityDetailViewModel still calls expensive calculations
- **Limited stats**: Missing comprehensive pace, speed, lap, and calorie analytics
- **Basic UI**: ActivityDetailView only shows map, no comprehensive stats view

## Current ActivityDetailView Performance Issues

### Problem Analysis from ActivityDetailViewModel.swift

Looking at [`ActivityDetailViewModel.loadActivityData()`](RunApp/Views/ActivityDetailViewModel.swift:25):

```swift
// Line 39: Still calling expensive simplification
let displayRoute = activityCopy.getRouteForDisplay(context: .activityDetail)

// Line 40-41: Speed analysis on simplified route every time
let speedAnalysis = SpeedAnalyzer.analyzeRouteSpeed(displayRoute)
let segments = SpeedAnalyzer.createSpeedSegments(from: displayRoute, using: speedAnalysis)
```

**Current Issues**:
- Route simplification happens every time user views activity detail
- Speed analysis recalculated on every view load
- No use of pre-calculated stats (distance, calories, pace, duration)
- Heavy processing in background thread on every view
- Missing comprehensive stats (pace variations, speed analytics, lap data)
- No dedicated stats view interface

## Proposed Solution

### Data Flow Optimization

```
Current Flow (Inefficient):
Workout Finish → Save Raw GPS Data → SwiftData Storage (Large)
View Activity → Load Raw Data → Apply Douglas-Peucker → Speed Analysis → Display
Each View Load → Re-simplify + Re-analyze Same Data

New Flow (Optimized):
Workout Finish → Calculate ALL Stats from Raw Data → Apply Douglas-Peucker Once → Save Simplified Data + Pre-calculated Stats + Lap Data
View Activity → Load Pre-simplified Data + Pre-calculated Stats → Instant Display in Map/Stats Tabs
Future Views → Instant Load (No Re-processing)
```

### Enhanced Stats Calculation Strategy

| Metric Category | Data Source | Pre-calculated Stats |
|----------------|-------------|---------------------|
| **Distance** | Raw GPS data | Total distance, lap distances |
| **Calories** | Raw GPS data | Active calories, pause calories, total calories |
| **Time** | Timer + GPS | Total time, active time, pause time, lap times |
| **Pace** | Raw GPS data | Average pace, best pace, lap paces |
| **Speed** | Raw GPS data | Average speed, max speed, lap speeds |
| **Laps** | Raw GPS data | Metric laps (1km), Imperial laps (1mi) |
| **Altitude** | Raw GPS data | Currently missing, needs to be preserved |
| **Speed Segments** | Raw GPS data | Pre-calculated for visualization |

## Enhanced Data Models

### New Lap Data Structure

```swift
struct LapData: Codable {
    let lapNumber: Int
    let distance: Double // Actual distance covered in this lap
    let time: TimeInterval // Time taken for this lap
    let averagePace: Double // min/km or min/mi
    let averageSpeed: Double // m/s
    let startTime: Date
    let endTime: Date
    let startCoordinateIndex: Int // Index in coordinates array
    let endCoordinateIndex: Int // Index in coordinates array
    let unit: LapUnit // .metric (1km) or .imperial (1mi)
}

enum LapUnit: String, Codable, CaseIterable {
    case metric = "km"
    case imperial = "mi"
    
    var distance: Double {
        switch self {
        case .metric: return 1000.0 // 1 km in meters
        case .imperial: return 1609.34 // 1 mile in meters
        }
    }
}

struct WorkoutStats: Codable {
    // Pace Analytics
    let averagePace: Double // min/km
    let bestPace: Double // min/km (fastest sustained pace)
    
    // Speed Analytics
    let averageSpeed: Double // m/s
    let maxSpeed: Double // m/s
    
    // Time Analytics
    let totalTime: TimeInterval // Including pauses
    let activeTime: TimeInterval // Excluding pauses
    let pauseTime: TimeInterval // Total pause duration
    
    // Calorie Analytics
    let activeCalories: Double // Calories burned during active movement
    let pauseCalories: Double // Calories burned during pauses
    let totalCalories: Double // activeCalories + pauseCalories
    
    // Lap Data
    let metricLaps: [LapData] // 1km laps
    let imperialLaps: [LapData] // 1 mile laps
}
```

## Implementation Plan

### Phase 1: Update Data Models (Low Risk)

#### 1.1 Update Coordinate Struct
**File**: `RunApp/Models/RunActivity.swift`

**Current**:
```swift
struct Coordinate: Codable {
    let latitude: Double
    let longitude: Double
    let timestamp: Date
    let isPaused: Bool
    let speed: Double?
    // ❌ NO ALTITUDE FIELD
}
```

**Updated**:
```swift
struct Coordinate: Codable {
    let latitude: Double
    let longitude: Double
    let altitude: Double // ✅ ADD ALTITUDE
    let timestamp: Date
    let isPaused: Bool
    let speed: Double?
}
```

#### 1.2 Add Enhanced Data Storage to RunActivity
**File**: `RunApp/Models/RunActivity.swift`

```swift
// Add to RunActivity model
@Attribute(.externalStorage) 
var storedSpeedSegments: [SpeedSegment]? = nil

@Attribute(.externalStorage)
var storedWorkoutStats: WorkoutStats? = nil

/// Get comprehensive workout stats without recalculation
var workoutStats: WorkoutStats {
    if let stored = storedWorkoutStats {
        return stored // ✅ Use pre-calculated stats
    }
    // Legacy fallback
    return calculateWorkoutStatsLegacy()
}

/// Get speed segments without recalculation
var speedSegments: [SpeedSegment] {
    if let stored = storedSpeedSegments {
        return stored // ✅ Use pre-calculated segments
    }
    // Legacy fallback
    let displayRoute = getRouteForDisplay(context: .activityDetail)
    let speedAnalysis = SpeedAnalyzer.analyzeRouteSpeed(displayRoute)
    return SpeedAnalyzer.createSpeedSegments(from: displayRoute, using: speedAnalysis)
}

// Convenient accessors for UI
var averagePace: Double { workoutStats.averagePace }
var bestPace: Double { workoutStats.bestPace }
var averageSpeed: Double { workoutStats.averageSpeed }
var maxSpeed: Double { workoutStats.maxSpeed }
var totalTime: TimeInterval { workoutStats.totalTime }
var activeTime: TimeInterval { workoutStats.activeTime }
var pauseTime: TimeInterval { workoutStats.pauseTime }
var activeCalories: Double { workoutStats.activeCalories }
var pauseCalories: Double { workoutStats.pauseCalories }
var totalCalories: Double { workoutStats.totalCalories }
var metricLaps: [LapData] { workoutStats.metricLaps }
var imperialLaps: [LapData] { workoutStats.imperialLaps }
```

#### 1.3 Add Static Helper Methods for Enhanced Stats Calculation
**File**: `RunApp/Models/RunActivity.swift`

```swift
// Add static methods to avoid side effects in initializer
extension RunActivity {
    /// Calculate comprehensive workout stats from raw data
    static func calculateWorkoutStats(
        from coordinates: [Coordinate],
        startTime: Date,
        endTime: Date,
        activeRunningTime: TimeInterval,
        weight: Double,
        sportType: SportType
    ) -> WorkoutStats {
        
        // 1. Separate active and paused segments
        let segments = groupCoordinatesByPauseStatus(coordinates)
        let activeSegments = segments.filter { !$0.isEmpty && !$0[0].isPaused }
        let pausedSegments = segments.filter { !$0.isEmpty && $0[0].isPaused }
        
        // 2. Calculate pace and speed analytics
        let paceStats = calculatePaceAndSpeedStats(from: activeSegments)
        
        // 3. Calculate time analytics
        let totalTime = endTime.timeIntervalSince(startTime)
        let pauseTime = totalTime - activeRunningTime
        
        // 4. Calculate calorie analytics
        let calorieStats = calculateCalorieStats(
            activeSegments: activeSegments,
            pausedSegments: pausedSegments,
            activeTime: activeRunningTime,
            pauseTime: pauseTime,
            weight: weight,
            sportType: sportType
        )
        
        // 5. Calculate lap data
        let metricLaps = calculateLaps(from: coordinates, unit: .metric, startTime: startTime)
        let imperialLaps = calculateLaps(from: coordinates, unit: .imperial, startTime: startTime)
        
        return WorkoutStats(
            averagePace: paceStats.averagePace,
            bestPace: paceStats.bestPace,
            averageSpeed: paceStats.averageSpeed,
            maxSpeed: paceStats.maxSpeed,
            totalTime: totalTime,
            activeTime: activeRunningTime,
            pauseTime: pauseTime,
            activeCalories: calorieStats.activeCalories,
            pauseCalories: calorieStats.pauseCalories,
            totalCalories: calorieStats.totalCalories,
            metricLaps: metricLaps,
            imperialLaps: imperialLaps
        )
    }
    
    /// Group coordinates by pause status
    static func groupCoordinatesByPauseStatus(_ coordinates: [Coordinate]) -> [[Coordinate]] {
        var segments: [[Coordinate]] = []
        var currentSegment: [Coordinate] = []
        
        for coordinate in coordinates {
            if currentSegment.isEmpty {
                currentSegment.append(coordinate)
            } else if currentSegment[0].isPaused == coordinate.isPaused {
                currentSegment.append(coordinate)
            } else {
                segments.append(currentSegment)
                currentSegment = [coordinate]
            }
        }
        if !currentSegment.isEmpty {
            segments.append(currentSegment)
        }
        
        return segments
    }
    
    /// Calculate pace and speed statistics
    static func calculatePaceAndSpeedStats(from activeSegments: [[Coordinate]]) -> (averagePace: Double, bestPace: Double, averageSpeed: Double, maxSpeed: Double) {
        var totalDistance = 0.0
        var totalTime: TimeInterval = 0
        var maxSpeed = 0.0
        var bestPace = Double.greatestFiniteMagnitude
        var validSpeedReadings: [Double] = []
        
        for segment in activeSegments {
            guard segment.count > 1 else { continue }
            
            for i in 0..<(segment.count - 1) {
                let loc1 = CLLocation(latitude: segment[i].latitude, longitude: segment[i].longitude)
                let loc2 = CLLocation(latitude: segment[i+1].latitude, longitude: segment[i+1].longitude)
                
                let segmentDistance = loc1.distance(from: loc2)
                if segmentDistance < 100 { // Filter GPS errors
                    totalDistance += segmentDistance
                    
                    let timeDiff = segment[i+1].timestamp.timeIntervalSince(segment[i].timestamp)
                    totalTime += timeDiff
                    
                    // Calculate instantaneous speed and pace
                    if timeDiff > 0 {
                        let speed = segmentDistance / timeDiff
                        validSpeedReadings.append(speed)
                        maxSpeed = max(maxSpeed, speed)
                        
                        if speed > 0.5 { // Minimum speed threshold for valid pace
                            let pace = (1000.0 / speed) / 60.0 // min/km
                            if pace < 30.0 { // Maximum reasonable pace
                                bestPace = min(bestPace, pace)
                            }
                        }
                    }
                }
            }
        }
        
        let averageSpeed = validSpeedReadings.isEmpty ? 0 : validSpeedReadings.reduce(0, +) / Double(validSpeedReadings.count)
        let averagePace = averageSpeed > 0 ? (1000.0 / averageSpeed) / 60.0 : 0
        
        return (
            averagePace: averagePace,
            bestPace: bestPace == Double.greatestFiniteMagnitude ? 0 : bestPace,
            averageSpeed: averageSpeed,
            maxSpeed: maxSpeed
        )
    }
    
    /// Calculate calorie breakdown for active vs pause periods
    static func calculateCalorieStats(
        activeSegments: [[Coordinate]],
        pausedSegments: [[Coordinate]],
        activeTime: TimeInterval,
        pauseTime: TimeInterval,
        weight: Double,
        sportType: SportType
    ) -> (activeCalories: Double, pauseCalories: Double, totalCalories: Double) {
        
        // Active calories: Use sport-specific MET values
        let activeCalories = CalorieCalculator.calculateActiveCalories(
            weight: weight,
            sportType: sportType,
            activeTime: activeTime
        )
        
        // Pause calories: Use resting metabolic rate
        let pauseCalories = CalorieCalculator.calculateRestingCalories(
            weight: weight,
            time: pauseTime
        )
        
        return (
            activeCalories: activeCalories,
            pauseCalories: pauseCalories,
            totalCalories: activeCalories + pauseCalories
        )
    }
    
    /// Calculate lap data for given unit (metric/imperial)
    static func calculateLaps(from coordinates: [Coordinate], unit: LapUnit, startTime: Date) -> [LapData] {
        guard !coordinates.isEmpty else { return [] }
        
        var laps: [LapData] = []
        var currentLapDistance = 0.0
        var lapStartIndex = 0
        var lapStartTime = startTime
        var lapNumber = 1
        
        // Filter to active coordinates only
        let activeCoordinates = coordinates.filter { !$0.isPaused }
        guard activeCoordinates.count > 1 else { return [] }
        
        for i in 0..<(activeCoordinates.count - 1) {
            let loc1 = CLLocation(latitude: activeCoordinates[i].latitude, longitude: activeCoordinates[i].longitude)
            let loc2 = CLLocation(latitude: activeCoordinates[i+1].latitude, longitude: activeCoordinates[i+1].longitude)
            
            let segmentDistance = loc1.distance(from: loc2)
            if segmentDistance < 100 { // Filter GPS errors
                currentLapDistance += segmentDistance
                
                // Check if we've completed a lap
                if currentLapDistance >= unit.distance {
                    let lapEndTime = activeCoordinates[i+1].timestamp
                    let lapTime = lapEndTime.timeIntervalSince(lapStartTime)
                    let lapAverageSpeed = currentLapDistance / lapTime
                    let lapAveragePace = lapAverageSpeed > 0 ? (1000.0 / lapAverageSpeed) / 60.0 : 0
                    
                    let lap = LapData(
                        lapNumber: lapNumber,
                        distance: currentLapDistance,
                        time: lapTime,
                        averagePace: lapAveragePace,
                        averageSpeed: lapAverageSpeed,
                        startTime: lapStartTime,
                        endTime: lapEndTime,
                        startCoordinateIndex: lapStartIndex,
                        endCoordinateIndex: i + 1,
                        unit: unit
                    )
                    
                    laps.append(lap)
                    
                    // Reset for next lap
                    currentLapDistance = 0.0
                    lapStartIndex = i + 1
                    lapStartTime = lapEndTime
                    lapNumber += 1
                }
            }
        }
        
        return laps
    }
    
    /// Calculate distance from coordinate array without requiring instance state
    static func calculateDistance(from coordinates: [Coordinate]) -> Double {
        let segments = groupCoordinatesByPauseStatus(coordinates)
        let activeSegments = segments.filter { !$0.isEmpty && !$0[0].isPaused }
        
        var totalDistance = 0.0
        for segment in activeSegments {
            guard segment.count > 1 else { continue }
            
            for i in 0..<(segment.count - 1) {
                let loc1 = CLLocation(latitude: segment[i].latitude, longitude: segment[i].longitude)
                let loc2 = CLLocation(latitude: segment[i+1].latitude, longitude: segment[i+1].longitude)
                
                let segmentDistance = loc1.distance(from: loc2)
                if segmentDistance < 100 { // Filter out unrealistic jumps
                    totalDistance += segmentDistance
                }
            }
        }
        
        return totalDistance
    }
    
    /// Pre-calculate speed segments for storage
    static func calculateSpeedSegments(from coordinates: [Coordinate]) -> [SpeedSegment] {
        guard !coordinates.isEmpty else { return [] }
        
        let speedAnalysis = SpeedAnalyzer.analyzeRouteSpeed(coordinates)
        return SpeedAnalyzer.createSpeedSegments(from: coordinates, using: speedAnalysis)
    }
}
```

#### 1.4 Add New RunActivity Initialization Method
**File**: `RunApp/Models/RunActivity.swift`

```swift
/// Optimized initializer: calculates comprehensive stats from raw data, stores simplified coordinates
/// This method avoids side effects by using static helper methods for calculations
init(
    rawCoordinates: [Coordinate],
    simplificationTolerance: Double = 0.0001,
    startTime: Date = Date(),
    endTime: Date = Date(),
    location: String = "",
    weight: Double = 0.0,
    activeRunningTime: TimeInterval = 0.0,
    sportType: SportType = SportType.run
) {
    // Step 1: Calculate comprehensive stats from raw data using static methods
    let workoutStats = Self.calculateWorkoutStats(
        from: rawCoordinates,
        startTime: startTime,
        endTime: endTime,
        activeRunningTime: activeRunningTime,
        weight: weight,
        sportType: sportType
    )
    
    // Step 2: Apply Douglas-Peucker simplification
    let simplifiedCoords = RouteSimplifier.safeSimplify(
        coordinates: rawCoordinates,
        tolerance: simplificationTolerance
    )
    
    // Step 3: Pre-calculate speed segments from simplified coordinates
    let preCalculatedSpeedSegments = Self.calculateSpeedSegments(from: simplifiedCoords)
    
    // Step 4: Initialize all properties with their final values (no side effects)
    self.id = UUID()
    self.startTime = startTime
    self.endTime = endTime
    self.location = location
    self.coordinates = simplifiedCoords
    self.storedPace = workoutStats.averagePace
    self.weight = weight
    self.activeRunningTime = activeRunningTime
    self.sportType = sportType
    self.storedDistance = Self.calculateDistance(from: rawCoordinates)
    self.storedCalories = workoutStats.totalCalories
    self.storedSpeedSegments = preCalculatedSpeedSegments
    self.storedWorkoutStats = workoutStats
}
```

### Phase 2: Update Save Workflow (Medium Risk)

#### 2.1 Modify ContentView.saveActivity()
**File**: `RunApp/ContentView.swift`

**Current** (lines 764-789):
```swift
let coordinates = routeManager.completedSegments.flatMap { segment in
    segment.coordinates.map { location in
        Coordinate(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude,
            timestamp: location.timestamp,
            isPaused: segment.isPaused,
            speed: location.speed >= 0 ? location.speed : nil
        )
    }
}

let activity = RunActivity(
    startTime: startTime,
    endTime: Date(),
    location: locationName,
    coordinates: coordinates, // Raw data
    averagePace: averagePace,
    weight: profile.first?.weight ?? 70.0,
    activeRunningTime: activeRunningTime,
    sportType: selectedSportType ?? .run
)

activity.finalizeActivity() // Recalculates everything
```

**Updated**:
```swift
let rawCoordinates = routeManager.completedSegments.flatMap { segment in
    segment.coordinates.map { location in
        Coordinate(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude,
            altitude: location.altitude, // ✅ ADD ALTITUDE
            timestamp: location.timestamp,
            isPaused: segment.isPaused,
            speed: location.speed >= 0 ? location.speed : nil
        )
    }
}

let activity = RunActivity(
    rawCoordinates: rawCoordinates, // Use new optimized initializer
    simplificationTolerance: RouteSimplifier.adaptiveTolerance(for: .activityList),
    startTime: startTime,
    endTime: Date(),
    location: locationName,
    weight: profile.first?.weight ?? 70.0,
    activeRunningTime: activeRunningTime,
    sportType: selectedSportType ?? .run
)

// ✅ No need for finalizeActivity() - comprehensive stats already calculated
```

### Phase 3: Update RouteSimplifier for Altitude Preservation (Medium Risk)

#### 3.1 Ensure RouteSimplifier Preserves Complete Coordinate Objects
**File**: `RunApp/Utils/RouteSimplifier.swift`

**Critical Implementation Note**: The Douglas-Peucker algorithm must return the original `Coordinate` objects from the input array, not reconstruct new ones. This ensures that altitude, timestamp, speed, and other metadata are preserved exactly.

**Current Issue**: If RouteSimplifier creates new Coordinate objects with only lat/lng, altitude data will be lost.

**Required Fix**: Modify the algorithm to work with indices and return the original objects:

```swift
/// Main simplification method using Douglas-Peucker algorithm
/// CRITICAL: Returns original Coordinate objects to preserve altitude and metadata
static func simplify(coordinates: [Coordinate], tolerance: Double = 0.0001) -> [Coordinate] {
    guard coordinates.count > 2 else { return coordinates }
    
    // Convert to DPPoints with original indices
    let dpPoints = coordinatesToDPPoints(coordinates)
    let simplifiedIndices = chooseSimplificationMethod(points: dpPoints, tolerance: tolerance)
    
    // ✅ CRITICAL: Map back using original indices to preserve all metadata
    return simplifiedIndices.map { coordinates[$0] }
}
```

This ensures that the simplified route maintains the correct altitude profile and all other coordinate metadata.

### Phase 4: Create Enhanced ActivityDetailView with Tabbed Interface (Medium Risk)

#### 4.1 Update ActivityDetailViewModel for Tabbed Interface
**File**: `RunApp/Views/ActivityDetailViewModel.swift`

```swift
enum ActivityDetailTab: String, CaseIterable {
    case map = "Map"
    case stats = "Stats"
    
    var systemImage: String {
        switch self {
        case .map: return "map"
        case .stats: return "chart.bar.xaxis"
        }
    }
}

@MainActor
final class ActivityDetailViewModel: ObservableObject {
    // Input
    private let activity: RunActivity
    
    // Output: Published properties to drive the UI
    @Published var isLoading = true
    @Published var selectedTab: ActivityDetailTab = .map
    @Published var mapRegion: MKCoordinateRegion?
    @Published var locationName = ""
    @Published var deletionErrorMessage = ""
    @Published var showDeletionError = false
    @Published var isDeleting = false
    
    init(activity: RunActivity) {
        self.activity = activity
    }
    
    func loadActivityData() async {
        // Ensure this runs only once
        guard isLoading else { return }
        
        // ✅ Use pre-simplified route data directly
        let activityRoutes = activity.coordinates // Already simplified
        
        // Perform minimal work in a background task
        let processedData = await Task.detached(priority: .userInitiated) { () -> (MKCoordinateRegion?, String) in
            // 1. Calculate Region (from simplified route - sufficient accuracy)
            let region = await self.calculateRouteRegion(routes: activityRoutes)
            
            // 2. ✅ Skip speed analysis - use pre-calculated segments
            
            // 3. Reverse Geocode Location (only if not already stored)
            let name = await self.fetchLocationName(routes: activityRoutes)
            
            return (region, name)
        }.value
        
        // Update published properties on the main thread
        self.mapRegion = processedData.0
        self.locationName = processedData.1
        self.isLoading = false
    }
    
    // MARK: - Optimized Stats Access (No Computation)
    
    var distance: Double { activity.distance }
    var calories: Double { activity.totalCalories }
    var activeCalories: Double { activity.activeCalories }
    var pauseCalories: Double { activity.pauseCalories }
    var duration: TimeInterval { activity.activeTime }
    var totalTime: TimeInterval { activity.totalTime }
    var pauseTime: TimeInterval { activity.pauseTime }
    var averagePace: Double { activity.averagePace }
    var bestPace: Double { activity.bestPace }
    var averageSpeed: Double { activity.averageSpeed }
    var maxSpeed: Double { activity.maxSpeed }
    var route: [Coordinate] { activity.coordinates }
    var speedSegments: [SpeedSegment] { activity.speedSegments }
    var metricLaps: [LapData] { activity.metricLaps }
    var imperialLaps: [LapData] { activity.imperialLaps }
    var workoutStats: WorkoutStats { activity.workoutStats }
    
    // Rest of existing methods...
}
```

#### 4.2 Create StatsView Component
**File**: `RunApp/Views/StatsView.swift`

```swift
import SwiftUI

struct StatsView: View {
    let viewModel: ActivityDetailViewModel
    @State private var selectedLapUnit: LapUnit = .metric
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Summary Stats Card
                SummaryStatsCard(viewModel: viewModel)
                
                // Pace & Speed Analytics Card
                PaceSpeedAnalyticsCard(viewModel: viewModel)
                
                // Time Breakdown Card
                TimeBreakdownCard(viewModel: viewModel)
                
                // Calorie Breakdown Card
                CalorieBreakdownCard(viewModel: viewModel)
                
                // Laps Section
                LapsSection(viewModel: viewModel, selectedUnit: $selectedLapUnit)
            }
            .padding()
        }
        .navigationBarHidden(true)
    }
}

struct SummaryStatsCard: View {
    let viewModel: ActivityDetailViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Summary")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatItem(title: "Distance", value: String(format: "%.2f km", viewModel.distance / 1000))
                StatItem(title: "Total Time", value: formatTime(viewModel.totalTime))
                StatItem(title: "Active Time", value: formatTime(viewModel.duration))
                StatItem(title: "Total Calories", value: String(format: "%.0f cal", viewModel.calories))
            }
        }
        .cardStyle()
    }
}

struct PaceSpeedAnalyticsCard: View {
    let viewModel: ActivityDetailViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Pace & Speed")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatItem(title: "Avg Pace", value: formatPace(viewModel.averagePace))
                StatItem(title: "Best Pace", value: formatPace(viewModel.bestPace))
                StatItem(title: "Avg Speed", value: String(format: "%.1f km/h", viewModel.averageSpeed * 3.6))
                StatItem(title: "Max Speed", value: String(format: "%.1f km/h", viewModel.maxSpeed * 3.6))
            }
        }
        .cardStyle()
    }
}

struct TimeBreakdownCard: View {
    let viewModel: ActivityDetailViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Time Breakdown")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
                StatItem(title: "Total", value: formatTime(viewModel.totalTime))
                StatItem(title: "Active", value: formatTime(viewModel.duration))
                StatItem(title: "Paused", value: formatTime(viewModel.pauseTime))
            }
        }
        .cardStyle()
    }
}

struct CalorieBreakdownCard: View {
    let viewModel: ActivityDetailViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Calories")
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
                StatItem(title: "Total", value: String(format: "%.0f", viewModel.calories))
                StatItem(title: "Active", value: String(format: "%.0f", viewModel.activeCalories))
                StatItem(title: "Rest", value: String(format: "%.0f", viewModel.pauseCalories))
            }
        }
        .cardStyle()
    }
}

struct LapsSection: View {
    let viewModel: ActivityDetailViewModel
    @Binding var selectedUnit: LapUnit
    
    var laps: [LapData] {
        selectedUnit == .metric ? viewModel.metricLaps : viewModel.imperialLaps
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Laps")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Picker("Unit", selection: $selectedUnit) {
                    ForEach(LapUnit.allCases, id: \.self) { unit in
                        Text(unit.rawValue).tag(unit)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 100)
            }
            
            if laps.isEmpty {
                Text("No laps completed")
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                ForEach(laps.indices, id: \.self) { index in
                    LapRow(lap: laps[index])
                }
            }
        }
        .cardStyle()
    }
}

struct LapRow: View {
    let lap: LapData
    
    var body: some View {
        HStack {
            Text("Lap \(lap.lapNumber)")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(formatTime(lap.time))
                    .font(.subheadline)
                Text(formatPace(lap.averagePace))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

struct StatItem: View {
    let title: String
    let value: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            Text(value)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
        }
    }
}

// Helper Extensions
extension View {
    func cardStyle() -> some View {
        self
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
    }
}

func formatTime(_ time: TimeInterval) -> String {
    let hours = Int(time) / 3600
    let minutes = (Int(time) % 3600) / 60
    let seconds = Int(time) % 60
    
    if hours > 0 {
        return String(format: "%d:%02d:%02d", hours, minutes, seconds)
    } else {
        return String(format: "%d:%02d", minutes, seconds)
    }
}

func formatPace(_ pace: Double) -> String {
    guard pace > 0 && pace < 60 else { return "--:--" }
    let minutes = Int(pace)
    let seconds = Int((pace - Double(minutes)) * 60)
    return String(format: "%d:%02d", minutes, seconds)
}
```

#### 4.3 Update ActivityDetailView with Tabbed Interface
**File**: `RunApp/Views/ActivityDetailView.swift`

```swift
// Add segmented control at the top
struct ActivityDetailView: View {
    let activity: RunActivity
    @StateObject private var viewModel: ActivityDetailViewModel
    
    init(activity: RunActivity) {
        self.activity = activity
        self._viewModel = StateObject(wrappedValue: ActivityDetailViewModel(activity: activity))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // ✅ Segmented Control at the top
            Picker("View", selection: $viewModel.selectedTab) {
                ForEach(ActivityDetailTab.allCases, id: \.self) { tab in
                    Label(tab.rawValue, systemImage: tab.systemImage)
                        .tag(tab)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding()
            
            // Content based on selected tab
            TabView(selection: $viewModel.selectedTab) {
                // Map View (existing)
                MapViewContent(viewModel: viewModel)
                    .tag(ActivityDetailTab.map)
                
                // ✅ New Stats View
                StatsView(viewModel: viewModel)
                    .tag(ActivityDetailTab.stats)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        }
        .task {
            await viewModel.loadActivityData()
        }
        .onDisappear {
            viewModel.onDisappear()
        }
        // ... rest of existing code
    }
}

struct MapViewContent: View {
    @ObservedObject var viewModel: ActivityDetailViewModel
    
    var body: some View {
        // Existing map view content
        // ... existing map implementation
    }
}
```

### Phase 5: Remove Legacy Display Simplification (Low Risk)

#### 5.1 Update RunActivity Display Methods
**File**: `RunApp/Models/RunActivity.swift`

**Remove these methods** (data is already simplified):
- `getSimplifiedRoute()` (line 269)
- `getRouteForDisplay()` (line 289)
- `getRouteWithPointLimit()` (line 295)
- Simplification cache system (lines 23-25)

**Replace with**:
```swift
// Simple accessor since data is already optimized
var route: [Coordinate] {
    coordinates // Already simplified
}

func getRouteForDisplay(context: SimplificationContext) -> [Coordinate] {
    coordinates // Already optimized for display
}

func getRouteWithPointLimit(maxPoints: Int) -> [Coordinate] {
    coordinates // Already within reasonable limits due to simplification
}
```

#### 5.2 Update Display Views
**Files**: 
- `RunApp/Views/ActivityRowView.swift`

**Before**:
```swift
// Views currently call expensive simplification
let displayRoute = activity.getRouteForDisplay(context: .activityList)
```

**After**:
```swift
// Direct access to pre-simplified data
let displayRoute = activity.coordinates // Already optimized
```

### Phase 6: Backward Compatibility with Hardened Edge Case Handling (Low Risk)

#### 6.1 Add Robust Migration Support
**File**: `RunApp/Models/RunActivity.swift`

```swift
// Support for legacy activities without pre-calculated stats
var isLegacyData: Bool {
    storedDistance == nil || storedCalories == nil || storedSpeedSegments == nil || storedWorkoutStats == nil
}

var distance: Double {
    if let storedDistance = storedDistance {
        return storedDistance // New optimized path
    }
    // Legacy fallback with edge case protection
    return calculateDistanceWithFallback() 
}

var totalCalories: Double {
    if let storedWorkoutStats = storedWorkoutStats {
        return storedWorkoutStats.totalCalories // New optimized path
    }
    // Legacy fallback
    return storedCalories ?? calculateCaloriesWithFallback()
}

var workoutStats: WorkoutStats {
    if let stored = storedWorkoutStats {
        return stored // New optimized path
    }
    // Legacy fallback - calculate on demand
    return calculateWorkoutStatsLegacy()
}

/// Legacy workout stats calculation for backward compatibility
private func calculateWorkoutStatsLegacy() -> WorkoutStats {
    return WorkoutStats(
        averagePace: storedPace ?? 0,
        bestPace: 0, // Not available in legacy data
        averageSpeed: storedPace > 0 ? 1000.0 / (storedPace * 60.0) : 0,
        maxSpeed: 0, // Not available in legacy data
        totalTime: endTime.timeIntervalSince(startTime),
        activeTime: activeRunningTime,
        pauseTime: endTime.timeIntervalSince(startTime) - activeRunningTime,
        activeCalories: storedCalories ?? calculateCaloriesWithFallback(),
        pauseCalories: 0, // Not available in legacy data
        totalCalories: storedCalories ?? calculateCaloriesWithFallback(),
        metricLaps: [], // Not available in legacy data
        imperialLaps: [] // Not available in legacy data
    )
}

/// Hardened legacy distance calculation that handles edge cases
private func calculateDistanceWithFallback() -> Double {
    // Edge case protection for legacy data
    guard coordinates.count > 1 else { 
        print("Legacy activity has insufficient coordinates for distance calculation")
        return 0.0 
    }
    
    // Check for degenerate case where all points are identical
    let firstCoord = coordinates[0]
    let allIdentical = coordinates.allSatisfy { 
        abs($0.latitude - firstCoord.latitude) < 0.000001 &&
        abs($0.longitude - firstCoord.longitude) < 0.000001
    }
    
    if allIdentical {
        print("Legacy activity has all identical coordinates")
        return 0.0
    }
    
    // Use the existing calculation method
    return calculateDistance()
}

/// Hardened legacy calorie calculation that handles edge cases  
private func calculateCaloriesWithFallback() -> Double {
    // Ensure we have valid weight and time data
    guard weight > 0, activeRunningTime > 0 else {
        print("Legacy activity has invalid weight or time data")
        return 0.0
    }
    
    // Use the existing calculation method
    return calculateCalories()
}
```

## Adaptive Tolerance Strategy

### RouteSimplifier.adaptiveTolerance Implementation Details

The adaptive tolerance system ensures optimal simplification based on workout characteristics:

```swift
/// Adaptive tolerance based on display context and spatial extent
static func adaptiveTolerance(for context: SimplificationContext, coordinates: [Coordinate]? = nil) -> Double {
    let baseTolerance: Double
    
    switch context {
    case .activityList:
        baseTolerance = 0.001      // Aggressive: ≈111m (for small preview maps)
    case .activityDetail:
        baseTolerance = 0.00003    // Moderate: ≈3.3m (for full-screen maps)
    case .realTimeView:
        baseTolerance = 0.00001    // Minimal: ≈1m (for active tracking)
    case .backgroundProcessing:
        baseTolerance = 0.0005     // Heavy: ≈55m (for background pre-processing)
    }
    
    // Optional: Adjust tolerance based on spatial extent of the workout
    if let coords = coordinates, coords.count > 2 {
        let latitudes = coords.map { $0.latitude }
        let longitudes = coords.map { $0.longitude }
        
        let latSpan = (latitudes.max() ?? 0) - (latitudes.min() ?? 0)
        let lonSpan = (longitudes.max() ?? 0) - (longitudes.min() ?? 0)
        let spatialExtent = max(latSpan, lonSpan)
        
        // For larger routes, allow slightly more aggressive simplification
        if spatialExtent > 0.01 { // Large route (>1km span)
            return baseTolerance * 1.5
        } else if spatialExtent < 0.001 { // Small route (<100m span)
            return baseTolerance * 0.5
        }
    }
    
    return baseTolerance
}
```

**Principle**: The adaptive tolerance ensures that longer, larger routes are simplified appropriately without losing detail on shorter routes. The tolerance is calculated based on the spatial extent (bounding box) of the workout, ensuring optimal balance between storage efficiency and visual fidelity.

## Performance Benefits

### Storage Optimization
- **Before**: 2000-5000 GPS points per workout
- **After**: 200-500 simplified points per workout
- **Reduction**: 80-90% smaller SwiftData files

### Display Performance
- **Before**: RouteSimplifier + SpeedAnalyzer + Stats calculation on every view load
- **After**: Zero processing overhead for stored activities
- **Improvement**: 20x faster activity detail loading

### Memory Usage
- **Before**: Large coordinate arrays + repeated calculations in memory
- **After**: Optimized coordinate arrays + pre-calculated data structures
- **Improvement**: Significantly reduced memory pressure

### Processing Overhead
- **Before**: Repeated simplification + speed analysis + stats calculation
- **After**: One-time comprehensive calculation during save
- **Improvement**: Eliminates all redundant CPU usage

### ActivityDetailView Specific Benefits
- **Before**: Heavy background processing + limited stats display
- **After**: Instant loading + comprehensive analytics dashboard
- **Stats Access**: Direct property access (no calculations)
- **Route Display**: Pre-simplified coordinates (no processing)
- **Speed Segments**: Pre-calculated segments (no analysis)
- **Comprehensive Analytics**: Pace, speed, lap, and calorie breakdowns
- **Enhanced UX**: Tabbed interface for map vs stats

## Implementation Timeline

### Week 1: Foundation (Low Risk)
1. Update Coordinate struct to include altitude
2. Add WorkoutStats and LapData structures
3. Add enhanced data storage fields to RunActivity
4. Add static helper methods for comprehensive stats calculation
5. Test new data structures with sample data

### Week 2: Enhanced Statistics (Medium Risk)
1. Implement comprehensive stats calculation methods
2. Add new RunActivity initialization method
3. Test stats calculation accuracy
4. Validate lap calculation algorithms

### Week 3: Save Workflow & UI (Medium Risk)
1. Update ContentView.saveActivity() to use new workflow
2. Update RouteSimplifier for altitude preservation
3. Create StatsView component
4. Update ActivityDetailView with tabbed interface
5. Test complete save-to-display workflow

### Week 4: Optimization & Compatibility (Low Risk)
1. Update ActivityDetailViewModel for optimized loading
2. Remove legacy simplification methods
3. Add hardened backward compatibility layer
4. Test edge case handling and legacy data support

### Week 5: Validation & Polish (Low Risk)
1. Performance testing and validation
2. Memory usage analysis
3. Data accuracy verification
4. UI/UX refinements
5. Legacy data migration testing

## Risk Assessment

### Low Risk Changes
- Adding altitude field to Coordinate struct
- Adding new data storage fields
- Static helper methods for calculation (additive)
- Creating new UI components
- Removing unused simplification methods

### Medium Risk Changes
- Modifying save workflow in ContentView
- RouteSimplifier altitude handling
- New RunActivity initialization method
- ActivityDetailViewModel optimization
- Comprehensive stats calculation algorithms

### Mitigation Strategies
- Backward compatibility ensures existing data continues to work
- Static methods eliminate side effects in initialization
- Gradual rollout allows for testing at each phase
- Comprehensive edge case handling for legacy data
- Performance monitoring to validate improvements
- Feature flags for new UI components
- Rollback plan for any breaking changes

## Testing Strategy

### Unit Tests
- Test comprehensive stats calculation with various workout scenarios
- Validate lap calculation for metric and imperial units
- Test new RunActivity initialization with edge cases
- Validate altitude preservation in simplification
- Test backward compatibility with legacy data
- Test calorie breakdown calculations

### Integration Tests
- Test complete save workflow from workout to display
- Validate performance improvements in both tabs
- Test ActivityDetailView tabbed interface
- Test memory usage under various conditions
- Test adaptive tolerance calculation

### Performance Tests
- Measure storage size reduction
- Benchmark display loading times (both map and stats tabs)
- Monitor memory usage patterns
- Validate accuracy of all calculated metrics
- Compare pre/post ActivityDetailView load times

### UI/UX Tests
- Test segmented control functionality
- Validate stats display formatting
- Test lap unit switching
- Verify responsive design across devices
- Test accessibility features

### Edge Case Tests
- Workouts with 0, 1, or 2 coordinates
- Workouts with all identical coordinates
- Very short workouts (no complete laps)
- Legacy activities with missing data fields
- Activities with invalid weight/time data
- Very small routes vs very large routes

## Success Metrics

### Performance Targets
- 80%+ reduction in SwiftData file sizes
- 10x improvement in activity list loading
- 20x improvement in ActivityDetailView loading
- 50%+ reduction in memory usage during display
- Zero data accuracy loss for all metrics
- Zero crashes on legacy data

### Feature Completeness
- ✅ Comprehensive pace analytics (average, best)
- ✅ Complete speed analytics (average, max)
- ✅ Detailed time breakdown (total, active, pause)
- ✅ Calorie breakdown (active, rest, total)
- ✅ Lap tracking (metric and imperial)
- ✅ Tabbed interface (map and stats)

### Quality Assurance
- All existing functionality continues to work
- New activities display comprehensive analytics instantly
- Legacy activities show available data with graceful fallbacks
- No crashes or data corruption
- Altitude data properly preserved
- Intuitive user interface for stats viewing

## Conclusion

This comprehensive implementation plan transforms the workout tracking app from a basic GPS recorder into a full-featured fitness analytics platform. By pre-calculating comprehensive statistics during the save process and providing a rich stats interface, users gain deep insights into their performance while enjoying optimal app performance.

The key innovations include:

1. **Comprehensive Analytics**: Detailed pace, speed, time, calorie, and lap analysis
2. **Storage Optimization**: 80-90% reduction in data size through intelligent simplification
3. **Performance Excellence**: 20x faster loading through pre-calculation
4. **Enhanced UX**: Tabbed interface separating map visualization from detailed analytics
5. **Data Integrity**: Altitude preservation and robust backward compatibility

The phased approach ensures minimal risk while delivering maximum value, transforming both the technical architecture and user experience of the fitness tracking application.

### Senior Review Recommendations Addressed

1. **Side Effect Elimination**: Static helper methods prevent temporary state mutations during initialization
2. **Altitude Preservation**: Explicit requirement for RouteSimplifier to preserve complete Coordinate objects
3. **Hardened Legacy Support**: Edge case protection for legacy data with graceful degradation
4. **Adaptive Tolerance Clarity**: Detailed explanation of spatial extent-based tolerance adjustment
5. **Comprehensive Testing**: Extended test strategy covering all new features and edge cases

### Enhanced Features Delivered

1. **Pre-calculated Comprehensive Stats**: All metrics calculated once during save
2. **Tabbed ActivityDetailView**: Separate map and comprehensive stats views
3. **Lap Tracking**: Both metric (1km) and imperial (1mi) lap analysis
4. **Enhanced Analytics**: Pace variations, speed analytics, calorie breakdowns
5. **Instant Performance**: Zero computation overhead for viewing past workouts