# Date Display Consistency Fix

## Problem Description

There is a discrepancy in how workout dates are displayed across the app:

- **Charts (AnalysisView)**: Use `activity.startTime` for date-based grouping and filtering
- **ActivityRowView**: Uses `activity.formattedDate` which incorrectly uses `endTime`
- **timeOfDay property**: Correctly uses `startTime` for determining morning/afternoon/evening/night

This inconsistency causes the same workout to appear on different dates in charts vs activity lists.

## Root Cause Analysis

In `RunActivity.swift`, the `formattedDate` computed property uses `endTime`:

```swift
var formattedDate: String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .none
    formatter.locale = Locale.current
    return formatter.string(from: endTime)  // ❌ INCORRECT: Uses endTime
}
```

While charts consistently use `startTime`:

```swift
// In AnalysisView.swift - multiple locations
let activityDate = activity.startTime  // ✅ CORRECT: Uses startTime
```

## Solution

**Change the `formattedDate` property to use `startTime` instead of `endTime`.**

### Why startTime is correct:
1. **Logical consistency**: A workout's "date" should be when it started
2. **Chart alignment**: Charts already group activities by start date
3. **User expectation**: Users expect to see workouts listed by when they began
4. **Temporal accuracy**: Start time better represents when the activity occurred

## Implementation Plan

### Step 1: Fix the formattedDate property
**File**: `RunApp/Models/RunActivity.swift`
**Location**: Line 248-253

**Change from**:
```swift
var formattedDate: String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .none
    formatter.locale = Locale.current
    return formatter.string(from: endTime)
}
```

**Change to**:
```swift
var formattedDate: String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .none
    formatter.locale = Locale.current
    return formatter.string(from: startTime)
}
```

### Step 2: Verify consistency across app
- Charts: ✅ Already use `startTime` correctly
- ActivityRowView: ✅ Will now use `startTime` via `formattedDate`
- timeOfDay: ✅ Already uses `startTime`
- Data export: ✅ Exports both start and end times separately

## Testing

1. Create a workout that spans midnight (starts before midnight, ends after)
2. Verify the workout appears on the same date in both:
   - Activity list (ActivityRowView)
   - Charts (AnalysisView)
3. Confirm the date matches the start time, not end time

## Impact Assessment

- **Breaking changes**: None
- **Data migration**: Not required (only changes UI display)
- **Performance impact**: None (same computation, different source)
- **User experience**: Improved consistency

## Corner Cases Handled

- **Midnight spanning workouts**: Will show on start date (correct behavior)
- **Multi-day workouts**: Will show on start date (logical choice)
- **Timezone changes**: Start time is more stable reference point

## YAGNI Compliance

This fix:
- ✅ Addresses a real user-facing inconsistency
- ✅ Uses minimal code change (1 line)
- ✅ Follows existing patterns (charts already use startTime)
- ✅ No over-engineering or unnecessary complexity 