# Speed-Visualized Route Display Implementation Plan

## 📋 Overview
Create a dynamic, speed-based color-coded route visualization in `ActivityRowView.swift` that replaces the current solid blue route line with a heatmap-style gradient representing speed variations throughout the running activity.

## 🎯 Key Features
1. **Heatmap Color Scheme**: Blue (slowest) → Green → Yellow → Orange → Red (fastest)
2. **Dynamic Speed Ranges**: Color mapping based on actual min/max speeds in each activity
3. **Speed Legend**: Colorbar with speed labels in top-right corner
4. **Unit System Support**: Speed display in km/h (metric) or mph (imperial)
5. **Backward Compatibility**: Fallback to blue line for activities without speed data

## 🏗️ Implementation Architecture

```
ActivityRowView
├── ActivityMap Component
│   ├── Speed Analysis Engine
│   │   ├── Extract Valid Speeds
│   │   ├── Calculate Min/Max/Ranges
│   │   └── Handle Missing Data
│   ├── Color Mapping System
│   │   ├── Heatmap Color Generator
│   │   ├── Segment Color Assignment
│   │   └── Gradient Interpolation
│   └── Speed Legend Component
│       ├── Colorbar Visualization
│       ├── Speed Labels
│       └── Unit Conversion
└── Coordinate Model (Speed Data: Double?)
```

## 📝 Detailed Implementation Plan

### Phase 1: Core Speed Analysis Engine
Create utility functions to process speed data from `Coordinate` model:

**New Components to Add:**
1. **`SpeedAnalyzer`** - Extract and analyze speed data
2. **`SpeedColorMapper`** - Convert speeds to colors  
3. **`SpeedLegend`** - Display speed colorbar

**Key Functions:**
```swift
// Speed analysis
func analyzeRouteSpeed(_ coordinates: [Coordinate]) -> SpeedAnalysis
func filterValidSpeeds(_ coordinates: [Coordinate]) -> [Double]
func calculateSpeedRanges(speeds: [Double]) -> (min: Double, max: Double, quartiles: [Double])

// Color mapping  
func speedToColor(_ speed: Double, speedRange: SpeedRange) -> Color
func createHeatmapGradient() -> [Color]
func interpolateColor(between: Color, and: Color, factor: Double) -> Color

// Unit conversion
func convertSpeed(_ metersPerSecond: Double, to unitSystem: UnitSystem) -> Double
func formatSpeed(_ speed: Double, unit: UnitSystem) -> String
```

### Phase 2: Route Visualization Enhancement
Replace current `MapPolyline` implementation:

**Current Code (Line 184-187):**
```swift
MapPolyline(coordinates: [start.clCoordinate, end.clCoordinate])
    .stroke(start.isPaused ? .blue.opacity(0.2) : .blue,
            style: StrokeStyle(lineWidth: 6))
```

**New Implementation:**
```swift
// Speed-based colored segments
ForEach(speedSegments.indices, id: \.self) { index in
    let segment = speedSegments[index]
    MapPolyline(coordinates: segment.coordinates)
        .stroke(segment.color, style: StrokeStyle(lineWidth: 8, lineCap: .round))
}
```

### Phase 3: Speed Legend Component
Add overlay component in top-right corner:

**SpeedLegend Structure:**
```swift
struct SpeedLegend: View {
    let speedRange: SpeedRange
    let unitSystem: UnitSystem
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            // Color gradient bar
            HStack(spacing: 0) {
                ForEach(heatmapColors.indices, id: \.self) { index in
                    Rectangle()
                        .fill(heatmapColors[index])
                        .frame(width: 4, height: 60)
                }
            }
            
            // Speed labels
            VStack(alignment: .trailing, spacing: 2) {
                Text(formatSpeed(speedRange.max, unit: unitSystem))
                Text(formatSpeed(speedRange.median, unit: unitSystem))
                Text(formatSpeed(speedRange.min, unit: unitSystem))
            }
        }
    }
}
```

## 🎨 Color Scheme Implementation

**Heatmap Colors (Blue → Red):**
```swift
let heatmapColors: [Color] = [
    Color.blue,      // Slowest (0%)
    Color.cyan,      // 25%
    Color.green,     // 50% 
    Color.yellow,    // 75%
    Color.orange,    // 90%
    Color.red        // Fastest (100%)
]
```

**Dynamic Range Mapping:**
- **Min Speed** → Blue
- **25th Percentile** → Cyan  
- **50th Percentile** → Green
- **75th Percentile** → Yellow
- **90th Percentile** → Orange
- **Max Speed** → Red

## 📊 Data Processing Pipeline

```
Raw Coordinates → Filter Valid Speeds → Remove Outliers >50 km/h → 
Calculate Speed Statistics → Create Speed Ranges → Map Coordinates to Colors → 
Generate Route Segments → Render Colored Route
```

## 🔧 Technical Specifications

### Speed Data Processing:
- **Input**: `Coordinate.speed: Double?` (meters/second)
- **Filter Criteria**: 
  - Remove `nil` values
  - Filter speeds > 13.89 m/s (50 km/h) as GPS errors
  - Minimum 2 valid speed points required
- **Fallback**: Solid blue line if insufficient speed data

### Unit System Support:
- **Metric**: km/h display using `UnitSystem.metric`
- **Imperial**: mph display using `UnitSystem.imperial`
- **Conversion**: m/s → km/h (* 3.6) or m/s → mph (* 2.237)

### Performance Considerations:
- **Segment Optimization**: Combine consecutive coordinates with similar speeds
- **Color Caching**: Pre-calculate color gradients
- **Memory Management**: Limit segments to prevent map performance issues

## 📍 File Modifications Required

1. **`ActivityRowView.swift`**:
   - Replace solid blue route with speed-colored segments  
   - Add SpeedLegend overlay component
   - Integrate speed analysis logic

2. **Create New Files**:
   - `SpeedAnalyzer.swift` - Speed processing utilities
   - `SpeedColorMapper.swift` - Color mapping logic  
   - `SpeedLegend.swift` - Legend component

3. **`UserProfile.swift`** (if needed):
   - Add speed unit conversion extensions

## 🎯 Success Criteria
- ✅ Route displays speed-based colors (blue to red gradient)
- ✅ Speed legend shows in top-right with proper units
- ✅ Smooth color transitions between speed segments
- ✅ Backward compatibility with activities lacking speed data
- ✅ Performance maintains smooth map interaction
- ✅ Supports both metric (km/h) and imperial (mph) units

## 🔄 Implementation Order
1. **Create speed analysis utilities** (SpeedAnalyzer)
2. **Implement color mapping system** (SpeedColorMapper)  
3. **Build speed legend component** (SpeedLegend)
4. **Integrate into ActivityRowView** (replace current route rendering)
5. **Add unit system support and formatting**
6. **Performance optimization and testing**

## 📅 Implementation Status
- [x] Phase 1: Speed Analysis Engine
- [x] Phase 2: Color Mapping System
- [x] Phase 3: Speed Legend Component
- [x] Phase 4: ActivityRowView Integration
- [x] Phase 5: Unit System Support
- [ ] Phase 6: Performance Optimization

## 🎨 Visual Design
The implementation will transform the basic blue route line into a rich, informative visualization that immediately shows users where they ran fastest and slowest, providing valuable insights into their running performance patterns.

**Color Progression Example:**
- Walking/Slow: Blue (0-5 km/h)
- Jogging: Cyan to Green (5-10 km/h)
- Running: Green to Yellow (10-15 km/h)
- Fast Running: Yellow to Orange (15-20 km/h)
- Sprint: Orange to Red (20+ km/h)

*Note: Actual color thresholds will be dynamically calculated based on each activity's specific speed range for optimal visualization.*