Implementation Plan: Optimizing Activity Detail View Performance
1. Summary of the Problem

The ActivityDetailView currently experiences significant performance issues, causing the UI to freeze when a user selects a workout. The root cause is that computationally expensive operations—calculating the map region, simplifying the GPS route, and analyzing speed data—are performed synchronously on the main thread during the view's rendering process.

2. High-Level Goal

The goal is to refactor the ActivityDetailView to perform all heavy data processing asynchronously on a background thread. The UI should remain responsive, displaying a loading indicator to the user while the data is prepared, and then seamlessly update once the data is available.

3. Architectural Approach: Model-View-ViewModel (MVVM)

We will introduce a dedicated ViewModel for the ActivityDetailView. This ActivityDetailViewModel will be responsible for orchestrating data loading and processing, separating this logic from the view and improving the overall architecture.

4. Data Flow Diagrams
graph TD
    A[User Taps Activity] --> B{Construct ActivityDetailView};
    B --> C{ActivityMap.body};
    C --> D[1. Calculate Bounding Region (All Points)];
    C --> E[2. Simplify Route (CPU-Intensive)];
    C --> F[3. Analyze Route Speed (CPU-Intensive)];
    F --> G[4. Render Map with Colored Polyline];
    G --> H[View Appears (Delayed)];

    subgraph "Main Thread (UI Blocks)"
        direction LR
        D; E; F; G;
    end

graph TD
    A[User Taps Activity] --> B{Construct ActivityDetailView};
    B --> C{Display Loading Indicator};
    B -- .task {starts background process} --> D(Process Data Asynchronously);
    
    subgraph "Background Thread"
      direction TB
      D --> E[1. Calculate Bounding Region];
      E --> F[2. Simplify Route];
      F --> G[3. Analyze Route Speed];
    end

    G -- Update UI on Main Thread --> H{Store Processed Data in @State};
    H --> I{Re-render View with Data};
    I --> J[Render Map with Colored Polyline];
    J --> K[View is Fully Interactive];
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Mermaid
IGNORE_WHEN_COPYING_END
5. Step-by-Step Implementation Plan

Create a new Swift file for the ViewModel. This class will be an ObservableObject that manages the state and logic for the detail view.

File: RunApp/Views/ActivityDetailViewModel.swift (New File)

Purpose: To offload data processing from the view and manage the view's state.

import SwiftUI
import MapKit
import Combine

@MainActor
final class ActivityDetailViewModel: ObservableObject {
    // Input
    private let activity: RunActivity

    // Output: Published properties to drive the UI
    @Published var isLoading = true
    @Published var mapRegion: MKCoordinateRegion?
    @Published var speedSegments: [SpeedSegment] = []
    @Published var locationName = ""
    @Published var deletionErrorMessage = ""
    @Published var showDeletionError = false
    @Published var isDeleting = false

    init(activity: RunActivity) {
        self.activity = activity
    }

    func loadActivityData() async {
        // Ensure this runs only once
        guard isLoading else { return }

        // Perform all heavy work in a background task
        let processedData = await Task.detached(priority: .userInitiated) { () -> (MKCoordinateRegion?, [SpeedSegment], String) in
            // 1. Calculate Region (from original route for accuracy)
            let region = self.calculateRouteRegion()

            // 2. Simplify Route and Analyze Speed
            let displayRoute = self.activity.getRouteForDisplay(context: .activityDetail)
            let speedAnalysis = SpeedAnalyzer.analyzeRouteSpeed(displayRoute)
            let segments = SpeedAnalyzer.createSpeedSegments(from: displayRoute, using: speedAnalysis)

            // 3. Reverse Geocode Location
            let name = await self.fetchLocationName()

            return (region, segments, name)
        }.value

        // Update published properties on the main thread
        self.mapRegion = processedData.0
        self.speedSegments = processedData.1
        self.locationName = processedData.2
        self.isLoading = false
    }
    
    // Move deletion logic here
    func deleteActivity(modelContext: ModelContext, dismiss: @escaping () -> Void) async {
        isDeleting = true
        do {
            let container = modelContext.container
            let deletionActor = ActivityDeletionActor(modelContainer: container)
            try await deletionActor.deleteActivity(activityId: activity.id)
            await MainActor.run {
                dismiss()
            }
        } catch {
            await MainActor.run {
                isDeleting = false
                deletionErrorMessage = "Failed to delete activity: \(error.localizedDescription)"
                showDeletionError = true
            }
        }
    }

    // --- Private Helper Functions (Moved from View) ---

    private func calculateRouteRegion() -> MKCoordinateRegion? {
        guard !activity.route.isEmpty else { return nil }
        // ... (existing logic from RunActivity extension) ...
        let coordinates = activity.route.map { $0.clCoordinate }
        var minLat = coordinates[0].latitude, maxLat = minLat, minLon = coordinates[0].longitude, maxLon = minLon
        for coord in coordinates {
            minLat = min(minLat, coord.latitude); maxLat = max(maxLat, coord.latitude)
            minLon = min(minLon, coord.longitude); maxLon = max(maxLon, coord.longitude)
        }
        let center = CLLocationCoordinate2D(latitude: (minLat + maxLat) / 2, longitude: (minLon + maxLon) / 2)
        let span = MKCoordinateSpan(latitudeDelta: (maxLat - minLat) * 1.15, longitudeDelta: (maxLon - minLon) * 1.15)
        return MKCoordinateRegion(center: center, span: span)
    }

    private func fetchLocationName() async -> String {
        guard let firstCoordinate = activity.route.first else { return "Unknown Location" }
        let location = CLLocation(latitude: firstCoordinate.latitude, longitude: firstCoordinate.longitude)
        let geocoder = CLGeocoder()
        do {
            let placemarks = try await geocoder.reverseGeocodeLocation(location)
            // ... (existing logic to format placemark) ...
            return placemarks.first?.locality ?? "Location Unavailable"
        } catch {
            return "Location Unavailable"
        }
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Swift
IGNORE_WHEN_COPYING_END

Modify the views to be driven by the new ViewModel.

File: RunApp/Views/ActivityRowView.swift

Purpose: To connect the view to the ViewModel, add a loading state, and simplify its responsibility to only rendering data.

// In RunApp/Views/ActivityRowView.swift

// MARK: - ActivityDetailView
struct ActivityDetailView: View {
    @StateObject private var viewModel: ActivityDetailViewModel
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @State private var showDeleteConfirmation = false
    
    // The map position is now managed by the view, but initialized from the ViewModel
    @State private var mapPosition: MapCameraPosition

    init(activity: RunActivity) {
        _viewModel = StateObject(wrappedValue: ActivityDetailViewModel(activity: activity))
        // Set a default map position
        _mapPosition = State(initialValue: .automatic)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.isLoading {
                // --- LOADING STATE ---
                ProgressView("Loading Workout...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // --- LOADED STATE ---
                ActivityMap(
                    activity: viewModel.activity, // Pass the original activity
                    speedSegments: viewModel.speedSegments,
                    mapPosition: $mapPosition
                )
                
                StatsCard(
                    activity: viewModel.activity,
                    locationName: viewModel.locationName
                )
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .toolbar { /* ... existing toolbar ... */ }
        .confirmationDialog(...) { /* ... existing dialog ... */ }
        .alert("Deletion Error", isPresented: $viewModel.showDeletionError) { /* ... */ }
        .task {
            // --- TRIGGER ASYNC LOADING ---
            await viewModel.loadActivityData()
        }
        .onChange(of: viewModel.mapRegion) { _, newRegion in
            // Update map position once the region is calculated
            if let region = newRegion {
                mapPosition = .region(region)
            }
        }
    }
}

// MARK: - ActivityMap View
private struct ActivityMap: View {
    let activity: RunActivity // Keep for start/end markers
    let speedSegments: [SpeedSegment] // Receive processed data
    @Binding var mapPosition: MapCameraPosition
    @Query private var profile: [UserProfile]
    
    private var unitSystem: UnitSystem {
        profile.first?.units ?? .metric
    }
    
    var body: some View {
        // The view no longer calculates anything. It just renders.
        Map(position: $mapPosition) {
            if !speedSegments.isEmpty {
                ForEach(speedSegments.indices, id: \.self) { index in
                    let segment = speedSegments[index]
                    MapPolyline(coordinates: segment.coordinates)
                        .stroke(segment.color, style: StrokeStyle(lineWidth: 8, lineCap: .round, lineJoin: .miter))
                }
            }
            
            // Markers still use the original route for accuracy
            if let start = activity.route.first { /* ... */ }
            if let end = activity.route.last { /* ... */ }
        }
        // ... rest of the view remains the same ...
    }
}

6. Summary of Changes by File

RunApp/Views/ActivityDetailViewModel.swift (New)

Define the ActivityDetailViewModel class.

Add @Published properties for isLoading, mapRegion, speedSegments, etc.

Implement the loadActivityData() function to perform calculations on a background thread.

Move helper functions (calculateRouteRegion, fetchLocationName) and deletion logic into the ViewModel.

RunApp/Views/ActivityRowView.swift (Modified)

ActivityDetailView:

Instantiate ActivityDetailViewModel as a @StateObject.

Add a .task modifier to call viewModel.loadActivityData().

Implement a conditional view to show ProgressView when viewModel.isLoading is true.

Pass the processed data from the ViewModel down to ActivityMap and StatsCard.

Manage mapPosition locally and update it via .onChange(of: viewModel.mapRegion).

ActivityMap:

Remove all internal data processing logic.

Accept speedSegments and a binding to mapPosition as inputs.

Its role is now purely rendering.

StatsCard:

Accept locationName from the ViewModel instead of calculating it.

7. Expected Outcome

Immediate UI Response: The ActivityDetailView will appear instantly upon user tap.

Clear Loading Feedback: A ProgressView will inform the user that data is being loaded.

Smooth Transition: Once data processing is complete, the map and stats will appear without blocking or stuttering.

Improved Architecture: The separation of concerns between the View and ViewModel will make the code cleaner, more maintainable, and easier to test.