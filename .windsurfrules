<ios_development_guidelines>
<swiftui_architecture>

- Follow MVVM architecture consistently
- Keep views small and focused
- Extract reusable components into separate views
- Use SwiftUI previews for rapid development
- Implement modular view builders for complex layouts
- Keep business logic out of views
- Use ViewModifier for reusable styling
- Maintain clear view hierarchy
- Use Swift macros (@Observable, @Observation) for state management
- Implement ScrollView with new .scrollTargetBehavior() modifier
- Use @BackgroundTask for background processing
- Leverage @Environment(\.modelContext) for SwiftData context access
- Implement custom App Intents for Shortcuts integration
- Use Swift Charts for data visualization
- Implement ShareLink for sharing content
- Use PhotosPicker for media selection

</swiftui_architecture>

<swiftui_state_management>

- Use @State for simple view-local state
- Use @Observable macro for class-based state
- Apply @Bindable for property observation
- Use @Binding for child view state management
- Leverage @EnvironmentObject for deep dependency injection
- Keep state changes predictable and traceable
- Consider state restoration for app lifecycle
- Use @Query for SwiftData state management
- Implement @MainActor for UI updates
- Use .task modifier for async operations
- Use @ModelActor for SwiftData concurrency 

</swiftui_state_management>

<swiftdata_implementation>

- Design clear and concise model schemas
- Use @Model macro for entity definitions
- Implement proper data validation
- Handle migration paths explicitly
- Keep model contexts scoped appropriately
- Use preview data for development
- Implement proper error handling for data operations
- Use .modelContainer() for SwiftData setup
- Leverage batch operations for performance
- Implement custom merge policies
- Handle SwiftData validation errors
- Use @Query macro for fetching
- Implement proper relationship cascading
- Support offline persistence
- Use @Relationship for explicit relationship management 
- Avoid using Array for unordered relationships 

</swiftdata_implementation>

<performance_optimization>

- Minimize view updates using Equatable
- Use lazy loading for large lists
- Implement proper memory management
- Profile UI performance regularly
- Optimize image loading with AsyncImage
- Consider background fetch strategies
- Monitor SwiftData query performance
- Use @MainActor for UI updates
- Implement .task modifier for async operations
- Use batch updates for SwiftData operations
- Leverage system cache policies
- Optimize SwiftData relationship handling 

</performance_optimization>

<ios_specific_practices>

- Support dynamic type sizing
- Implement proper dark mode support
- Handle all device orientations
- Use SF Symbols when possible
- Follow Apple's Human Interface Guidelines
- Support accessibility features
- Handle app state transitions properly
- Support Stage Manager for iPad
- Implement Live Activities
- Use StoreKit 2 for in-app purchases
- Support Focus filters
- Handle screen edges and safe areas
- Implement proper keyboard handling
- Support Spotlight integration
- Use @Environment(\.openURL) for URL handling 

</ios_specific_practices>

<swiftui_navigation>

- Use NavigationStack with path binding
- Implement proper deep linking support
- Handle navigation stack memory management
- Use .navigationDestination for type-safe navigation
- Maintain navigation state properly
- Support universal links
- Handle back navigation gracefully
- Use .presentationDetents for sheets
- Implement custom sheet presentations
- Support multiple column navigation
- Handle navigation history
- Use .navigationTitle for dynamic titles 

</swiftui_navigation>

<data_flow_patterns>

- Implement proper offline support
- Use appropriate persistence strategies
- Handle data synchronization
- Implement proper caching mechanisms
- Handle network reachability
- Manage data conflicts resolution
- Support background updates
- Use structured concurrency
- Implement async/await patterns
- Handle CoreData migration to SwiftData
- Support CloudKit sync
- Use @ModelActor for concurrent data operations 

</data_flow_patterns>

<testing_specific>

- Write unit tests for view models
- Test SwiftData operations thoroughly
- Implement UI tests for critical flows
- Use preview providers for visual testing
- Test offline capabilities
- Verify state management
- Test migration paths
- Test macro implementations
- Use .previewContainer for SwiftData
- Test navigation flows
- Implement snapshot testing
- Test accessibility
- Test @ModelActor concurrency 

</testing_specific>

<error_handling>

- Implement user-friendly error messages
- Handle network errors gracefully
- Provide offline fallbacks
- Log errors appropriately
- Handle SwiftData errors properly
- Implement error recovery strategies
- Maintain crash reporting
- Use structured concurrency error handling
- Implement retry mechanisms
- Handle validation errors
- Support error analytics
- Handle @ModelActor concurrency errors 

</error_handling>

<dependency_management>

- Use Swift Package Manager when possible
- Keep dependencies minimal
- Version dependencies explicitly
- Document third-party usage
- Handle dependency updates carefully
- Consider dependency impact on app size
- Maintain dependency security
- Use modular architecture
- Implement feature packages
- Handle package versioning
- Support package resources
- Use @_spi for experimental features 

</dependency_management>

<code_organization>

- Follow consistent file structure
- Group related components together
- Use clear naming conventions
- Maintain proper access control
- Document public interfaces
- Keep feature modules separate
- Use extensions appropriately
- Implement Swift macros
- Use feature-based modularization
- Leverage asset catalogs
- Support localization
- Maintain documentation
- Use @_documentation for API documentation 

</code_organization>

<build_and_deployment>

- Use proper code signing
- Implement CI/CD pipelines
- Maintain build configurations
- Handle app thinning
- Support bitcode when required
- Implement proper versioning
- Monitor app size
- Handle TestFlight distribution
- Support app privacy requirements
- Implement proper logging
- Maintain release notes
- Use @_backDeploy for backward compatibility 

</build_and_deployment>

</ios_development_guidelines>