RunApp Project Summary
RunApp is a minimalist fitness tracking application for iOS built with SwiftUI and SwiftData. It focuses on outdoor activities like running, walking, hiking, and biking with these key features:

Real-time activity tracking with route mapping and performance metrics
Integrated metronome with customizable sounds and frequency settings
Detailed performance analytics with interactive charts
Multilingual support (12 languages including Arabic, Chinese, English, etc.)
Theme customization (system, light, dark)
RevenueCat integration for subscription management
The app follows MVVM architecture and uses modern Swift features like @Observable, @Query, and SwiftData for persistence. It's designed with a minimalist philosophy to provide a clutter-free experience for users who value simplicity in their fitness tracking.

## Recent Bug Fixes (June 2025)
- **Activity Deletion Sync Issue**: Fixed critical bug where deleted activities would still appear in AnalysisView after deletion. Implemented notification-based state synchronization between ActivityDetailView and AnalysisView to ensure immediate UI updates after deletion operations. See DevDoc/BugFix-ActivityDeletionNotUpdatingAnalysisView.md for detailed technical documentation.