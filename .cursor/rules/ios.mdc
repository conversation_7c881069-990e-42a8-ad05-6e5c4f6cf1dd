---
description: 
globs: *.swift
alwaysApply: false
---
**Technology Requirements**

* Use only **Swift 6** features compatible with **iOS 17+**.
* Use **SwiftUI 6** for **all UI implementations**.
* Use **SwiftData** for **data persistence**. Do **not** use CoreData or Realm.

---

**CloudKit Compatibility for SwiftData Models**

When defining SwiftData models intended for CloudKit sync:
* Do **not** use `@Attribute(.unique)`.
* All properties must have **default values** or be **optional**.
* All relationships must be **optional**.

---

**Instruction Format for Task Execution**

For each task:

1. Begin with a brief **plan** for what will be implemented.
2. Implement the code using **clean Swift 6 syntax**.
3. Add inline comments for clarity.
4. When needed, include `#Preview` blocks for SwiftUI.
5. Use `@Model` from SwiftData appropriately.
6. Avoid deprecated APIs or legacy patterns (like UIKit, Combine, etc.).

---
Use xcodebuild to test and build to ensure compilation correctly: 
**Command to execute:**
```zsh
xcodebuild -project RunApp.xcodeproj -scheme RunApp -destination 'platform=iOS Simulator,name=iPhone 16,OS=18.4' build
```

---

# iOS SwiftUI MVVM Guidelines

## 1. MVVM Roles  
- **Model**: Data and business logic.  
- **View**: UI rendering (no logic).  
- **ViewModel**: Mediates between View and Model.  

---

## 2. Keep Views Small  
- Break UI into reusable components.  
- Avoid logic in Views; delegate to ViewModel.  

**Example**:  
```swift
struct UserProfileView: View {
    @StateObject var viewModel: UserProfileViewModel
    var body: some View {
        VStack {
            ProfileHeader(user: viewModel.user)
            ProfileDetails(details: viewModel.userDetails)
        }
    }
}
```

---

## 3. Reusable Components  
- Build generic, configurable components.  

**Example**:  
```swift
struct PrimaryButton: View {
    var title: String
    var action: () -> Void
    var body: some View {
        Button(action: action) {
            Text(title).padding().background(Color.blue).foregroundColor(.white).cornerRadius(8)
        }
    }
}
```

---

## 4. ViewModel Best Practices  
- Use `@Published` for state updates.  
- Expose only necessary data to Views.  

**Example**:  
```swift
class UserProfileViewModel: ObservableObject {
    @Published var user: User
    init(user: User) { self.user = user }
    func updateUser() { /* Logic */ }
}
```

---

## 5. Dependency Injection  
- Pass ViewModels or services to Views.  

**Example**:  
```swift
struct UserProfileView: View {
    @StateObject var viewModel: UserProfileViewModel
    init(user: User) {
        _viewModel = StateObject(wrappedValue: UserProfileViewModel(user: user))
    }
    var body: some View { /* View code */ }
}
```

---

## 6. Separate Concerns  
- Move networking, state, and constants into separate files.  

---

## 7. Testing  
- Write unit tests for ViewModels.  
- Use SwiftUI previews for UI testing.  

**Example**:  
```swift
class UserProfileViewModelTests: XCTestCase {
    func testUpdateUser() {
        let user = User(name: "John")
        let viewModel = UserProfileViewModel(user: user)
        viewModel.updateUser()
        XCTAssertEqual(viewModel.user.name, "John Updated")
    }
}
```

---

## 8. Folder Structure  
```
/Features  
  /UserProfile  
    /Models  
    /ViewModels  
    /Views  
/Shared  
  /Components  
  /Services  
```

---

## 9. Avoid Over-Engineering  
- Start simple; refactor as needed.  

---

### Example Project  
```swift
// Model  
struct User { let name: String }  

// ViewModel  
class UserProfileViewModel: ObservableObject {  
    @Published var user: User  
    init(user: User) { self.user = user }  
}  

// View  
struct UserProfileView: View {  
    @StateObject var viewModel: UserProfileViewModel  
    var body: some View {  
        VStack {  
            Text("Hello, \(viewModel.user.name)!")  
            PrimaryButton(title: "Update", action: {})  
        }  
    }  
}  

// Reusable Component  
struct PrimaryButton: View {  
    var title: String  
    var action: () -> Void  
    var body: some View {  
        Button(action: action) {  
            Text(title).padding().background(Color.blue).foregroundColor(.white).cornerRadius(8)  
        }  
    }  
}  

// Preview  
struct UserProfileView_Previews: PreviewProvider {  
    static var previews: some View {  
        UserProfileView(viewModel: UserProfileViewModel(user: User(name: "John")))  
    }  
}
```

---

```
# Swift 6 Concurrency Rules

## Golden Rule
When crossing actor boundaries, always use `Task { @ActorName in ... }`

## Core Rules
1. **Closure Safety**: All closures that access actor-isolated state must wrap access in `Task { @ActorName in ... }`
2. **Three-Question Check**: Before writing closures ask: Where does this run? What does it access? Do they match? If no → use Task wrapper
3. **Danger Patterns**: Always wrap these in `Task { @MainActor in }`: Timer.scheduledTimer, NotificationCenter callbacks, DispatchQueue.async, network completions, deinit methods calling actor code
4. **Explicit Actor Annotation**: Mark classes with @MainActor if they have @Published properties or touch UI
5. **Weak Self Pattern**: Use `Task { @MainActor [weak self] in guard let self = self else { return }; ... }` for async operations

## Examples
```swift
// ❌ Wrong
Timer.scheduledTimer { [weak self] _ in
    self?.actorProperty = value // ERROR
}

// ✅ Correct  
Timer.scheduledTimer { [weak self] _ in
    Task { @MainActor in
        self?.actorProperty = value // SAFE
    }
}

// ❌ Wrong deinit
deinit {
    stopMainActorMethod() // ERROR
}

// ✅ Correct deinit
deinit {
    Task { @MainActor [weak self] in
        self?.stopMainActorMethod()
    }
}
```

## Mental Model
Think of actors as islands and `Task { @Actor }` as bridges. Never throw data across without a bridge.
```