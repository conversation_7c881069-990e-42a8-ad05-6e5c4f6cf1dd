---
description: 
globs: 
alwaysApply: false
---
Execute the `xcodebuild` command with iphone 16 to capture and analyze any compile errors, warnings, or issues. 
Assume you already in the project directory, so no need to use the 'cd' to change the directory.
**Command to execute:**
```zsh
xcodebuild -project RunApp.xcodeproj -scheme RunApp -destination 'platform=iOS Simulator,name=iPhone 16,OS=18.4' build
```