import SwiftData
import SwiftUI

struct ActivityStatsView: View {
  let viewModel: ActivityDetailViewModel
  @Query private var profile: [UserProfile]

  private var unitSystem: UnitSystem {
    profile.first?.units ?? .metric
  }

  var body: some View {
    ScrollView {
      LazyVStack(spacing: 24) {
        // Hero Summary Card
        HeroSummaryCard(viewModel: viewModel, unitSystem: unitSystem)

        // Performance Metrics
        PerformanceMetricsCard(viewModel: viewModel, unitSystem: unitSystem)

        // Time Analysis
        TimeAnalysisCard(viewModel: viewModel)

        // Energy Expenditure
        EnergyExpenditureCard(viewModel: viewModel)
      }
      .padding(.horizontal, 20)
      .padding(.vertical, 16)
    }
    .background(Color(.systemGroupedBackground))
  }
}

// MARK: - Hero Summary Card
struct HeroSummaryCard: View {
  let viewModel: ActivityDetailViewModel
  let unitSystem: UnitSystem

  var body: some View {
    VStack(spacing: 16) {
      // Top row
      HStack(spacing: 16) {
        // Distance
        HeroMetricItem(
          icon: "figure.stairs.circle",
          title: "act_dist".localized,
          value: formatDistanceValue(viewModel.distance, unitSystem: unitSystem),
          unit: formatDistanceUnit(viewModel.distance, unitSystem: unitSystem)
        )

        Divider()
          .frame(height: 60)

        // Active Duration (real active time, not total time)
        HeroMetricItem(
          icon: "clock.badge.checkmark",
          title: "act_time".localized,
          value: formatDetailTime(viewModel.activeTime)
        )
      }

      Divider()
        .background(.secondary.opacity(0.3))

      // Bottom row
      HStack(spacing: 16) {
        // Active Calories
        HeroMetricItem(
          icon: "flame.circle",
          title: "act_cal".localized,
          value: {
            let activeCalories = viewModel.activeCalories
            print(
              "🔥 CALORIE DEBUG: ActivityStatsView hero panel - activeCalories: \(activeCalories)")
            return String(format: "%.0f", activeCalories)
          }(),
          unit: "kcal".localized
        )

        Divider()
          .frame(height: 60)

        // Average Pace
        HeroMetricItem(
          icon: "speedometer",
          title: "avg_pace".localized,
          value: formatDetailPaceValue(viewModel.averagePace, unitSystem: unitSystem),
          unit: formatDetailPaceUnit(unitSystem)
        )
      }
    }
    .modernCardStyle()
  }
}

// MARK: - Performance Metrics Card
struct PerformanceMetricsCard: View {
  let viewModel: ActivityDetailViewModel
  let unitSystem: UnitSystem

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      SectionHeader(title: "performance".localized, icon: "chart.line.uptrend.xyaxis")

      LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 16
      ) {
        MetricTile(
          title: "avg_pace".localized,
          value: formatDetailPaceValue(viewModel.averagePace, unitSystem: unitSystem),
          unit: formatDetailPaceUnit(unitSystem),
          color: .blue
        )

        MetricTile(
          title: "best_pace".localized,
          value: viewModel.bestPace > 0
            ? formatDetailPaceValue(viewModel.bestPace, unitSystem: unitSystem) : "--:--",
          unit: viewModel.bestPace > 0 ? formatDetailPaceUnit(unitSystem) : "",
          color: .green
        )

        MetricTile(
          title: "avg_speed".localized,
          value: formatSpeedValue(viewModel.averageSpeed, unitSystem: unitSystem),
          unit: formatSpeedUnit(unitSystem),
          color: .orange
        )

        MetricTile(
          title: "max_speed".localized,
          value: viewModel.maxSpeed > 0
            ? formatSpeedValue(viewModel.maxSpeed, unitSystem: unitSystem) : "--",
          unit: viewModel.maxSpeed > 0 ? formatSpeedUnit(unitSystem) : "",
          color: .red
        )
      }
    }
    .modernCardStyle()
  }
}

// MARK: - Time Analysis Card
struct TimeAnalysisCard: View {
  let viewModel: ActivityDetailViewModel

  // Use the correct total time calculation: active + paused
  private var calculatedTotalTime: TimeInterval {
    viewModel.activeTime + viewModel.pauseTime
  }

  private var pausePercentage: Double {
    guard calculatedTotalTime > 0 else { return 0 }
    return (viewModel.pauseTime / calculatedTotalTime) * 100
  }

  private var activePercentage: Double {
    guard calculatedTotalTime > 0 else { return 0 }
    return (viewModel.activeTime / calculatedTotalTime) * 100
  }

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      SectionHeader(title: "time_analysis".localized, icon: "clock.fill")

      // Time breakdown with visual representation
      VStack(spacing: 12) {
        TimeBreakdownRow(
          title: "total_time".localized,
          time: calculatedTotalTime,
          percentage: 100,
          color: .primary
        )

        TimeBreakdownRow(
          title: "act_time".localized,
          time: viewModel.activeTime,
          percentage: activePercentage,
          color: .red
        )

        TimeBreakdownRow(
          title: "paused_time".localized,
          time: viewModel.pauseTime,
          percentage: pausePercentage,
          color: .green
        )
      }
    }
    .modernCardStyle()
  }
}

// MARK: - Energy Expenditure Card
struct EnergyExpenditureCard: View {
  let viewModel: ActivityDetailViewModel

  private var calorieRate: Double {
    let totalTime = viewModel.activeTime + viewModel.pauseTime
    guard totalTime > 0 else { return 0 }
    let totalCalories = viewModel.activeCalories + viewModel.pauseCalories
    return totalCalories / (totalTime / 60)  // Total calories per total workout time (more accurate)
  }

  private var activeCalorieRate: Double {
    guard viewModel.activeTime > 0 else { return 0 }
    return viewModel.activeCalories / (viewModel.activeTime / 60)  // Active calories per active time
  }

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      SectionHeader(title: "calories".localized, icon: "flame.fill")

      HStack(spacing: 20) {
        // Total calories with circular progress
        VStack(spacing: 8) {
          ZStack {
            Circle()
              .stroke(Color(.systemGray5), lineWidth: 8)
              .frame(width: 80, height: 80)

            // Calculate correct total calories: active + resting
            let displayActiveCalories = viewModel.activeCalories
            let displayRestingCalories = viewModel.pauseCalories
            let displayTotalCalories = displayActiveCalories + displayRestingCalories

            // Calculate proportions for circular progress
            let activeRatio =
              displayTotalCalories > 0 ? min(displayActiveCalories / displayTotalCalories, 1.0) : 0
            let restingRatio =
              displayTotalCalories > 0 ? min(displayRestingCalories / displayTotalCalories, 1.0) : 0

            // Debug logging for circular progress
            let _ = {
              print(
                "🔥 CIRCLE DEBUG: Active=\(displayActiveCalories), Resting=\(displayRestingCalories), Total=\(displayTotalCalories)"
              )
              print("🔥 CIRCLE DEBUG: ActiveRatio=\(activeRatio), RestingRatio=\(restingRatio)")
              return true
            }()

            // Active calories (red portion) - starts at 0
            Circle()
              .trim(from: 0, to: activeRatio)
              .stroke(.red, style: StrokeStyle(lineWidth: 8, lineCap: .round))
              .frame(width: 80, height: 80)
              .rotationEffect(.degrees(-90))

            // Resting calories (green portion) - starts where active ends
            Circle()
              .trim(from: activeRatio, to: activeRatio + restingRatio)
              .stroke(.green, style: StrokeStyle(lineWidth: 8, lineCap: .round))
              .frame(width: 80, height: 80)
              .rotationEffect(.degrees(-90))

            VStack(spacing: 2) {
              Text(String(format: "%.0f", displayTotalCalories))
                .font(.system(size: 24, weight: .bold, design: .rounded))
              Text("cal".localized)
                .font(.caption2)
                .foregroundStyle(.secondary)
            }
          }

          Text("total".localized)
            .font(.caption)
            .foregroundStyle(.secondary)
        }

        Spacer()

        // Calorie breakdown
        VStack(alignment: .leading, spacing: 12) {
          CalorieBreakdownItem(
            title: "active_calories".localized,
            value: viewModel.activeCalories,
            color: .red
          )

          CalorieBreakdownItem(
            title: "resting_calories".localized,
            value: viewModel.pauseCalories,  // Use pause calories directly
            color: .green
          )

          CalorieBreakdownItem(
            title: "rate".localized,
            value: calorieRate,
            unit: "cal_per_min".localized,
            color: .blue
          )
        }
      }
    }
    .modernCardStyle()
  }
}

// MARK: - Supporting Views
struct SectionHeader: View {
  let title: String
  let icon: String

  var body: some View {
    HStack(spacing: 8) {
      Image(systemName: icon)
        .foregroundStyle(.blue)
        .font(.title3)

      Text(title)
        .font(.title2)
        .fontWeight(.semibold)
        .foregroundStyle(.primary)

      Spacer()
    }
  }
}

struct HeroMetricItem: View {
  let icon: String
  let title: String
  let value: String
  let unit: String?

  init(icon: String, title: String, value: String) {
    self.icon = icon
    self.title = title
    self.value = value
    self.unit = nil
  }

  init(icon: String, title: String, value: String, unit: String) {
    self.icon = icon
    self.title = title
    self.value = value
    self.unit = unit
  }

  var body: some View {
    HStack(spacing: 12) {
      Image(systemName: icon)
        .foregroundStyle(.blue)
        .font(.title2)
        .frame(width: 24, height: 24)

      VStack(alignment: .leading, spacing: 4) {
        Text(title)
          .font(.caption)
          .foregroundStyle(.secondary)

        if let unit = unit, !unit.isEmpty {
          HStack(alignment: .bottom, spacing: 2) {
            Text(value)
              .font(.system(size: 24, weight: .bold, design: .rounded))
              .foregroundStyle(.primary)
            Text(unit)
              .font(.system(size: 14, weight: .medium))
              .foregroundStyle(.secondary)
          }
        } else {
          Text(value)
            .font(.system(size: 24, weight: .bold, design: .rounded))
            .foregroundStyle(.primary)
        }
      }

      Spacer()
    }
    .frame(maxWidth: .infinity, alignment: .leading)
  }
}

struct CompactMetricItem: View {
  let title: String
  let value: String

  var body: some View {
    VStack(alignment: .leading, spacing: 4) {
      Text(title)
        .font(.caption)
        .foregroundStyle(.secondary)
        .multilineTextAlignment(.leading)

      Text(value)
        .font(.system(size: 20, weight: .bold, design: .rounded))
        .foregroundStyle(.primary)
        .multilineTextAlignment(.leading)
    }
    .frame(maxWidth: .infinity, alignment: .leading)
    .padding(.vertical, 4)
  }
}

struct SecondaryMetric: View {
  let title: String
  let value: String
  let icon: String

  var body: some View {
    HStack(spacing: 8) {
      Image(systemName: icon)
        .foregroundStyle(.blue)
        .font(.title3)

      VStack(alignment: .leading, spacing: 2) {
        Text(value)
          .font(.system(size: 18, weight: .semibold, design: .rounded))
          .foregroundStyle(.primary)
        Text(title)
          .font(.caption)
          .foregroundStyle(.secondary)
      }
    }
  }
}

struct MetricTile: View {
  let title: String
  let value: String
  let unit: String?
  let color: Color

  init(title: String, value: String, color: Color) {
    self.title = title
    self.value = value
    self.unit = nil
    self.color = color
  }

  init(title: String, value: String, unit: String, color: Color) {
    self.title = title
    self.value = value
    self.unit = unit
    self.color = color
  }

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        Circle()
          .fill(color.opacity(0.2))
          .frame(width: 8, height: 8)

        Text(title)
          .font(.caption)
          .foregroundStyle(.secondary)

        Spacer()
      }

      if let unit = unit, !unit.isEmpty {
        HStack(alignment: .bottom, spacing: 2) {
          Text(value)
            .font(.system(size: 24, weight: .bold, design: .rounded))
            .foregroundStyle(.primary)
          Text(unit)
            .font(.system(size: 14, weight: .medium))
            .foregroundStyle(.secondary)
        }
      } else {
        Text(value)
          .font(.system(size: 24, weight: .bold, design: .rounded))
          .foregroundStyle(.primary)
      }
    }
    .padding(16)
    .background(Color(.secondarySystemGroupedBackground))
    .cornerRadius(12)
    .overlay(
      RoundedRectangle(cornerRadius: 12)
        .stroke(color.opacity(0.3), lineWidth: 1)
    )
  }
}

struct TimeBreakdownRow: View {
  let title: String
  let time: TimeInterval
  let percentage: Double
  let color: Color

  var body: some View {
    VStack(spacing: 6) {
      HStack(alignment: .bottom) {
        Text(title)
          .font(.subheadline)
          .foregroundStyle(.primary)

        Spacer()

        Text(formatDetailTime(time))
          .font(.system(size: 24, weight: .bold, design: .rounded))
          .foregroundStyle(.primary)

        Text(String(format: "%.0f%%", percentage))
          .font(.caption)
          .foregroundStyle(.secondary)
          .frame(width: 40, alignment: .trailing)
      }

      GeometryReader { geometry in
        ZStack(alignment: .leading) {
          Rectangle()
            .fill(Color(.systemGray5))
            .frame(height: 4)
            .cornerRadius(2)

          Rectangle()
            .fill(color)
            .frame(width: geometry.size.width * (percentage / 100), height: 4)
            .cornerRadius(2)
        }
      }
      .frame(height: 4)
    }
  }
}

struct CalorieBreakdownItem: View {
  let title: String
  let value: Double
  var unit: String = "cal"
  let color: Color

  var body: some View {
    HStack(spacing: 8) {
      Circle()
        .fill(color)
        .frame(width: 8, height: 8)

      Text(title)
        .font(.subheadline)
        .foregroundStyle(.secondary)

      Spacer(minLength: 8)  // Ensure minimum spacing

      HStack(alignment: .bottom, spacing: 2) {
        Text(String(format: "%.0f", value))
          .font(.system(size: 24, weight: .bold, design: .rounded))
          .foregroundStyle(.primary)
          .fixedSize()  // Prevent text wrapping
        Text(unit)
          .font(.system(size: 14, weight: .medium))
          .foregroundStyle(.secondary)
          .fixedSize()  // Prevent text wrapping
      }
      .fixedSize(horizontal: true, vertical: false)  // Keep value+unit on same line
    }
  }
}

// MARK: - Style Extensions
extension View {
  func modernCardStyle() -> some View {
    self
      .padding(20)
      .background(Color(.secondarySystemGroupedBackground))
      .cornerRadius(16)
      .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
  }
}

// MARK: - Formatting Helper Functions
private func formatDetailTime(_ time: TimeInterval) -> String {
  let hours = Int(time) / 3600
  let minutes = (Int(time) % 3600) / 60
  let seconds = Int(time) % 60

  if hours > 0 {
    return String(format: "%d:%02d:%02d", hours, minutes, seconds)
  } else {
    return String(format: "%d:%02d", minutes, seconds)
  }
}

private func formatDetailPace(_ pace: Double, unitSystem: UnitSystem) -> String {
  guard pace > 0 && pace < 60 else { return "--:--" }

  // Convert pace based on unit system
  let adjustedPace = unitSystem == .imperial ? pace * 1.60934 : pace

  let minutes = Int(adjustedPace)
  let seconds = Int((adjustedPace - Double(minutes)) * 60)
  let unit = unitSystem == .metric ? "/km" : "/mi"

  return String(format: "%d:%02d%@", minutes, seconds, unit)
}

private func formatDetailPaceValue(_ pace: Double, unitSystem: UnitSystem) -> String {
  guard pace > 0 && pace < 60 else { return "--:--" }

  // Convert pace based on unit system
  let adjustedPace = unitSystem == .imperial ? pace * 1.60934 : pace

  let minutes = Int(adjustedPace)
  let seconds = Int((adjustedPace - Double(minutes)) * 60)

  return String(format: "%d:%02d", minutes, seconds)
}

private func formatDetailPaceUnit(_ unitSystem: UnitSystem) -> String {
  return unitSystem == .metric ? "/km" : "/mi"
}

func formatSpeed(_ speed: Double, unitSystem: UnitSystem) -> String {
  guard speed > 0 else { return "--" }

  if unitSystem == .metric {
    return String(format: "%.1f km/h", speed * 3.6)
  } else {
    return String(format: "%.1f mph", speed * 2.237)
  }
}

func formatSpeedValue(_ speed: Double, unitSystem: UnitSystem) -> String {
  guard speed > 0 else { return "--" }

  if unitSystem == .metric {
    return String(format: "%.1f", speed * 3.6)
  } else {
    return String(format: "%.1f", speed * 2.237)
  }
}

func formatSpeedUnit(_ unitSystem: UnitSystem) -> String {
  return unitSystem == .metric ? "km/h" : "mph"
}

func formatDistance(_ distance: Double, unitSystem: UnitSystem) -> String {
  if unitSystem == .metric {
    if distance >= 1000 {
      return String(format: "%.2f km", distance / 1000)
    } else {
      return String(format: "%.0f m", distance)
    }
  } else {
    let miles = distance * 0.000621371
    if miles >= 1 {
      return String(format: "%.2f mi", miles)
    } else {
      let feet = distance * 3.28084
      return String(format: "%.0f ft", feet)
    }
  }
}

func formatDistanceValue(_ distance: Double, unitSystem: UnitSystem) -> String {
  if unitSystem == .metric {
    if distance >= 1000 {
      return String(format: "%.2f", distance / 1000)
    } else {
      return String(format: "%.0f", distance)
    }
  } else {
    let miles = distance * 0.000621371
    if miles >= 1 {
      return String(format: "%.2f", miles)
    } else {
      let feet = distance * 3.28084
      return String(format: "%.0f", feet)
    }
  }
}

func formatDistanceUnit(_ distance: Double, unitSystem: UnitSystem) -> String {
  if unitSystem == .metric {
    if distance >= 1000 {
      return "km"
    } else {
      return "m"
    }
  } else {
    let miles = distance * 0.000621371
    if miles >= 1 {
      return "mi"
    } else {
      return "ft"
    }
  }
}
