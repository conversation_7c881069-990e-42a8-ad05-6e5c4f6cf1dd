import SwiftUI

struct MetronomeAnimation: ViewModifier {
    @StateObject private var metronome = MetronomeManager.shared
    @Environment(\.scenePhase) private var scenePhase
    @State private var angle: Double = -15
    @State private var isAnimatingEnabled = true

    func body(content: Content) -> some View {
        content
            .rotationEffect(.degrees(angle))
            .onChange(of: metronome.isOnBeat) { _, isOnBeat in
                if metronome.isEnabled && isAnimatingEnabled {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        angle = isOnBeat ? 15 : -15
                    }
                }
            }
            .onChange(of: metronome.isEnabled) { _, isEnabled in
                if !isEnabled {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        angle = -15
                    }
                }
            }
            .onChange(of: scenePhase) { _, newPhase in
                isAnimatingEnabled = (newPhase == .active)

                // Reset to neutral position when going to background
                if newPhase != .active {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        angle = -15
                    }
                }
            }
    }
}

extension View {
    func metronomeAnimation() -> some View {
        modifier(MetronomeAnimation())
    }
}
