import SwiftUI

struct GpsStatusOverlay: View {
    @Binding var isVisible: Bool
    let hasStartLocation: Bool
    
    var body: some View {
        VStack {
            if isVisible {
                Text(gpsStatusMessage)
                    .font(.title2.weight(.black)) // Slightly smaller than metronome message for less intrusion
                    .foregroundColor(.primary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(.ultraThinMaterial.opacity(0.7))
                    .cornerRadius(12)
                    .transition(.opacity.animation(.easeInOut))
                    .padding(.top, 100) // Add some padding from the top edge
            }
            Spacer() // Pushes the message to the top
        }
        .allowsHitTesting(false) // Prevent the overlay from blocking map interactions
    }
    
    private var gpsStatusMessage: String {
        if hasStartLocation {
            return NSLocalizedString("gps.poor_signal", comment: "Message shown when GPS signal is poor during workout")
        } else {
            return NSLocalizedString("gps.searching", comment: "Message shown while waiting for GPS signal")
        }
    }
}

#Preview {
    ZStack {
        Color.gray // Background for preview
        GpsStatusOverlay(isVisible: .constant(true), hasStartLocation: false)
    }
}
