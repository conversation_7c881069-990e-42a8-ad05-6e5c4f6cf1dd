import SwiftData
import SwiftUI

struct StatsView: View {
  let distance: Double  // in meters
  let elapsedTime: TimeInterval
  let activeTime: TimeInterval
  let recentPace: TimeInterval  // in minutes per kilometer
  let calories: Double  // real-time calories

  @Query private var profile: [UserProfile]

  private var unitSystem: UnitSystem {
    profile.first?.units ?? .metric
  }

  private var formattedDistance: String {
    if unitSystem == .metric {
      return String(format: "%.2f", distance / 1000)  // meters to km
    } else {
      return String(format: "%.2f", distance / 1609.34)  // meters to miles
    }
  }

  private var distanceUnit: String {
    unitSystem == .metric ? "km" : "mi"
  }

  private var formattedPace: String {
    if unitSystem == .metric {
      // Already in min/km
      return formatPace(recentPace)
    } else {
      // Convert to min/mile
      let paceInMiles = recentPace * 1.60934  // Convert min/km to min/mile
      return formatPace(paceInMiles)
    }
  }

  var body: some View {
    VStack(spacing: 0) {
      // Top row: Distance and Calories
      HStack(spacing: 0) {
        // Distance cell
        ZStack {
          Text(formattedDistance)
            .font(.system(.largeTitle, design: .rounded))
            .fontWeight(.black)
          VStack {
            Spacer()
            HStack {
              Spacer()
              Text(distanceUnit.localized)
                .font(.system(.footnote, design: .rounded))
                .fontWeight(.heavy)
                .foregroundStyle(.primary)
            }
          }
          .padding(.trailing, 8)
          .padding(.bottom, 4)
        }
        .frame(maxWidth: .infinity, maxHeight: 40)

        // Vertical divider
        Rectangle()
          .fill(Color.gray.opacity(0.7))
          .frame(width: 2, height: 30)

        // Calories cell
        ZStack {
          Text(String(format: "%.0f", calories))
            .font(.system(.largeTitle, design: .rounded))
            .fontWeight(.black)
          VStack {
            Spacer()
            HStack {
              Spacer()
              Text("cal")
                .font(.system(.footnote, design: .rounded))
                .fontWeight(.heavy)
                .foregroundStyle(.primary)
            }
          }
          .padding(.trailing, 8)
          .padding(.bottom, 4)
        }
        .frame(maxWidth: .infinity, maxHeight: 40)
      }

      // Horizontal divider
      Rectangle()
        .fill(Color.gray.opacity(0.7))
        .frame(height: 2)
        .padding(.horizontal, 20)

      // Bottom row: Time and Pace
      HStack(spacing: 0) {
        // Time cell
        ZStack {
          Text(formatTime(activeTime))
            .font(.system(.largeTitle, design: .rounded))
            .fontWeight(.black)
        }
        .frame(maxWidth: .infinity, maxHeight: 40)

        // Vertical divider
        Rectangle()
          .fill(Color.gray.opacity(0.7))
          .frame(width: 2, height: 30)

        // Pace cell
        ZStack {
          Text(formattedPace)
            .font(.system(.largeTitle, design: .rounded))
            .fontWeight(.black)
          VStack {
            Spacer()
            HStack {
              Spacer()
              Text("pace".localized)
                .font(.system(.footnote, design: .rounded))
                .fontWeight(.heavy)
                .foregroundStyle(.primary)
            }
          }
          .padding(.trailing, 8)
          .padding(.bottom, 4)
        }
        .frame(maxWidth: .infinity, maxHeight: 40)
      }
    }
    .padding(.horizontal, 8)
    .padding(.vertical, 8)
    .background(Color.gray.opacity(0.3))
    .clipShape(RoundedRectangle(cornerRadius: 20))
    .padding(.horizontal, 20)
  }

  private func formatTime(_ timeInterval: TimeInterval) -> String {
    let totalSeconds = Int(timeInterval)
    let hours = totalSeconds / 3600
    let minutes = (totalSeconds % 3600) / 60
    let seconds = totalSeconds % 60

    if hours == 0 {
      if minutes < 10 {
        // Format as m:ss when hours are zero and minutes < 10
        return String(format: "%d:%02d", minutes, seconds)
      } else {
        // Format as mm:ss when hours are zero and minutes >= 10
        return String(format: "%02d:%02d", minutes, seconds)
      }
    } else if hours < 10 {
      // Format as h:mm:ss when hours are 1-9
      return String(format: "%d:%02d:%02d", hours, minutes, seconds)
    } else {
      // Format as hh:mm:ss when hours are 10 or more
      return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
  }

  private func formatPace(_ pace: TimeInterval) -> String {
    guard pace > 0 else { return "--'--\"" }
    let minutes = Int(pace)
    let seconds = Int((pace - Double(minutes)) * 60)
    return String(format: "%d'%02d\"", minutes, seconds)
  }
}

#Preview {
  StatsView(
    distance: 5000,  // 5 km
    elapsedTime: 1800,  // 30 minutes
    activeTime: 1750,  // 29:10 minutes
    recentPace: 5.5,  // 5:30 min/km
    calories: 245.5  // calories
  )
  .modelContainer(for: UserProfile.self, inMemory: true)
}
