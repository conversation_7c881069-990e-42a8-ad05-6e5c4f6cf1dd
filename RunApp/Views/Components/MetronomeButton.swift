import SwiftUI
import SwiftData

struct MetronomeButton: View {
    @ObservedObject private var metronome = MetronomeManager.shared
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @State private var isAnimating = false
    var inWorkoutMode: Bool = false
    var isPaused: Bool = false
    
    var body: some View {
        Button(action: {
            print("Metronome button tapped (isPaused: \(isPaused))")
            ensureUserProfileExists()
            
            if let userProfile = profile.first {
                print("Found user profile, BPM: \(userProfile.metronomeBPM)")
                metronome.toggleMetronome(isPaused: isPaused)
                
                // Update the profile
                userProfile.metronomeEnabled = metronome.isEnabled
                try? modelContext.save()
            } else {
                print("No user profile found after creation attempt!")
            }
        }) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                (metronome.isEnabled ? Color.red : Color.gray).opacity(0.8),
                                (metronome.isEnabled ? Color.red : Color.gray)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: inWorkoutMode ? 35 : 45, height: inWorkoutMode ? 35 : 45)
                Image(systemName: "metronome.fill")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    .metronomeAnimation()
            }
            .overlay(alignment: .topTrailing) {
                // Alert frequency badge
                if metronome.isEnabled, let userProfile = profile.first {
                    AlertFrequencyBadge(
                        alertFrequency: userProfile.metronomeAlertFrequency,
                        isCompact: inWorkoutMode
                    )
                    .offset(x: inWorkoutMode ? 4 : 6, y: inWorkoutMode ? -4 : -6)
                }
            }
            .shadow(color: Color.black.opacity(0.8), radius: 5, x: 0, y: 3)
        }
    }
    
    private func ensureUserProfileExists() {
        if profile.isEmpty {
            print("Creating default user profile")
            let newProfile = UserProfile()
            newProfile.metronomeBPM = 180  // Default BPM
            newProfile.metronomeEnabled = false
            newProfile.metronomeSoundEnabled = true
            newProfile.metronomeVibrationEnabled = true
            modelContext.insert(newProfile)
            try? modelContext.save()
            print("Default profile created with BPM: \(newProfile.metronomeBPM)")
        }
    }
}

#Preview {
    MetronomeButton()
        .modelContainer(for: UserProfile.self, inMemory: true)
}

// MARK: - Alert Frequency Badge Component

struct AlertFrequencyBadge: View {
    let alertFrequency: MetronomeAlertFrequency
    let isCompact: Bool
    
    private var badgeSize: CGFloat {
        isCompact ? 16 : 20
    }
    
    private var fontSize: CGFloat {
        isCompact ? 10 : 12
    }
    
    var body: some View {
        Circle()
            .fill(Color.black.opacity(0.85))
            .frame(width: badgeSize, height: badgeSize)
            .overlay(
                Text("\(alertFrequency.rawValue)")
                    .font(.system(size: fontSize, weight: .bold))
                    .foregroundColor(.white)
            )
            .overlay(
                Circle()
                    .stroke(Color.white, lineWidth: 1.5)
            )
    }
}

// MARK: - Preview for Badge

#Preview("Badge Examples") {
    VStack(spacing: 20) {
        HStack(spacing: 20) {
            ForEach(MetronomeAlertFrequency.allCases, id: \.self) { frequency in
                VStack {
                    AlertFrequencyBadge(alertFrequency: frequency, isCompact: false)
                    Text("\(frequency.rawValue)")
                        .font(.caption)
                }
            }
        }
        
        Text("Compact Mode")
            .font(.headline)
        
        HStack(spacing: 20) {
            ForEach(MetronomeAlertFrequency.allCases, id: \.self) { frequency in
                VStack {
                    AlertFrequencyBadge(alertFrequency: frequency, isCompact: true)
                    Text("\(frequency.rawValue)")
                        .font(.caption)
                }
            }
        }
    }
    .padding()
}
