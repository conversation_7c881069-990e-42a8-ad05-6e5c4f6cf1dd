import SwiftUI

struct ActivityListSection: View {
    let activities: [RunActivity]
    
    var body: some View {
        ForEach(activities) { activity in
            NavigationLink(destination: ActivityDetailView(activity: activity)) {
                ActivityRowView(activity: activity)
                    .foregroundColor(.primary)
            }
        }
    }
}

#Preview {
    NavigationStack {
        ActivityListSection(activities: [])
            .padding()
    }
} 