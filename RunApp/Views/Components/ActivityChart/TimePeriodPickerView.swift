import SwiftUI

struct TimePeriodPickerView: View {
    @Binding var selectedPeriod: AnalysisView.TimePeriod
    
    private func localizedPeriod(_ period: AnalysisView.TimePeriod) -> String {
        switch period {
        case .sevenDays:
            return "seven_days".localized
        case .oneMonth:
            return "one_month".localized
        case .oneYear:
            return "one_year".localized
        case .allTime:
            return "all_time".localized
        }
    }
    
    var body: some View {
        HStack {
            Picker("time_period".localized, selection: $selectedPeriod) {
                ForEach(AnalysisView.TimePeriod.allCases, id: \.self) { period in
                    Text(localizedPeriod(period)).bold().tag(period)
                }
            }
            .pickerStyle(.segmented)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.3), radius: 5, x: 0, y: 2)
            )
        }
        .padding(.horizontal)
    }
}

#Preview {
    TimePeriodPickerView(selectedPeriod: .constant(.sevenDays))
}
