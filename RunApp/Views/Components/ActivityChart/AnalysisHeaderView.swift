import SwiftUI

struct AnalysisHeaderView: View {
    @Binding var selectedActivityType: AnalysisView.ActivityType
    @Binding var selectedMetric: ChartMetric
    
    private var localizedActivityType: String {
        selectedActivityType.localized
    }
    
    private func localizedMetric(_ metric: ChartMetric) -> String {
        switch metric {
        case .distance:
            return "distance".localized
        case .duration:
            return "duration".localized
        case .calories:
            return "calories".localized
        }
    }
    
    private func iconForActivityType(_ type: AnalysisView.ActivityType) -> String {
        switch type {
        case .run: return "figure.run"
        case .walk: return "figure.walk"
        case .hike: return "figure.hiking"
        case .bike: return "figure.outdoor.cycle"
        }
    }
    
    var body: some View {
        HStack {
            // Activity Type Dropdown
            Menu {
                ForEach(AnalysisView.ActivityType.allCases, id: \.self) { type in
                    Button {
                        selectedActivityType = type
                    } label: {
                        HStack {
                            Image(systemName: iconForActivityType(type))
                                .foregroundColor(.blue)
                            Text(type.localized)
                            if selectedActivityType == type {
                                Image(systemName: "checkmark")
                            }
                        }
                    }
                }
            } label: {
                HStack(spacing: 4) {
                    Image(systemName: iconForActivityType(selectedActivityType))
                        .foregroundColor(.blue)
                    Text(localizedActivityType)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Image(systemName: "chevron.down")
                        .font(.caption)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 5)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.3), radius: 5, x: 0, y: 2)
                )
            }

            Spacer()

            // Metric Selection
            HStack(spacing: 6) {
                ForEach(ChartMetric.allCases, id: \.self) { metric in
                    Button(action: { selectedMetric = metric }) {
                        Text(localizedMetric(metric))
                            .font(.subheadline.bold())
                            .padding(.horizontal, 5)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 5)
                                    .fill(selectedMetric == metric ? metric.color.opacity(0.2) : Color(.systemBackground))
                                    .shadow(color: Color.black.opacity(0.3), radius: 5, x: 0, y: 2)
                            )
                            .foregroundColor(selectedMetric == metric ? metric.color : .primary)
                    }
                }
            }
        }
        .padding(.horizontal)
    }
}

#Preview {
    AnalysisHeaderView(
        selectedActivityType: .constant(.run),
        selectedMetric: .constant(.distance)
    )
}
