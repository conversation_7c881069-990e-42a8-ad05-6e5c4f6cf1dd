import SwiftUI

struct AnalysisStatCard: View {
    let icon: String
    let title: String
    let value: String

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.title2)

            VStack(alignment: .leading) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.headline)
            }
        }
        .padding()
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(10)
    }
}

struct ActivitySummaryPanel: View {
    let selectedMetric: ChartMetric
    let totalValue: String
    let averageValue: String
    let averageLabel: String
    
    private var localizedMetric: String {
        switch selectedMetric {
        case .distance:
            return "distance".localized
        case .duration:
            return "duration".localized
        case .calories:
            return "calories".localized
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            AnalysisStatCard(
                icon: "sum",
                title: "\("total".localized) \(localizedMetric)",
                value: totalValue
            )
            .frame(maxWidth: .infinity)

            AnalysisStatCard(
                icon: "chart.bar.xaxis",
                title: "\("avg".localized) \(averageLabel.localized)",
                value: averageValue
            )
            .frame(maxWidth: .infinity)
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    ActivitySummaryPanel(
        selectedMetric: .distance,
        totalValue: "10.5 km",
        averageValue: "2.1 km",
        averageLabel: "per_day"
    )
}