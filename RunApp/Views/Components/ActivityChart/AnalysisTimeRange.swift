import SwiftUI

struct AnalysisTimeRange {
    private let calendar = Calendar.current
    private let now = Date()
    
    let selectedPeriod: AnalysisView.TimePeriod
    let weekOffset: Int
    let monthOffset: Int
    let yearOffset: Int
    let activities: [RunActivity]
    
    var visibleTimeRange: (start: Date, end: Date) {
        switch selectedPeriod {
        case .sevenDays:
            let currentComponents = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now)
            let currentWeekStart = calendar.date(from: currentComponents)!
            let weekStart = calendar.date(byAdding: .weekOfYear, value: weekOffset, to: currentWeekStart)!
            let weekEnd = calendar.date(byAdding: .day, value: 7, to: weekStart)!
            return (weekStart, weekEnd)
            
        case .oneMonth:
            let currentComponents = calendar.dateComponents([.year, .month], from: now)
            let currentMonthStart = calendar.date(from: currentComponents)!
            let monthStart = calendar.date(byAdding: .month, value: monthOffset, to: currentMonthStart)!
            let nextMonthStart = calendar.date(byAdding: .month, value: 1, to: monthStart)!
            return (monthStart, nextMonthStart)
            
        case .oneYear:
            let currentComponents = calendar.dateComponents([.year], from: now)
            let currentYearStart = calendar.date(from: currentComponents)!
            let yearStart = calendar.date(byAdding: .year, value: yearOffset, to: currentYearStart)!
            let nextYearStart = calendar.date(byAdding: .year, value: 1, to: yearStart)!
            return (yearStart, nextYearStart)
            
        case .allTime:
            let periodStart = activities.map { $0.startTime }.min() ??
                calendar.date(byAdding: .year, value: -3, to: now)!
            return (periodStart, now)
        }
    }
    
    func getDivisor() -> Int {
        switch selectedPeriod {
        case .sevenDays:
            return 7 // days per week
        case .oneMonth:
            let offsetDate = calendar.date(byAdding: .month, value: monthOffset, to: now)!
            let range = calendar.range(of: .day, in: .month, for: offsetDate)!
            return range.count
        case .oneYear:
            return 12 // months per year
        case .allTime:
            let (start, end) = visibleTimeRange
            let components = calendar.dateComponents([.year], from: start, to: end)
            return max(components.year ?? 1, 1)
        }
    }
    
    func getAverageLabel() -> String {
        switch selectedPeriod {
        case .sevenDays, .oneMonth:
            return "per_day".localized
        case .oneYear, .allTime:
            return "per_month".localized
        }
    }
} 