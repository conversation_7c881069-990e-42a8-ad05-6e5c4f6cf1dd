import SwiftUI

struct PeriodIndicatorView: View {
    let selectedPeriod: AnalysisView.TimePeriod
    let weekOffset: Int
    let monthOffset: Int
    let yearOffset: Int
    
    private var calendar: Calendar {
        Calendar.current
    }
    
    private var now: Date {
        Date()
    }
    
    private func formatWeekText(offset: Int, year: Int) -> String {
        if offset == 0 {
            return "current_week".localized
        }
        let absOffset = abs(offset)
        if absOffset == 1 {
            return String(format: "week_ago_format".localized, absOffset, year)
        }
        return String(format: "weeks_ago_format".localized, absOffset, year)
    }
    
    private func formatMonthText(offset: Int, year: Int) -> String {
        if offset == 0 {
            return "current_month".localized
        }
        let absOffset = abs(offset)
        if absOffset == 1 {
            return String(format: "month_ago_format".localized, absOffset, year)
        }
        return String(format: "months_ago_format".localized, absOffset, year)
    }
    
    private func formatYearText(offset: Int, year: Int) -> String {
        if offset == 0 {
            return "current_year".localized
        }
        let absOffset = abs(offset)
        if absOffset == 1 {
            return String(format: "year_ago_format".localized, absOffset, year)
        }
        return String(format: "years_ago_format".localized, absOffset, year)
    }
    
    var body: some View {
        switch selectedPeriod {
        case .sevenDays:
            let weekStartDate = calendar.date(byAdding: .weekOfYear, value: weekOffset, to: now)!
            let year = calendar.component(.year, from: weekStartDate)
            Text(formatWeekText(offset: weekOffset, year: year))
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.top, -4)
        case .oneMonth:
            let monthStartDate = calendar.date(byAdding: .month, value: monthOffset, to: now)!
            let year = calendar.component(.year, from: monthStartDate)
            Text(formatMonthText(offset: monthOffset, year: year))
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.top, -4)
        case .oneYear:
            let yearStartDate = calendar.date(byAdding: .year, value: yearOffset, to: now)!
            let year = calendar.component(.year, from: yearStartDate)
            Text(formatYearText(offset: yearOffset, year: year))
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.top, -4)
        default:
            EmptyView()
        }
    }
}

#Preview {
    VStack {
        PeriodIndicatorView(
            selectedPeriod: .sevenDays,
            weekOffset: -1,
            monthOffset: 0,
            yearOffset: 0
        )
        PeriodIndicatorView(
            selectedPeriod: .oneMonth,
            weekOffset: 0,
            monthOffset: -2,
            yearOffset: 0
        )
        PeriodIndicatorView(
            selectedPeriod: .oneYear,
            weekOffset: 0,
            monthOffset: 0,
            yearOffset: -1
        )
    }
} 