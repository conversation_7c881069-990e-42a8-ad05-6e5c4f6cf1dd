import SwiftUI

struct AnalysisMetrics {
    let activities: [RunActivity]
    let selectedMetric: ChartMetric
    let unitSystem: UnitSystem
    let divisor: Int
    
    private var filteredActivities: [RunActivity] {
        activities
    }
    
    // Total metrics
    var totalDistance: Double {
        filteredActivities.reduce(0) { $0 + $1.distance }
    }

    var totalDuration: TimeInterval {
        filteredActivities.reduce(0) { $0 + $1.duration }
    }

    var totalCalories: Double {
        filteredActivities.reduce(0) { $0 + $1.calories }
    }

    // Average metrics
    var averageDistance: Double {
        divisor > 0 ? totalDistance / Double(divisor) : 0
    }

    var averageDuration: TimeInterval {
        divisor > 0 ? totalDuration / Double(divisor) : 0
    }

    var averageCalories: Double {
        divisor > 0 ? totalCalories / Double(divisor) : 0
    }
    
    // Formatted values
    func formatTotalMetric() -> String {
        switch selectedMetric {
        case .distance:
            return String(format: "%.1f", unitSystem.convertDistance(totalDistance)) + " " + selectedMetric.unit(unitSystem: unitSystem)
        case .duration:
            return formatDuration(totalDuration)
        case .calories:
            return String(format: "%.0f", totalCalories) + " " + selectedMetric.unit(unitSystem: unitSystem)
        }
    }

    func formatAverageMetric() -> String {
        switch selectedMetric {
        case .distance:
            return String(format: "%.1f", unitSystem.convertDistance(averageDistance)) + " " + selectedMetric.unit(unitSystem: unitSystem)
        case .duration:
            return formatDuration(averageDuration)
        case .calories:
            return String(format: "%.0f", averageCalories) + " " + selectedMetric.unit(unitSystem: unitSystem)
        }
    }

    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = duration / 3600.0
        return String(format: "%.1f hr", hours)
    }
} 