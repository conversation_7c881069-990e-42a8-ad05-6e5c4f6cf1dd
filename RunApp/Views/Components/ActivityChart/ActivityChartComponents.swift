import SwiftUI
import Charts

struct ActivityDataPoint: Identifiable {
    let id: UUID
    let activityId: UUID  // Original activity ID
    let date: Date
    let distance: Double
    let duration: TimeInterval
    let calories: Double
    let unitSystem: UnitSystem  // Add unit system to data point

    func value(for selectedMetric: ChartMetric) -> Double {
        switch selectedMetric {
        case .distance:
            return unitSystem.convertDistance(distance)
        case .duration: return duration
        case .calories: return calories
        }
    }
}

enum ChartMetric: String, CaseIterable {
    case distance = "Distance"
    case duration = "Duration"
    case calories = "Calories"

    var color: Color {
        switch self {
        case .distance: return .blue
        case .duration: return .green
        case .calories: return .orange
        }
    }

    func unit(unitSystem: UnitSystem) -> String {
        switch self {
        case .distance:
            return unitSystem.distanceUnit
        case .duration: return "hr"
        case .calories: return "cal"
        }
    }
}

struct ActivityChart: View {
    let chartData: [ActivityDataPoint]
    let selectedPeriod: AnalysisView.TimePeriod
    let selectedMetric: ChartMetric
    @Binding var selectedDataPoint: ActivityDataPoint?

    private func formatChartDuration(_ timeInterval: TimeInterval) -> String {
        let hours = timeInterval / 3600.0
        return String(format: "%.1f hr", hours)
    }

    private func handleChartTap(at location: CGPoint) {
        if let point = chartData.first {
            selectedDataPoint = point
        }
    }

    var body: some View {
        VStack {
            Chart(chartData) { point in
                BarMark(
                    x: .value("Date", point.date, unit: selectedPeriod == .oneYear ? .month :
                            selectedPeriod == .allTime ? .year : .day),
                    y: .value(selectedMetric.rawValue, point.value(for: selectedMetric)),
                    width: .fixed(selectedPeriod == .oneMonth ? 6 : 14),
                    stacking: .standard
                )
                .foregroundStyle(selectedMetric.color.gradient)
                .clipShape(Capsule())
                .opacity(selectedDataPoint?.id == point.id ? 1.0 : 0.8)

                if selectedDataPoint?.id == point.id {
                    RuleMark(
                        x: .value("Date", point.date, unit: selectedPeriod == .oneYear ? .month :
                                selectedPeriod == .allTime ? .year : .day)
                    )
                    .foregroundStyle(selectedMetric.color.opacity(0.3))
                }

                if let selected = selectedDataPoint, selected.id == point.id {
                    PointMark(
                        x: .value("Date", point.date, unit: selectedPeriod == .oneYear ? .month :
                                selectedPeriod == .allTime ? .year : .day),
                        y: .value(selectedMetric.rawValue, point.value(for: selectedMetric))
                    )
                    .foregroundStyle(selectedMetric.color)
                    .annotation(position: .top) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(point.date, format: selectedPeriod.xAxisFormat)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            Text(selectedMetric == .distance ?
                                 "\(String(format: "%.1f", point.unitSystem.convertDistance(point.distance))) \(selectedMetric.unit(unitSystem: point.unitSystem))" :
                                 selectedMetric == .duration ?
                                 formatChartDuration(point.value(for: selectedMetric)) :
                                 selectedMetric == .calories ?
                                 "\(String(format: "%.0f", point.value(for: selectedMetric))) \(selectedMetric.unit(unitSystem: point.unitSystem))" :
                                 "\(String(format: "%.1f", point.value(for: selectedMetric))) \(selectedMetric.unit(unitSystem: point.unitSystem))")
                                .font(.caption.bold())
                                .foregroundColor(selectedMetric.color)
                        }
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.background)
                                .shadow(radius: 2)
                        )
                    }
                }
            }
            .chartXAxis {
                AxisMarks(values: .stride(by: selectedPeriod.strideComponent)) { value in
                    if let date = value.as(Date.self) {
                        if selectedPeriod == .oneMonth {
                            let calendar = Calendar.current
                            let day = calendar.component(.day, from: date)
                            if day == 1 || day == 10 || day == 20 {
                                AxisGridLine()
                                AxisValueLabel {
                                    Text(date, format: .dateTime.month(.defaultDigits).day())
                                        .font(.caption2)
                                }
                            }
                        } else {
                            AxisGridLine()
                            AxisTick()
                            AxisValueLabel {
                                switch selectedPeriod {
                                case .sevenDays:
                                    Text(date, format: .dateTime.month(.defaultDigits).day(.defaultDigits))
                                        .font(.caption2)
                                        .frame(width: 40, alignment: .center)
                                case .oneYear:
                                    let monthStr = date.formatted(.dateTime.month(.abbreviated))
                                    Text(String(monthStr.prefix(1)))
                                        .font(.caption2)
                                case .allTime:
                                    Text(date, format: .dateTime.year())
                                        .font(.caption2)
                                default:
                                    Text(date, format: .dateTime.month(.abbreviated))
                                        .font(.caption2)
                                }
                            }
                        }
                    }
                }
            }
            .chartYAxis {
                AxisMarks(preset: .aligned, values: .automatic(desiredCount: 5)) { value in
                    AxisGridLine()
                    AxisValueLabel {
                        if case .duration = selectedMetric,
                           let timeInterval = value.as(Double.self) {
                            Text(formatChartDuration(timeInterval))
                        } else if let doubleValue = value.as(Double.self) {
                            if let firstPoint = chartData.first {
                                if case .calories = selectedMetric {
                                    Text(String(format: "%.0f %@", doubleValue, selectedMetric.unit(unitSystem: firstPoint.unitSystem)))
                                } else {
                                    Text(String(format: "%.1f %@", doubleValue, selectedMetric.unit(unitSystem: firstPoint.unitSystem)))
                                }
                            } else {
                                if case .calories = selectedMetric {
                                    Text(String(format: "%.0f", doubleValue))
                                } else {
                                    Text(String(format: "%.1f", doubleValue))
                                }
                            }
                        }
                    }
                }
            }
            .padding(.top, 4)
        }
        .frame(height: 200)
        .if(selectedPeriod == .allTime) { view in
            view
                .gesture(
                    DragGesture(minimumDistance: 0)
                        .onChanged { _ in
                            selectedDataPoint = nil
                        }
                )
                .onTapGesture { location in
                    handleChartTap(at: location)
                }
        }
    }
} 