import SwiftUI

struct ChartNavigationView: View {
    let selectedPeriod: AnalysisView.TimePeriod
    let selectedMetric: ChartMetric
    @Binding var selectedDataPoint: ActivityDataPoint?
    
    @Binding var weekOffset: Int
    @Binding var monthOffset: Int
    @Binding var yearOffset: Int
    
    let loadedWeekRange: ClosedRange<Int>
    let loadedMonthRange: ClosedRange<Int>
    let loadedYearRange: ClosedRange<Int>
    let maxYearsBack: Int
    
    let historicalWeekData: [Int: [ActivityDataPoint]]
    let historicalMonthData: [Int: [ActivityDataPoint]]
    let historicalYearData: [Int: [ActivityDataPoint]]
    let chartData: [ActivityDataPoint]
    
    let onWeekOffsetChange: (Int) -> Void
    let onMonthOffsetChange: (Int) -> Void
    let onYearOffsetChange: (Int) -> Void
    
    var body: some View {
        GeometryReader { geometry in
            switch selectedPeriod {
            case .sevenDays:
                TabView(selection: $weekOffset) {
                    ForEach(Array(loadedWeekRange), id: \.self) { offset in
                        ActivityChart(
                            chartData: historicalWeekData[offset, default: []],
                            selectedPeriod: selectedPeriod,
                            selectedMetric: selectedMetric,
                            selectedDataPoint: $selectedDataPoint
                        )
                        .frame(width: geometry.size.width - 32)
                        .tag(offset)
                    }
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                .frame(height: 200)
                .onChange(of: weekOffset) { _, newValue in
                    onWeekOffsetChange(newValue)
                }
            case .oneMonth:
                TabView(selection: $monthOffset) {
                    ForEach(Array(loadedMonthRange), id: \.self) { offset in
                        ActivityChart(
                            chartData: historicalMonthData[offset, default: []],
                            selectedPeriod: selectedPeriod,
                            selectedMetric: selectedMetric,
                            selectedDataPoint: $selectedDataPoint
                        )
                        .frame(maxWidth: .infinity)
                        .frame(width: geometry.size.width - 32)
                        .tag(offset)
                    }
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                .frame(height: 200)
                .onChange(of: monthOffset) { _, newValue in
                    onMonthOffsetChange(newValue)
                }
            case .oneYear:
                TabView(selection: $yearOffset) {
                    ForEach(Array(loadedYearRange).filter { $0 >= -maxYearsBack }, id: \.self) { offset in
                        ActivityChart(
                            chartData: historicalYearData[offset, default: []],
                            selectedPeriod: selectedPeriod,
                            selectedMetric: selectedMetric,
                            selectedDataPoint: $selectedDataPoint
                        )
                        .frame(maxWidth: .infinity)
                        .frame(width: geometry.size.width - 32)
                        .tag(offset)
                    }
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                .frame(height: 200)
                .onChange(of: yearOffset) { _, newValue in
                    onYearOffsetChange(newValue)
                }
            default:
                ActivityChart(
                    chartData: chartData,
                    selectedPeriod: selectedPeriod,
                    selectedMetric: selectedMetric,
                    selectedDataPoint: $selectedDataPoint
                )
                .frame(width: geometry.size.width - 32)
            }
        }
        .frame(height: 200)
        .padding(.horizontal)
    }
}

#Preview {
    ChartNavigationView(
        selectedPeriod: .sevenDays,
        selectedMetric: .distance,
        selectedDataPoint: .constant(nil),
        weekOffset: .constant(0),
        monthOffset: .constant(0),
        yearOffset: .constant(0),
        loadedWeekRange: -11...0,
        loadedMonthRange: -11...0,
        loadedYearRange: -11...0,
        maxYearsBack: 3,
        historicalWeekData: [:],
        historicalMonthData: [:],
        historicalYearData: [:],
        chartData: [],
        onWeekOffsetChange: { _ in },
        onMonthOffsetChange: { _ in },
        onYearOffsetChange: { _ in }
    )
} 