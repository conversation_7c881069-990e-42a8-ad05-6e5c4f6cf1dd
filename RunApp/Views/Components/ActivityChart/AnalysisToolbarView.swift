import SwiftUI

struct AnalysisToolbarView: ToolbarContent {
    let dismiss: DismissAction
    let onGenerateTestData: () -> Void
    let onGenerateLargeTestData: () -> Void
    
    var body: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            <PERSON><PERSON>(action: { dismiss() }) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundStyle(.secondary)
                    .font(.title2)
                    .shadow(color: Color.black.opacity(0.5), radius: 5, x: 0, y: 3)
            }
        }

        ToolbarItem(placement: .navigationBarTrailing) {
            Menu {
                Button("Generate Test Data", action: onGenerateTestData)
                But<PERSON>("Generate Large Test Data", action: onGenerateLargeTestData)
            } label: {
                Image(systemName: "plus.circle")
                    .foregroundStyle(.blue)
                    .font(.title2)
            }
        }
    }
}

struct AnalysisToolbarPreview: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            Color.clear
                .toolbar {
                    AnalysisToolbarView(
                        dismiss: dismiss,
                        onGenerateTestData: {},
                        onGenerateLargeTestData: {}
                    )
                }
        }
    }
}

#Preview {
    AnalysisToolbarPreview()
} 