import SwiftUI
import AudioToolbox

struct ControlsOverlay: View {
    @Binding var isRunning: <PERSON><PERSON>
    @Binding var isPaused: <PERSON><PERSON>
    @Binding var selectedDetent: PresentationDetent
    @Binding var selectedSportType: SportType?
    @Binding var showingCountdown: <PERSON><PERSON>
    @Binding var showingAnalysisView: <PERSON><PERSON>
    @Binding var showingCompleteAlert: Bool
    
    let distance: Double
    let elapsedTime: TimeInterval
    let activeTime: TimeInterval
    let recentPace: TimeInterval
    let calories: Double
    let onPause: () -> Void
    let onResume: () -> Void
    let onShowSettings: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // Stats View
            if isRunning || isPaused {
                StatsView(
                    distance: distance,
                    elapsedTime: elapsedTime,
                    activeTime: activeTime,
                    recentPace: recentPace,
                    calories: calories
                )
                .presentationDetents([.height(100), .medium, .large], selection: $selectedDetent)
                .presentationBackground(.regularMaterial)
                .presentationBackgroundInteraction(.enabled(upThrough: .medium))
                .presentationCornerRadius(12)
                .padding(.bottom, 16)
            }
            
            // Sport Type Selector
            if !isRunning && !isPaused {
                SportTypeSelector(selectedSportType: $selectedSportType)
            }
            
            // Activity Controls
            ActivityControls(
                isRunning: isRunning,
                isPaused: isPaused,
                selectedSportType: selectedSportType,
                onStart: {
                    showingCountdown = true
                    AudioServicesPlaySystemSound(1103)
                },
                onPause: onPause,
                onResume: onResume,
                onEnd: { showingCompleteAlert = true },
                onShowAnalysis: { showingAnalysisView = true },
                onShowSettings: onShowSettings
            )
        }
        .padding(.bottom, 30)
    }
}
