import SwiftUI

struct CustomIntervalPopup: View {
    let alertType: String
    let presets: [Double]
    let unit: String
    let formatter: (Double) -> String
    let integerOnly: Bool
    let validationRange: ClosedRange<Double>?
    
    @Binding var selectedValue: Double
    @Binding var isPresented: Bool
    
    @State private var customValue: String = ""
    @State private var showingValidationError = false
    @State private var validationErrorMessage = ""
    
    @Environment(\.colorScheme) private var colorScheme
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        ZStack {
            // iOS-style background dimming
            Rectangle()
                .fill(.black.opacity(0.25))
                .ignoresSafeArea()
                .onTapGesture {
                    withAnimation(.easeOut(duration: 0.25)) {
                        isPresented = false
                    }
                }
            
            // iOS-style alert popup
            VStack(spacing: 0) {
                // Header with iOS-style spacing
                VStack(spacing: 12) {
                    // Icon
                    Image(systemName: getAlertIcon())
                        .font(.system(size: 40, weight: .medium))
                        .foregroundColor(.blue)
                        .padding(.top, 24)
                    
                    // Title and subtitle
                    VStack(spacing: 4) {
                        Text(alertType)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .multilineTextAlignment(.center)
                        
                        Text("custom_interval".localized)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding(.horizontal, 20)
                
                // Content area with iOS-style spacing
                VStack(spacing: 16) {
                    // Input section with iOS-style label
                    VStack(alignment: .leading, spacing: 8) {
                        Text("interval_value".localized + " (\(unit))")
                            .font(.footnote)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 4)
                        
                        TextField("enter_value_placeholder".localized, text: $customValue)
                            .keyboardType(alertType.lowercased().contains("distance") ? .decimalPad : .numberPad)
                            .font(.title3)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .multilineTextAlignment(.center)
                            .focused($isTextFieldFocused)
                            .onChange(of: customValue) { oldValue, newValue in
                                let isDistanceAlert = alertType.lowercased().contains("distance")
                                var newText = ""
                                var decimalFound = false

                                for char in newValue {
                                    if char.isNumber {
                                        newText.append(char)
                                    } else if char == "." && isDistanceAlert && !decimalFound {
                                        newText.append(char)
                                        decimalFound = true
                                    }
                                }
                                
                                // Prevent leading decimal point if it's the only character
                                if newText == "." {
                                    newText = "0."
                                }

                                if newText != customValue { // Check against the current state variable
                                    customValue = newText
                                }
                            }
                            .onAppear {
                                // Start with empty input field
                                customValue = ""
                                // Auto-focus with delay for smooth animation
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                                    isTextFieldFocused = true
                                }
                            }
                    }
                    
                    // Validation hints with iOS-style design
                    if integerOnly || validationRange != nil {
                        VStack(alignment: .leading, spacing: 4) {
                            if integerOnly {
                                Label("whole_numbers_only".localized, systemImage: "info.circle")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            if let range = validationRange {
                                let minStr = integerOnly ? String(Int(range.lowerBound)) : String(format: "%.1f", range.lowerBound)
                                let maxStr = integerOnly ? String(Int(range.upperBound)) : String(format: "%.1f", range.upperBound)
                                
                                Label(String(format: "valid_range".localized, minStr, maxStr), systemImage: "ruler")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    
                    // Quick preset selection with iOS-style chips
                    if !presets.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("preset_options".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .leading)
                            
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                                ForEach(presets, id: \.self) { preset in
                                    Button(action: {
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            customValue = formatValueForInput(preset)
                                        }
                                    }) {
                                        HStack(spacing: 2) {
                                            Text(formatPresetValue(preset))
                                                .font(.caption)
                                                .fontWeight(.medium)
                                            
                                            Text(unit)
                                                .font(.caption2)
                                                .foregroundColor(.secondary)
                                        }
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(
                                            Capsule()
                                                .fill(.blue.opacity(0.1))
                                        )
                                        .overlay(
                                            Capsule()
                                                .strokeBorder(.blue.opacity(0.3), lineWidth: 0.5)
                                        )
                                    }
                                    .buttonStyle(.plain)
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 20)
                
                // iOS-style action buttons
                Divider()
                
                HStack(spacing: 0) {
                    // Cancel button
                    Button("cancel".localized) {
                        withAnimation(.easeOut(duration: 0.25)) {
                            isPresented = false
                        }
                    }
                    .font(.body)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .contentShape(Rectangle())
                    
                    Divider()
                        .frame(height: 44)
                    
                    // Save button
                    Button("save".localized) {
                        validateAndSaveCustomValue()
                    }
                    .font(.body)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .contentShape(Rectangle())
                }
                .background(.clear)
            }
            .frame(width: 280)
            .background(
                RoundedRectangle(cornerRadius: 14)
                    .fill(.regularMaterial)
            )
            .scaleEffect(isPresented ? 1 : 1.1)
            .opacity(isPresented ? 1 : 0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPresented)
        }
        .alert("invalid_input".localized, isPresented: $showingValidationError) {
            Button("ok".localized, role: .cancel) { }
        } message: {
            Text(validationErrorMessage)
        }
    }
    
    private func validateAndSaveCustomValue() {
        guard let value = Double(customValue.trimmingCharacters(in: .whitespaces)), value > 0 else {
            validationErrorMessage = "please_enter_positive_number".localized
            showingValidationError = true
            return
        }
        
        if integerOnly {
            // Check if the input is a whole number
            if value != floor(value) {
                validationErrorMessage = "please_enter_whole_number".localized
                showingValidationError = true
                return
            }
        }
        
        // Check validation range if provided
        if let range = validationRange {
            if value < range.lowerBound || value > range.upperBound {
                let minStr = integerOnly ? String(Int(range.lowerBound)) : String(format: "%.1f", range.lowerBound)
                let maxStr = integerOnly ? String(Int(range.upperBound)) : String(format: "%.1f", range.upperBound)
                validationErrorMessage = String(format: "please_enter_value_between".localized, minStr, maxStr)
                showingValidationError = true
                return
            }
        }
        
        selectedValue = value
        customValue = ""
        withAnimation(.easeOut(duration: 0.2)) {
            isPresented = false
        }
    }
    
    private func getAlertIcon() -> String {
        switch alertType.lowercased() {
        case let type where type.contains("distance"):
            return "location"
        case let type where type.contains("time"):
            return "clock"
        case let type where type.contains("pace"):
            return "speedometer"
        case let type where type.contains("calorie"):
            return "flame"
        default:
            return "bell"
        }
    }
    
    private func formatValueForInput(_ value: Double) -> String {
        if integerOnly {
            return String(Int(value.rounded()))
        } else {
            // Remove trailing zeros for clean input
            return String(format: "%.10g", value)
        }
    }
    
    private func formatPresetValue(_ value: Double) -> String {
        let isDistanceAlert = alertType.lowercased().contains("distance")
        if isDistanceAlert {
            // Always show 1 decimal place for distance alerts
            return String(format: "%.1f", value)
        } else {
            // Use the provided formatter for other alerts
            return formatter(value)
        }
    }
}

#Preview {
    ZStack {
        // Sample background to show the popup effect
        LinearGradient(
            colors: [.blue.opacity(0.6), .purple.opacity(0.6)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        CustomIntervalPopup(
            alertType: "Distance Alerts",
            presets: [0.5, 1.0, 1.5, 2.0, 3.0, 5.0],
            unit: "km",
            formatter: { String(format: "%.1f", $0) },
            integerOnly: false,
            validationRange: 0.1...10.0,
            selectedValue: .constant(1.0),
            isPresented: .constant(true)
        )
    }
    .preferredColorScheme(.light)
}