import SwiftUI
import CoreLocation

struct SportTypeSelector: View {
    // Using SportType directly since it's in the same module
    @Binding var selectedSportType: SportType?
    @StateObject private var routeManager = RouteManager.shared
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(SportType.allCases, id: \.self) { sport in
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        // Clear any existing route data from previous workouts
                        routeManager.resetAllRouteData()
                        
                        selectedSportType = sport
                    }
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: sport.iconName)
                            .font(.system(size: 25, weight: .black))
                            .foregroundColor(.primary)
                            .frame(width: 30, height: 30)
                        Text(sport.rawValue.localized)
                            .font(.system(size: 14, weight: .black))
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity, alignment: .center)
                    }
                    .frame(maxWidth: 85)
                    .padding(.vertical, 8)
                    .background(selectedSportType == sport ?
                        Color.accentColor.opacity(0.35) : Color.clear)
                    .foregroundColor(selectedSportType == sport ?
                        Color.accentColor : .gray)
                }
                .buttonStyle(.plain)
                .overlay(
                    selectedSportType == sport ?
                    Rectangle()
                        .frame(height: 2)
                        .foregroundColor(Color.accentColor)
                        .padding(.horizontal, 4)
                        .transition(.scale)
                    : nil,
                    alignment: .bottom
                )
            }
        }
        .background(Color.gray.opacity(0.15))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        //.shadow(color: Color.black.opacity(0.3), radius: 3, x: 0, y: 3)
        .shadow(color: Color.black.opacity(0.4), radius: 10, x: 0, y: 5)
        .padding(.horizontal)
        .padding(.bottom, 16)
    }
}

#Preview {
    SportTypeSelector(selectedSportType: .constant(.run))
}
