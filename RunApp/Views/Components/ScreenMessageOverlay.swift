import SwiftUI

struct ScreenMessageOverlay: View {
    let isVisible: Bool
    let isScreenOn: Bool
    
    var body: some View {
        if isVisible {
            VStack {
                Spacer()
                Text(isScreenOn ? "Screen Always On" : "Screen Auto off")
                    .font(.title2.bold())
                    .foregroundColor(.primary)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial.opacity(0.5))
                    .cornerRadius(12)
                Spacer()
            }
        }
    }
}

#Preview {
    ScreenMessageOverlay(isVisible: true, isScreenOn: true)
}
