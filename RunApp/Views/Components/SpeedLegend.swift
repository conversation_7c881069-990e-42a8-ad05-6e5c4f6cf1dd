import SwiftUI

// MARK: - Speed Legend Component
struct SpeedLegend: View {
    let speedRange: SpeedRange
    let unitSystem: UnitSystem
    
    private let barWidth: CGFloat = 120
    private let barHeight: CGFloat = 12
    
    private var speedLabels: [(speed: Double, position: CGFloat)] {
        guard speedRange.quartiles.count >= 6 else { return [] }
        
        let speeds = [
            speedRange.quartiles[0], // Min
            speedRange.quartiles[2], // Median
            speedRange.quartiles[5]  // Max
        ]
        
        return speeds.enumerated().map { index, speed in
            let position = CGFloat(index) / CGFloat(speeds.count - 1) * barWidth
            return (speed: speed, position: position)
        }
    }
    
    var body: some View {
        VStack(alignment: .center, spacing: 10) {
            // Horizontal continuous color gradient bar
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: SpeedColorMapper.heatmapColors,
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(width: barWidth, height: barHeight)
                .clipShape(RoundedRectangle(cornerRadius: barHeight / 2))
                .overlay {
                    RoundedRectangle(cornerRadius: barHeight / 2)
                        .stroke(.white.opacity(0.4), lineWidth: 0.5)
                }
            
            // Speed labels positioned below the bar
            ZStack {
                ForEach(speedLabels.indices, id: \.self) { index in
                    let label = speedLabels[index]
                    
                    VStack(alignment: .center, spacing: 2) {
                        // Small tick mark
                        Rectangle()
                            .fill(.white.opacity(0.8))
                            .frame(width: 1, height: 4)
                        
                        // Show speed value only for leftmost (min) and rightmost (max)
                        if index == 0 || index == speedLabels.count - 1 {
                            Text(formatSpeedValue(label.speed))
                                .font(.caption2)
                                .fontWeight(.medium)
                                .foregroundStyle(.primary)
                                .shadow(color: .black.opacity(0.4), radius: 1, x: 0, y: 0)
                        }
                        
                        // Show unit only in the middle position
                        if index == 1 {
                            Text(unitSystem.speedUnit) // Display speed unit in middle position
                                .font(.caption2)
                                .foregroundStyle(.secondary)
                                .shadow(color: .black.opacity(0.4), radius: 0.5, x: 0, y: 0)
                        }
                    }
                    .position(x: label.position, y: 0)
                }
            }
            .frame(width: barWidth, height: 20)
        }
        .padding(.horizontal, 12)  // Keep left/right padding at 10
        .padding(.top, 4)
        .padding(.bottom,0)
        .background {
            RoundedRectangle(cornerRadius: 10)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.5), radius: 6, x: 0, y: 4)
        }
        .overlay {
            RoundedRectangle(cornerRadius: 10)
                .stroke(.white.opacity(0.3), lineWidth: 0.5)
        }
    }
    
    private func formatSpeedValue(_ speed: Double) -> String {
        let convertedSpeed = SpeedAnalyzer.convertSpeed(speed, to: unitSystem)
        return String(format: "%.1f", convertedSpeed)
    }
}

// MARK: - Compact Speed Legend (for smaller spaces)
struct CompactSpeedLegend: View {
    let speedRange: SpeedRange
    let unitSystem: UnitSystem
    
    private let barWidth: CGFloat = 80
    private let barHeight: CGFloat = 8
    
    var body: some View {
        VStack(alignment: .center, spacing: 6) {
            // Compact horizontal gradient bar
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: SpeedColorMapper.heatmapColors,
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .frame(width: barWidth, height: barHeight)
                .clipShape(RoundedRectangle(cornerRadius: barHeight / 2))
                .overlay {
                    RoundedRectangle(cornerRadius: barHeight / 2)
                        .stroke(.white.opacity(0.4), lineWidth: 0.5)
                }
            
            // Compact speed labels (min - max)
            HStack(alignment: .top) {
                Text(formatCompactSpeedValue(speedRange.min))
                    .font(.caption2)
                    .foregroundStyle(.secondary)
                
                Spacer()
                
                VStack(spacing: 2) {
                    Text(formatCompactSpeedValue(speedRange.max))
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundStyle(.primary)
                    Text(unitSystem.paceUnit)
                        .font(.system(size: 7))
                        .foregroundStyle(.secondary)
                        .shadow(color: .black.opacity(0.4), radius: 0.5, x: 0, y: 0)
                }
            }
            .frame(width: barWidth)
        }
        .padding(6)
        .background {
            RoundedRectangle(cornerRadius: 6)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
    }
    
    private func formatCompactSpeedValue(_ speed: Double) -> String {
        let convertedSpeed = SpeedAnalyzer.convertSpeed(speed, to: unitSystem)
        return String(format: "%.1f", convertedSpeed)
    }
}

// MARK: - Preview
#Preview("Speed Legend") {
    VStack(spacing: 20) {
        SpeedLegend(
            speedRange: SpeedRange(
                min: 2.0,
                max: 8.0,
                quartiles: [2.0, 3.5, 5.0, 6.5, 7.5, 8.0]
            ),
            unitSystem: .metric
        )
        
        CompactSpeedLegend(
            speedRange: SpeedRange(
                min: 2.0,
                max: 8.0,
                quartiles: [2.0, 3.5, 5.0, 6.5, 7.5, 8.0]
            ),
            unitSystem: .imperial
        )
    }
    .padding()
    .background(Color.blue.gradient)
}