import SwiftUI

struct MetronomeMessageOverlay: View {
    @ObservedObject var metronome: MetronomeManager
    
    var body: some View {
        if metronome.showingMessage {
            VStack {
                Spacer()
                Text(metronome.isEnabled ? NSLocalizedString("metronome.status.on", comment: "Shown when metronome is turned on") : NSLocalizedString("metronome.status.off", comment: "Shown when metronome is turned off"))
                    .font(.title2.bold())
                    .foregroundColor(.primary)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(.ultraThinMaterial.opacity(0.5))
                    .cornerRadius(12)
                Spacer()
            }
        }
    }
}

#Preview {
    MetronomeMessageOverlay(metronome: MetronomeManager.shared)
}
