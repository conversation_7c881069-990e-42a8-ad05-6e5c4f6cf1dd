import SwiftUI

struct WorkoutProcessingOverlay: View {
  let progress: Double
  let message: String
  @State private var pulseScale: CGFloat = 1.0
  @State private var rotationAngle: Double = 0

  var body: some View {
    ZStack {
      // Transparent background
      Color.clear
        .ignoresSafeArea()

      ZStack {
        // Background ring
        Circle()
          .stroke(Color.gray.opacity(0.2), lineWidth: 6)
          .frame(width: 80, height: 80)

        // Progress ring - red during 0-99%, green at 100%
        Circle()
          .trim(from: 0, to: progress)
          .stroke(
            LinearGradient(
              colors: progress >= 1.0
                ? [Color.green.opacity(0.8), Color.green]
                : [Color.red.opacity(0.8), Color.red],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            ),
            style: StrokeStyle(lineWidth: 6, lineCap: .round)
          )
          .frame(width: 80, height: 80)
          .rotationEffect(.degrees(-90))
          .animation(.easeOut(duration: 0.3), value: progress)

        // Checkmark only at 100%
        if progress >= 1.0 {
          Image(systemName: "checkmark")
            .font(.system(size: 20, weight: .bold))
            .foregroundStyle(Color.green)
            .scaleEffect(pulseScale)
            .animation(.spring(response: 0.5, dampingFraction: 0.6), value: pulseScale)
        }
      }
      .background(
        Circle()
          .fill(.ultraThinMaterial)
          .frame(width: 100, height: 100)
          .shadow(color: .black.opacity(0.1), radius: 15, x: 0, y: 8)
      )
    }
    .onAppear {
      if progress >= 1.0 {
        pulseScale = 1.2
      }
    }
    .onChange(of: progress) { _, newProgress in
      if newProgress >= 1.0 {
        pulseScale = 1.2
      }
    }
  }
}

#Preview {
  WorkoutProcessingOverlay(
    progress: 0.65,
    message: "Finalizing route data and syncing with Apple Health..."
  )
}
