import SwiftUI

struct ImageAdjustmentView: View {
    @Environment(\.dismiss) private var dismiss
    let image: UIImage
    let onSave: (UIImage) -> Void
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastScale: CGFloat = 1.0
    @State private var lastOffset: CGSize = .zero
    
    private let minScale: CGFloat = 0.5
    private let maxScale: CGFloat = 3.0
    private let imageScale: CGFloat = 0.9 // 90% of screen width
    
    // MARK: - Haptic Feedback
    private let selectionFeedback = UISelectionFeedbackGenerator()
    private let notificationFeedback = UINotificationFeedbackGenerator()
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                VStack {
                    Spacer()
                    
                    // Image container with gestures
                    ZStack {
                        // Background grid
                        Circle()
                            .stroke(.quaternary, lineWidth: 1)
                            .background(
                                Circle()
                                    .fill(.black.opacity(0.03))
                            )
                        
                        // Guide circles
                        Circle()
                            .stroke(.quaternary.opacity(0.5), style: StrokeStyle(lineWidth: 0.5, dash: [2]))
                            .scaleEffect(0.8)
                        
                        makeImageView()
                    }
                    .clipShape(Circle())
                    .overlay(Circle().stroke(.white.opacity(0.3), lineWidth: 2))
                    .frame(width: geometry.size.width * imageScale, height: geometry.size.width * imageScale)
                    .background(Color(.systemBackground).opacity(0.8))
                    .gesture(
                        SimultaneousGesture(
                            MagnificationGesture()
                                .onChanged { value in
                                    let delta = value / lastScale
                                    lastScale = value
                                    
                                    // Limit scale within bounds
                                    let newScale = scale * delta
                                    scale = min(maxScale, max(minScale, newScale))
                                }
                                .onEnded { _ in
                                    lastScale = 1.0
                                },
                            DragGesture()
                                .onChanged { value in
                                    let delta = CGSize(
                                        width: value.translation.width - lastOffset.width,
                                        height: value.translation.height - lastOffset.height
                                    )
                                    lastOffset = value.translation
                                    
                                    // Apply translation
                                    offset = CGSize(
                                        width: offset.width + delta.width,
                                        height: offset.height + delta.height
                                    )
                                }
                                .onEnded { _ in
                                    lastOffset = .zero
                                }
                        )
                    )
                    Spacer()
                    
                    // Controls
                    VStack(spacing: 16) {
                        Divider()
                            .padding(.bottom, 8)
                        VStack(spacing: 8) {
                            Text("\(Int(scale * 100))%")
                                .font(.system(.body, design: .rounded, weight: .medium))
                                .foregroundStyle(.secondary)
                                .monospacedDigit()
                            
                            Slider(
                                value: $scale,
                                in: minScale...maxScale,
                                step: 0.1
                            ) {
                                Text("Zoom")
                            } minimumValueLabel: {
                                Image(systemName: "minus.magnifyingglass")
                                    .foregroundStyle(.secondary)
                            } maximumValueLabel: {
                                Image(systemName: "plus.magnifyingglass")
                                    .foregroundStyle(.secondary)
                            }
                        }

                        Text("Drag to adjust position")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 12)
                    .background(.ultraThinMaterial)
                }
            }
            .navigationTitle("Edit Photo")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel", role: .cancel) {
                        selectionFeedback.selectionChanged()
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveAdjustedImage()
                    }
                }
            }
        }
        .onAppear {
            selectionFeedback.prepare()
            notificationFeedback.prepare()
        }
    }
    
    @ViewBuilder
    private func makeImageView() -> some View {
        Image(uiImage: image)
            .resizable()
            .aspectRatio(contentMode: .fit)
            .scaleEffect(scale)
            .offset(offset)
    }
    
    private func saveAdjustedImage() {
        // Create snapshot of the adjusted image at the same size as the editor view
        let size = UIScreen.main.bounds.width * imageScale
        let renderer = ImageRenderer(content:
            makeImageView()
                .frame(width: size, height: size)
                .clipShape(Circle())
        )
        renderer.scale = UIScreen.main.scale // Ensure high quality output
        
        if let processedImage = renderer.uiImage {
            onSave(processedImage)
            notificationFeedback.notificationOccurred(.success)
            dismiss()
        } else {
            notificationFeedback.notificationOccurred(.error)
        }
    }
}

#Preview {
    ImageAdjustmentView(
        image: UIImage(systemName: "person.circle.fill")!
    ) { _ in }
}
