import SwiftUI
import AudioToolbox

struct ActivityControls: View {
    // Using SportType directly since it's in the same module
    @StateObject private var metronome = MetronomeManager.shared
    
    let isRunning: Bool
    let isPaused: Bool
    let selectedSportType: SportType?
    let onStart: () -> Void
    let onPause: () -> Void
    let onResume: () -> Void
    let onEnd: () -> Void
    let onShowAnalysis: () -> Void
    let onShowSettings: () -> Void
    
    var body: some View {
        if !isRunning && !isPaused {
            // Initial state controls
            HStack(spacing: 40) {
                // Analysis Button
                Button(action: onShowAnalysis) {
                    VStack(spacing: 4) {
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [Color.blue.opacity(0.8), Color.blue],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 45, height: 45)
                            Image(systemName: "chart.bar.fill")
                                .foregroundColor(.white)
                        }
                        .shadow(color: Color.black.opacity(0.8), radius: 5, x: 0, y: 3)
                        Text("activities".localized)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.primary)
                    }
                }
                
                // Start Button
                Button(action: onStart) {
                    Image(systemName: "play.circle.fill")
                        .font(.system(size: 80))
                        .foregroundStyle(Color.green)
                        .opacity(selectedSportType != nil ? 1.0 : 0.3)
                        .shadow(color: Color.black.opacity(0.4), radius: 5, x: 0, y: 3)
                }
                .disabled(selectedSportType == nil)
                
                // Main Metronome Button
                VStack(spacing: 4) {
                    MetronomeButton(inWorkoutMode: false, isPaused: isPaused)
                    Text("metronome".localized)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.primary)
                }
            }
        } else if isPaused {
            // Paused state controls
            HStack(spacing: 40) {
                // Settings Button
                Button(action: onShowSettings) {
                    Circle()
                        .fill(Color.gray)
                        .frame(width: 80, height: 80)
                        .overlay {
                            Image(systemName: "gearshape.fill")
                                .font(.system(size: 30, weight: .bold))
                                .foregroundColor(.white)
                        }
                        .shadow(color: Color.black.opacity(0.4), radius: 5, x: 0, y: 3)
                }
                
                // Resume Button
                Button(action: onResume) {
                    Circle()
                        .fill(Color.orange)
                        .frame(width: 80, height: 80)
                        .overlay {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 30, weight: .bold))
                                .foregroundColor(.white)
                        }
                        .shadow(color: Color.black.opacity(0.4), radius: 5, x: 0, y: 3)
                }
                
                // Stop Button
                Button(action: onEnd) {
                    Circle()
                        .fill(Color.red)
                        .frame(width: 80, height: 80)
                        .overlay {
                            Image(systemName: "stop.fill")
                                .font(.system(size: 30, weight: .bold))
                                .foregroundColor(.white)
                        }
                        .shadow(color: Color.black.opacity(0.4), radius: 5, x: 0, y: 3)
                }
            }
            .transition(.opacity)
        } else {
            // Running state controls
            Button(action: onPause) {
                Circle()
                    .fill(Color.yellow)
                    .frame(width: 85, height: 85)
                    .overlay {
                        Image(systemName: "pause.fill")
                            .font(.system(size: 30, weight: .bold))
                            .foregroundColor(.white)
                    }
                    .shadow(color: Color.black.opacity(0.4), radius: 5, x: 0, y: 3)
            }
        }
    }
}

#Preview {
    VStack {
        ActivityControls(
            isRunning: false,
            isPaused: false,
            selectedSportType: .run,
            onStart: {},
            onPause: {},
            onResume: {},
            onEnd: {},
            onShowAnalysis: {},
            onShowSettings: {}
        )
        
        ActivityControls(
            isRunning: true,
            isPaused: false,
            selectedSportType: .run,
            onStart: {},
            onPause: {},
            onResume: {},
            onEnd: {},
            onShowAnalysis: {},
            onShowSettings: {}
        )
        
        ActivityControls(
            isRunning: true,
            isPaused: true,
            selectedSportType: .run,
            onStart: {},
            onPause: {},
            onResume: {},
            onEnd: {},
            onShowAnalysis: {},
            onShowSettings: {}
        )
    }
    .padding()
}
