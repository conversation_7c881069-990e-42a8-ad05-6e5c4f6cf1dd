import SwiftUI
import MapKit

struct MapControls: View {
    @Binding var selectedMapStyle: ContentView.MapStyleChoice
    @Binding var isFollowingOrientation: Bool
    let onCenterLocation: () -> Void
    
    var body: some View {
        VStack(spacing: 15) {
            // Map Style Button
            Menu {
                ForEach(ContentView.MapStyleChoice.allCases, id: \.self) { style in
                    Button(action: { selectedMapStyle = style }) {
                        if selectedMapStyle == style {
                            Label(style.rawValue.capitalized, systemImage: "checkmark")
                        } else {
                            Text(style.rawValue.capitalized)
                        }
                    }
                }
            } label: {
                Image(systemName: "map.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 45, height: 45)
                    .background {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color.purple.opacity(0.5), Color.purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    }
                    .shadow(color: Color.black.opacity(0.8), radius: 5, x: 0, y: 3)
            }
            
            // Orientation Toggle Button
            Button(action: { isFollowingOrientation.toggle() }) {
                Image(systemName: isFollowingOrientation ? "location.north.circle.fill" : "location.north.circle")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 45, height: 45)
                    .rotationEffect(.degrees(isFollowingOrientation ? 45 : 0))
                    .background {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color.purple.opacity(0.5), Color.purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    }
                    .shadow(color: Color.black.opacity(0.8), radius: 5, x: 0, y: 3)
            }
            
            // Center on Location Button
            Button(action: onCenterLocation) {
                Image(systemName: "dot.scope")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 45, height: 45)
                    .background {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color.purple.opacity(0.5), Color.purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    }
                    .shadow(color: Color.black.opacity(0.8), radius: 5, x: 0, y: 3)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
        .padding(.horizontal, 16)
        .padding(.top, 5) // Add more top padding to avoid status bar
    }
}
