import SwiftUI
import SwiftData

struct UnitsSettingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @State private var selectedUnit: UnitSystem = .metric  // Initialize with default value
    
    var body: some View {
        NavigationView {
            Form {
                Picker("unit_system".localized, selection: $selectedUnit) {
                    ForEach(UnitSystem.allCases, id: \.self) { unit in
                        Text(unit.rawValue.localized)
                    }
                }
                .pickerStyle(.inline)
            }
            .navigationTitle("units".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        saveUnit()
                        dismiss()
                    }
                }
            }
            .onAppear {
                // Set the initial value when the view appears
                selectedUnit = profile.first?.units ?? .metric
            }
        }
    }
    
    private func saveUnit() {
        if let userProfile = profile.first {
            userProfile.units = selectedUnit
        } else {
            let newProfile = UserProfile(units: selectedUnit)
            modelContext.insert(newProfile)
        }
    }
}
