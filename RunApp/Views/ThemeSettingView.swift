import SwiftUI
import Foundation
// Removed incorrect import RunApp

struct ThemeSettingView: View {
    @Bindable private var themeManager = ThemeManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        List {
            ForEach(Theme.allCases, id: \.self) { theme in
                HStack {
                    Text(theme.displayName)
                    Spacer()
                    if themeManager.currentTheme == theme {
                        Image(systemName: "checkmark")
                            .foregroundColor(.accentColor)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    themeManager.setTheme(theme)
                    dismiss()
                }
            }
        }
        .navigationTitle("theme".localized) // Revert to using .localized
    }
}
