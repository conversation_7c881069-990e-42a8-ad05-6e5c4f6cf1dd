import SwiftUI
import SwiftData

struct NameSettingView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) var dismiss
    @Query private var profile: [UserProfile]
    
    @State private var nameString: String = ""
    @State private var showingAlert = false
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("enter_your_name".localized)) {
                    TextField("name".localized, text: $nameString)
                }
            }
            .navigationTitle("name".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        saveName()
                    }
                }
            }
        }
        .alert("invalid_name".localized, isPresented: $showingAlert) {
            Button("ok".localized, role: .cancel) { }
        } message: {
            Text("please_enter_valid_name".localized)
        }
        .onAppear {
            if let userProfile = profile.first {
                nameString = userProfile.name ?? ""
            }
        }
    }
    
    private func saveName() {
        let trimmedName = nameString.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else {
            showingAlert = true
            return
        }
        
        if let userProfile = profile.first {
            userProfile.name = trimmedName
        } else {
            let newProfile = UserProfile(name: trimmedName)
            modelContext.insert(newProfile)
        }
        
        try? modelContext.save()
        dismiss()
    }
}

#Preview {
    NameSettingView()
        .modelContainer(for: UserProfile.self, inMemory: true)
}
