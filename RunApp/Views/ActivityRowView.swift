import CoreLocation
import MapKit
import SwiftData
import SwiftUI

// MARK: - ActivityRowView
struct ActivityRowView: View {
  let activity: RunActivity
  @State private var locationName: String = ""
  @Query private var profile: [UserProfile]

  private var unitSystem: UnitSystem {
    profile.first?.units ?? .metric
  }

  var body: some View {
    VStack(alignment: .leading, spacing: 0) {
      // Combined header with location and time
      HStack(alignment: .top) {
        // Location and run type
        HStack(spacing: 12) {
          Image(systemName: activity.sportType.iconName)
            .font(.title)
            .foregroundStyle(.blue)

          VStack(alignment: .leading, spacing: 4) {
            Text(
              "\(activity.timeOfDay.localized) \(activity.sportType.rawValue.localized.capitalized)"
            )
            .font(.headline)
            .foregroundStyle(.primary)
            if !locationName.isEmpty {
              Text(locationName)
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .lineLimit(1)
            }
          }
        }

        Spacer()

        // Date and time
        VStack(alignment: .trailing, spacing: 4) {
          Text(activity.formattedDate)
            .font(.subheadline)
            .foregroundStyle(.primary)
          Text("\(activity.formattedStartTime) - \(activity.formattedEndTime)")
            .font(.caption)
            .foregroundStyle(.secondary)
        }
      }
      .padding()
      .background(Color(.secondarySystemGroupedBackground))

      // Stats - Using reusable component
      ActivityStatsRow(
        activity: activity,
        unitSystem: unitSystem,
        dividerHeight: 30
      )
      .padding(.horizontal, 8)
      .padding(.bottom)
      .background(Color(.secondarySystemGroupedBackground))
    }
    .clipShape(RoundedRectangle(cornerRadius: 16))
    .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
    .overlay(
      NavigationLink(destination: ActivityDetailView(activity: activity)) {
        EmptyView()
      }
      .opacity(0)
    )
    .contentShape(Rectangle())
    .task {
      await loadLocationName()
    }
  }

  private func loadLocationName() async {
    guard !activity.route.isEmpty, let firstLocation = activity.route.first else {
      locationName = "Unknown Location"
      return
    }

    let geocoder = CLGeocoder()
    let location = CLLocation(latitude: firstLocation.latitude, longitude: firstLocation.longitude)

    do {
      let placemarks = try await geocoder.reverseGeocodeLocation(location)
      if let placemark = placemarks.first {
        var components: [String] = []

        if let thoroughfare = placemark.thoroughfare {
          components.append(thoroughfare)
        }
        if let subLocality = placemark.subLocality {
          components.append(subLocality)
        }
        if let locality = placemark.locality {
          components.append(locality)
        }
        if let administrativeArea = placemark.administrativeArea {
          components.append(administrativeArea)
        }

        locationName = components.joined(separator: ", ")
      }
    } catch {
      locationName = "Location Unavailable"
    }
  }
}

// MARK: - StatView
struct StatView: View {
  let value: String
  let label: String

  private var valueComponents: (value: String, unit: String) {
    // Handle different formats
    if value.contains("/") {
      // Pace format like "4'18"/km"
      let components = value.components(separatedBy: "/")
      if components.count == 2 {
        return (components[0], "/\(components[1])")
      }
    } else if value.contains(" ") {
      // Distance format like "1.19 km"
      let components = value.components(separatedBy: " ")
      if components.count == 2 {
        return (components[0], components[1])
      }
    }
    // No unit (duration, calories)
    return (value, "")
  }

  var body: some View {
    VStack(spacing: 4) {
      HStack(alignment: .bottom, spacing: 2) {
        Text(valueComponents.value)
          .font(.title3)
          .fontWeight(.bold)
          .foregroundStyle(.primary)
          .minimumScaleFactor(0.7)
          .lineLimit(1)

        if !valueComponents.unit.isEmpty {
          Text(valueComponents.unit)
            .font(.caption)
            .fontWeight(.medium)
            .foregroundStyle(.secondary)
        }
      }

      Text(label)
        .font(.caption2)
        .foregroundStyle(.secondary)
    }
    .frame(maxWidth: .infinity)
  }
}

// MARK: - ActivityStatsRow (Reusable Component)
private struct ActivityStatsRow: View {
  let activity: RunActivity
  let unitSystem: UnitSystem
  let dividerHeight: CGFloat

  var body: some View {
    HStack(spacing: 8) {
      StatView(
        value: activity.formattedDistance(for: unitSystem),
        label: "distance".localized
      )

      Divider()
        .frame(height: dividerHeight)

      StatView(
        value: activity.formattedDuration,
        label: "time".localized
      )

      Divider()
        .frame(height: dividerHeight)

      StatView(
        value: activity.formattedPace(for: unitSystem),
        label: "avg_pace".localized
      )

      Divider()
        .frame(height: dividerHeight)

      StatView(
        value: {
          let activeCalories = activity.activeCalories
          let totalCalories = activity.calories
          print(
            "🔥 CALORIE DEBUG: ActivityRowView - activeCalories: \(activeCalories), totalCalories: \(totalCalories)"
          )
          let displayValue = activeCalories > 0 ? activeCalories : totalCalories
          print("🔥 CALORIE DEBUG: ActivityRowView - displaying: \(displayValue)")
          return String(format: "%.0f", displayValue)
        }(),
        label: "calories".localized
      )
    }
  }
}

// MARK: - ActivityMap View
private struct ActivityMap: View {
  let activity: RunActivity  // Keep for start/end markers
  let speedSegments: [SpeedSegment]  // Receive processed data
  @Binding var mapPosition: MapCameraPosition
  @Query private var profile: [UserProfile]

  private var unitSystem: UnitSystem {
    profile.first?.units ?? .metric
  }

  var body: some View {
    // The view no longer calculates anything. It just renders.
    Map(position: $mapPosition) {
      if !speedSegments.isEmpty {
        ForEach(speedSegments.indices, id: \.self) { index in
          let segment = speedSegments[index]
          MapPolyline(coordinates: segment.coordinates)
            .stroke(
              segment.color, style: StrokeStyle(lineWidth: 8, lineCap: .round, lineJoin: .miter))
        }
      }

      // Markers still use the original route for accuracy
      if let start = activity.route.first {
        Marker(
          NSLocalizedString("map.marker.start", comment: "Label for the start marker on map"),
          coordinate: start.clCoordinate
        )
        .tint(.green)
      }

      if let end = activity.route.last, activity.route.count > 1 {
        Marker(
          NSLocalizedString("map.marker.end", comment: "Label for the end marker on map"),
          coordinate: end.clCoordinate
        )
        .tint(.red)
      }
    }
    .mapControls {
      MapCompass()
      MapScaleView()
      MapPitchToggle()
    }
    .mapStyle(.standard)
    .overlay(alignment: .topTrailing) {
      // Speed legend - only show if we have valid speed data
      if !speedSegments.isEmpty, let firstSegment = speedSegments.first, firstSegment.speed != nil {
        // Calculate speed range from segments for legend
        let speeds = speedSegments.compactMap { $0.speed }
        if !speeds.isEmpty {
          let sortedSpeeds = speeds.sorted()
          let speedRange = SpeedRange(
            min: sortedSpeeds.first ?? 0,
            max: sortedSpeeds.last ?? 0,
            quartiles: calculateQuartiles(sortedSpeeds)
          )
          SpeedLegend(speedRange: speedRange, unitSystem: unitSystem)
            .padding(.trailing, 8)
            .padding(.top, 8)
        }
      }
    }
  }

  // Helper function for quartiles calculation
  private func calculateQuartiles(_ sortedSpeeds: [Double]) -> [Double] {
    guard sortedSpeeds.count >= 4 else {
      return sortedSpeeds
    }

    let count = sortedSpeeds.count
    let q1Index = count / 4
    let q2Index = count / 2  // Median
    let q3Index = (3 * count) / 4
    let q4Index = (9 * count) / 10  // 90th percentile

    return [
      sortedSpeeds[0],  // Min (0th percentile)
      sortedSpeeds[q1Index],  // 25th percentile
      sortedSpeeds[q2Index],  // 50th percentile (median)
      sortedSpeeds[q3Index],  // 75th percentile
      sortedSpeeds[q4Index],  // 90th percentile
      sortedSpeeds[count - 1],  // Max (100th percentile)
    ]
  }
}

// MARK: - ActivityDetailView
struct ActivityDetailView: View {
  @StateObject private var viewModel: ActivityDetailViewModel
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dismiss) private var dismiss
  @Environment(\.scenePhase) private var scenePhase
  @State private var showDeleteConfirmation = false

  // The map position is now managed by the view, but initialized from the ViewModel
  @State private var mapPosition: MapCameraPosition = .automatic  // Use a non-blocking default

  // The initializer must be lightweight
  init(activity: RunActivity) {
    _viewModel = StateObject(wrappedValue: ActivityDetailViewModel(activity: activity))
  }

  var body: some View {
    Group {
      if viewModel.isLoading {
        // --- LOADING STATE ---
        ProgressView("Loading Workout...")
          .frame(maxWidth: .infinity, maxHeight: .infinity)
      } else {
        // Only render complex UI when NOT in background to prevent watchdog timeout
        if scenePhase != .background {
          // Content based on selected tab - no segmented control here
          TabView(selection: $viewModel.selectedTab) {
            // Map View (existing)
            MapViewContent(
              activity: viewModel.activityReference,
              speedSegments: viewModel.speedSegments,
              mapPosition: $mapPosition,
              locationName: viewModel.locationName
            )
            .tag(ActivityDetailTab.map)

            // ✅ New Stats View
            ActivityStatsView(viewModel: viewModel)
              .tag(ActivityDetailTab.stats)
          }
          .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        } else {
          // Minimal placeholder in background to prevent watchdog timeout
          Rectangle()
            .fill(Color.clear)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
      }
    }
    .navigationBarTitleDisplayMode(.inline)
    .toolbar {
      // Segmented Control in the center of the toolbar
      ToolbarItem(placement: .principal) {
        Picker("View", selection: $viewModel.selectedTab) {
          ForEach(ActivityDetailTab.allCases, id: \.self) { tab in
            Label(tab.rawValue, systemImage: tab.systemImage)
              .tag(tab)
          }
        }
        .pickerStyle(SegmentedPickerStyle())
        .frame(maxWidth: .infinity)
        .background(.clear)
      }

      // Delete button on the trailing side
      ToolbarItem(placement: .topBarTrailing) {
        Button(role: .destructive) {
          showDeleteConfirmation = true
        } label: {
          if viewModel.isDeleting {
            ProgressView()
              .scaleEffect(0.8)
          } else {
            Image(systemName: "trash")
              .foregroundStyle(.red)
          }
        }
        .disabled(viewModel.isDeleting)
      }
    }
    .confirmationDialog(
      "delete_activity".localized,
      isPresented: $showDeleteConfirmation
    ) {
      Button("delete".localized, role: .destructive) {
        Task { @MainActor in
          await viewModel.deleteActivity(
            modelContext: modelContext, dismiss: dismiss.callAsFunction)
        }
      }
    } message: {
      Text("are_you_sure_deletion".localized)
    }
    .alert("Deletion Error", isPresented: $viewModel.showDeletionError) {
      Button("OK") {}
    } message: {
      Text(viewModel.deletionErrorMessage)
    }
    .task {
      // --- TRIGGER ASYNC LOADING ---
      await viewModel.loadActivityData()
    }
    // This modifier reacts to the ViewModel without blocking the UI
    .onReceive(viewModel.$mapRegion) { newRegion in
      if let region = newRegion {
        mapPosition = .region(region)
      }
      // Don't reset to .automatic when nil - preserve current position
      // This prevents zoom reset during state transitions
    }
    .onChange(of: scenePhase) { _, newPhase in
      // Handle background state changes to prevent watchdog timeout
      viewModel.setBackgroundState(newPhase == .background)

      if newPhase == .background {
        // Handle background transition during deletion
        if viewModel.isDeleting {
          // Deletion will complete in background
        }
      }
    }
    .onDisappear {
      viewModel.onDisappear()
    }
  }
}

struct MapViewContent: View {
  let activity: RunActivity
  let speedSegments: [SpeedSegment]
  @Binding var mapPosition: MapCameraPosition
  let locationName: String
  @Environment(\.scenePhase) private var scenePhase

  var body: some View {
    ZStack(alignment: .bottom) {
      // Only render ActivityMap when not in background to prevent heavy GPU operations
      if scenePhase != .background {
        ActivityMap(
          activity: activity,
          speedSegments: speedSegments,
          mapPosition: $mapPosition
        )
      } else {
        // Lightweight placeholder for background state
        Rectangle()
          .fill(Color.gray.opacity(0.1))
          .overlay {
            Text("Map")
              .foregroundStyle(.secondary)
          }
      }

      StatsCard(
        activity: activity,
        locationName: locationName
      )
    }
    .ignoresSafeArea(edges: .bottom)
  }
}

// MARK: - StatsCard
private struct StatsCard: View {
  let activity: RunActivity
  let locationName: String
  @Query private var profile: [UserProfile]

  private var unitSystem: UnitSystem {
    profile.first?.units ?? .metric
  }

  var body: some View {
    VStack(spacing: 0) {
      RoundedRectangle(cornerRadius: 2.5)
        .fill(.white.opacity(0.3))
        .frame(width: 40, height: 5)
        .padding(.vertical, 8)

      VStack(spacing: 12) {
        HStack {
          VStack(alignment: .leading, spacing: 4) {
            Text(
              "\(activity.timeOfDay.localized) \(activity.sportType.rawValue.localized.capitalized)"
            )
            .font(.title3)
            .fontWeight(.bold)
            if !locationName.isEmpty {
              Text(locationName)
                .font(.subheadline)
                .foregroundStyle(.secondary)
            }
          }

          Spacer()

          VStack(alignment: .trailing, spacing: 4) {
            Text(activity.formattedDate)
              .font(.subheadline)
              .foregroundStyle(.primary)
            Text("\(activity.formattedStartTime) - \(activity.formattedEndTime)")
              .font(.caption)
              .foregroundStyle(.secondary)
          }
        }

        Divider()
          .background(.white.opacity(0.2))

        // Using reusable component
        ActivityStatsRow(
          activity: activity,
          unitSystem: unitSystem,
          dividerHeight: 40
        )
      }
      .padding(.horizontal)
      .padding(.bottom)
    }
    .background {
      Rectangle()
        .fill(.ultraThinMaterial)
        .overlay {
          LinearGradient(
            colors: [
              .white.opacity(0.3),
              .white.opacity(0.1),
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
          )
        }
    }
    .overlay {
      RoundedRectangle(cornerRadius: 0)
        .stroke(.white.opacity(0.2), lineWidth: 1)
    }
  }
}
