import SwiftUI
import SwiftData

struct AppView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var profiles: [UserProfile]
    @State var isAuthenticated = false
    
    var body: some View {
        Group {
            if isAuthenticated {
                ContentView()
            } else {
                ContentView()
                //AuthView()
            }
        }
        .task {
            // Load or initialize user profile
            await loadUserProfile()
        }
    }
    
    private func loadUserProfile() async {
        // Check if we already have a profile
        if profiles.isEmpty {
            // Create default profile
            let profile = UserProfile()
            modelContext.insert(profile)
            
            do {
                try modelContext.save()
            } catch {
                print("Error saving initial profile: \(error)")
            }
        }
        
        // Set authenticated state
        isAuthenticated = true
    }
}

#Preview {
    AppView()
        .modelContainer(for: UserProfile.self)
}
