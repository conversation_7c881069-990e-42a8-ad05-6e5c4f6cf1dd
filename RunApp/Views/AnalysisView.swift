import SwiftUI
import Charts
import SwiftData
import CoreLocation

// Import local models
@preconcurrency import _PhotosUI_SwiftUI

struct AnalysisView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.dynamicTypeSize) private var dynamicTypeSize
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    
    // Performance optimization: Remove loading ALL activities at startup
    @State private var activities: [RunActivity] = []
    @State private var isLoadingData: Bool = false
    
    @State private var selectedPeriod: TimePeriod = .sevenDays
    @State private var selectedMetric: ChartMetric = .distance
    @State private var selectedActivityType: ActivityType = .run
    @State private var selectedDataPoint: ActivityDataPoint?
    @State private var chartData: [ActivityDataPoint] = [] // Used for allTime view only
    @State private var weekOffset: Int = 0
    @State private var monthOffset: Int = 0
    @State private var yearOffset: Int = 0
    
    // 🚀 NEW: Unified dynamic caching system
    @State private var availableOffsetRange: ClosedRange<Int> = -50...0  // Start with 50 periods back
    @State private var chartDataCache: [Int: [ActivityDataPoint]] = [:]
    
    private var timeRange: AnalysisTimeRange {
        AnalysisTimeRange(
            selectedPeriod: selectedPeriod,
            weekOffset: weekOffset,
            monthOffset: monthOffset,
            yearOffset: yearOffset,
            activities: activities
        )
    }
    
    // 🚀 NEW: Dynamic chart navigation properties
    private var currentOffsetBinding: Binding<Int> {
        switch selectedPeriod {
        case .sevenDays:
            return $weekOffset
        case .oneMonth:
            return $monthOffset
        case .oneYear:
            return $yearOffset
        case .allTime:
            return .constant(0)
        }
    }
    
    private var availableOffsets: [Int] {
        Array(availableOffsetRange)
    }
    
    private var filteredActivities: [RunActivity] {
        // Filter activities for the current viewing period (e.g., current week)
        let calendar = Calendar.current
        let now = Date()
        
        let (startDate, endDate): (Date, Date)
        
        switch selectedPeriod {
        case .sevenDays:
            // Get the current week being viewed based on weekOffset
            let currentComponents = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now)
            let currentWeekStart = calendar.date(from: currentComponents)!
            let weekStart = calendar.date(byAdding: .weekOfYear, value: weekOffset, to: currentWeekStart)!
            let weekEnd = calendar.date(byAdding: .day, value: 7, to: weekStart)!
            startDate = weekStart
            endDate = weekEnd
            
        case .oneMonth:
            // Get the current month being viewed based on monthOffset
            let currentComponents = calendar.dateComponents([.year, .month], from: now)
            let currentMonthStart = calendar.date(from: currentComponents)!
            let monthStart = calendar.date(byAdding: .month, value: monthOffset, to: currentMonthStart)!
            let nextMonthStart = calendar.date(byAdding: .month, value: 1, to: monthStart)!
            startDate = monthStart
            endDate = nextMonthStart
            
        case .oneYear:
            // Get the current year being viewed based on yearOffset
            let currentComponents = calendar.dateComponents([.year], from: now)
            let currentYearStart = calendar.date(from: currentComponents)!
            let yearStart = calendar.date(byAdding: .year, value: yearOffset, to: currentYearStart)!
            let nextYearStart = calendar.date(byAdding: .year, value: 1, to: yearStart)!
            startDate = yearStart
            endDate = nextYearStart
            
        case .allTime:
            // For all time, show all activities (no date filtering)
            return activities.sorted { $0.startTime > $1.startTime }
        }
        
        // Filter activities within the current viewing period
        let filtered = activities.filter { activity in
            activity.startTime >= startDate && activity.startTime < endDate
        }.sorted { $0.startTime > $1.startTime }
        
        return filtered
    }

    enum TimePeriod: String, CaseIterable {
        case sevenDays = "1w"
        case oneMonth = "1m"
        case oneYear = "1y"
        case allTime = "all"
        
        var dataPoints: Int {
            switch self {
            case .sevenDays: return 7
            case .oneMonth: return 30
            case .oneYear: return 12 // months
            case .allTime: return 10 // years (dynamic based on data)
            }
        }
        
        var xAxisFormat: Date.FormatStyle {
            switch self {
            case .sevenDays: return .dateTime.month(.defaultDigits).day(.defaultDigits)
            case .oneMonth: return .dateTime.day()
            case .oneYear: return .dateTime.month(.abbreviated)
            case .allTime: return .dateTime.year()
            }
        }
        
        var strideComponent: Calendar.Component {
            switch self {
            case .sevenDays, .oneMonth: return .day
            case .oneYear: return .month
            case .allTime: return .year
            }
        }
        
        var strideValue: Int {
            switch self {
            case .sevenDays: return 1
            case .oneMonth: return 1
            case .oneYear: return 1
            case .allTime: return 1
            }
        }
    }
    
    enum ActivityType: String, CaseIterable {
        case run = "Run"
        case walk = "Walk"
        case hike = "Hike"
        case bike = "Bike"
        
        var sportType: SportType {
            switch self {
            case .run: return .run
            case .walk: return .walk
            case .hike: return .hike
            case .bike: return .bike
            }
        }
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 16) {
                    // Activity Type and Metric Selection
                    AnalysisHeaderView(
                        selectedActivityType: $selectedActivityType,
                        selectedMetric: $selectedMetric
                    )
                    
                    // Time Period Selection
                    TimePeriodPickerView(selectedPeriod: $selectedPeriod)
                    
                    // 🚀 SWIPEABLE CHART: Dynamic unlimited historical data with swipe navigation
                    GeometryReader { geometry in
                        if selectedPeriod == .allTime {
                            // All Time view - single chart
                            ActivityChart(
                                chartData: chartData,
                                selectedPeriod: selectedPeriod,
                                selectedMetric: selectedMetric,
                                selectedDataPoint: $selectedDataPoint
                            )
                            .frame(width: geometry.size.width - 32)
                            .padding(.horizontal)
                        } else {
                            // Swipeable TabView for week/month/year navigation
                            TabView(selection: currentOffsetBinding) {
                                ForEach(availableOffsets, id: \.self) { offset in
                                    ActivityChart(
                                        chartData: generateChartDataForOffset(offset),
                                        selectedPeriod: selectedPeriod,
                                        selectedMetric: selectedMetric,
                                        selectedDataPoint: $selectedDataPoint
                                    )
                                    .frame(width: geometry.size.width - 32)
                                    .tag(offset)
                                }
                            }
                            .tabViewStyle(.page(indexDisplayMode: .never))
                            .onChange(of: currentOffsetBinding.wrappedValue) { _, newOffset in
                                selectedDataPoint = nil
                                // Expand range if user is approaching boundaries
                                expandRangeIfNeeded(newOffset)
                            }
                        }
                    }
                    .frame(height: 200)
                    
                    // Period indicator below chart
                    PeriodIndicatorView(
                        selectedPeriod: selectedPeriod,
                        weekOffset: weekOffset,
                        monthOffset: monthOffset,
                        yearOffset: yearOffset
                    )
                    
                    // Summary Panel with enhanced visuals
                    let metrics = AnalysisMetrics(
                        activities: filteredActivities,
                        selectedMetric: selectedMetric,
                        unitSystem: profile.first?.units ?? .metric,
                        divisor: timeRange.getDivisor()
                    )
                    
                    ActivitySummaryPanel(
                        selectedMetric: selectedMetric,
                        totalValue: metrics.formatTotalMetric(),
                        averageValue: metrics.formatAverageMetric(),
                        averageLabel: timeRange.getAverageLabel()
                    )
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 15)
                            .fill(Color(.secondarySystemGroupedBackground))
                            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 2)
                    )
                    .padding(.horizontal)

                    // Activity List Section
                    ActivityListSection(activities: filteredActivities)
                        .padding(.horizontal, 5)
                }
                .padding(.vertical)
                .padding(.horizontal)
            }
            .overlay {
                // Performance optimization: Show loading state during data fetch
                if isLoadingData {
                    Color(.systemGroupedBackground)
                        .overlay {
                            VStack(spacing: 16) {
                                ProgressView()
                                    .scaleEffect(1.2)
                                Text("Loading activities...")
                                    .font(.subheadline)
                                    .foregroundStyle(.secondary)
                            }
                        }
                }
            }
            .navigationTitle("improve_with_personalized_data".localized)
            .navigationBarTitleDisplayMode(.inline)
            .background(Color(.systemGroupedBackground))
            .toolbar {
                AnalysisToolbarView(
                    dismiss: dismiss,
                    onGenerateTestData: {
                        Task {
                            // Generate last 26 months of data
                            let calendar = Calendar.current
                            let endDate = Date()
                            let startDate = calendar.date(byAdding: .month, value: -26, to: endDate)!
                            
                            let generator = TestDataGenerator(modelContext: modelContext)
                            await generator.generateActivities(startDate: startDate, endDate: endDate)
                            
                            // Update the chart after generating data
                            updateChartData()
                        }
                    },
                    onGenerateLargeTestData: {
                        Task {
                            let generator = TestDataGenerator(modelContext: modelContext)
                            await generator.generateLargeActivitiesForTesting()
                            
                            // Update the chart after generating data
                            updateChartData()
                        }
                    }
                )
            }
        }
        .onChange(of: selectedPeriod) { oldValue, newValue in
            // Performance optimization: Reset offsets and clear cache for new period
            resetOffsetsForPeriod(newValue)
            clearAllCaches()
            updateChartData()
        }
        .onChange(of: selectedActivityType) { oldValue, newValue in
            // Performance optimization: Clear cache and reload filtered data
            clearAllCaches()
            loadFilteredActivities()
        }
        .onChange(of: activities) { oldValue, newValue in
            // 🚀 FIX: Clear cache when activities change to prevent stale empty data
            clearAllCaches()
            updateChartData()
        }
        .onChange(of: monthOffset) { oldValue, newValue in
            if selectedPeriod == .oneMonth {
                selectedDataPoint = nil
            }
        }
        .onChange(of: weekOffset) { oldValue, newValue in
            if selectedPeriod == .sevenDays {
                selectedDataPoint = nil
            }
        }
        .onChange(of: yearOffset) { oldValue, newValue in
            if selectedPeriod == .oneYear {
                selectedDataPoint = nil
            }
        }
        .onAppear {
            // Performance optimization: Smart sport type initialization using cached value
            initializeSmartSportType()
            loadFilteredActivities()
        }
        .task {
            // Initial data load is now handled in onAppear
        }
        // ✅ ADD: Listen for deletion notifications and refresh chart data
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ActivityDeleted"))) { _ in
            Task {
                // ✅ Reload filtered activities and update chart
                loadFilteredActivities()
            }
        }
    }
    
    // MARK: - 🚀 NEW: Simplified Chart Data Management
    
    /// Generate chart data for specific offset - this is the ONLY chart data generation function we need
    private func generateChartDataForOffset(_ offset: Int) -> [ActivityDataPoint] {
        // Check cache first
        if let cachedData = chartDataCache[offset] {
            return cachedData
        }
        
        let calendar = Calendar.current
        let now = Date()
        var chartData: [ActivityDataPoint] = []
        
        switch selectedPeriod {
        case .sevenDays:
            let offsetNow = calendar.date(byAdding: .weekOfYear, value: offset, to: now)!
            let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: offsetNow)
            let weekStart = calendar.date(from: components)!
            let weekEnd = calendar.date(byAdding: .day, value: 7, to: weekStart)!
            
            // Pre-populate all days of the week with zero values
            var dailyData: [Date: (distance: Double, duration: TimeInterval, calories: Double)] = [:]
            for dayOffset in 0..<7 {
                let date = calendar.date(byAdding: .day, value: dayOffset, to: weekStart)!
                dailyData[date] = (0, 0, 0)
            }
            
            // Filter activities within this week
            let weekActivities = activities.filter { activity in
                let activityDate = activity.startTime
                return activityDate >= weekStart && activityDate < weekEnd
            }
            
            // Aggregate data by day
            for activity in weekActivities {
                let activityDate = calendar.startOfDay(for: activity.startTime)
                let current = dailyData[activityDate] ?? (0, 0, 0)
                dailyData[activityDate] = (
                    current.distance + activity.distance,
                    current.duration + activity.duration,
                    current.calories + activity.calories
                )
            }
            
            chartData = dailyData.map { date, values in
                ActivityDataPoint(
                    id: UUID(),
                    activityId: UUID(),
                    date: date,
                    distance: values.distance,
                    duration: values.duration,
                    calories: values.calories,
                    unitSystem: profile.first?.units ?? .metric
                )
            }.sorted { $0.date < $1.date }
            
        case .oneMonth:
            let offsetNow = calendar.date(byAdding: .month, value: offset, to: now)!
            let components = calendar.dateComponents([.year, .month], from: offsetNow)
            let monthStart = calendar.date(from: components)!
            let nextMonth = calendar.date(byAdding: .month, value: 1, to: monthStart)!
            let monthEnd = calendar.date(byAdding: .second, value: -1, to: nextMonth)!
            
            // Pre-populate days with zero values
            var dailyData: [Date: (distance: Double, duration: TimeInterval, calories: Double)] = [:]
            var currentDate = monthStart
            while currentDate <= monthEnd {
                dailyData[currentDate] = (0, 0, 0)
                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
            }
            
            // Filter activities for this month
            let monthActivities = activities.filter { activity in
                let activityDate = activity.startTime
                return activityDate >= monthStart && activityDate <= monthEnd
            }
            
            // Aggregate by day
            for activity in monthActivities {
                let activityDate = calendar.startOfDay(for: activity.startTime)
                let current = dailyData[activityDate] ?? (0, 0, 0)
                dailyData[activityDate] = (
                    current.distance + activity.distance,
                    current.duration + activity.duration,
                    current.calories + activity.calories
                )
            }
            
            chartData = dailyData.map { date, values in
                ActivityDataPoint(
                    id: UUID(),
                    activityId: UUID(),
                    date: date,
                    distance: values.distance,
                    duration: values.duration,
                    calories: values.calories,
                    unitSystem: profile.first?.units ?? .metric
                )
            }.sorted { $0.date < $1.date }
            
        case .oneYear:
            let offsetNow = calendar.date(byAdding: .year, value: offset, to: now)!
            let components = calendar.dateComponents([.year], from: offsetNow)
            let yearStart = calendar.date(from: components)!
            let nextYear = calendar.date(byAdding: .year, value: 1, to: yearStart)!
            
            // Pre-populate months with zero values
            var monthlyData: [Date: (distance: Double, duration: TimeInterval, calories: Double)] = [:]
            for month in 0..<12 {
                if let monthDate = calendar.date(byAdding: .month, value: month, to: yearStart) {
                    monthlyData[monthDate] = (0, 0, 0)
                }
            }
            
            // Filter activities for this year
            let yearActivities = activities.filter { activity in
                let activityDate = activity.startTime
                return activityDate >= yearStart && activityDate < nextYear
            }
            
            // Aggregate by month
            for activity in yearActivities {
                let monthComps = calendar.dateComponents([.year, .month], from: activity.startTime)
                if let monthDate = calendar.date(from: monthComps) {
                    var current = monthlyData[monthDate] ?? (0, 0, 0)
                    current.distance += activity.distance
                    current.duration += activity.duration
                    current.calories += activity.calories
                    monthlyData[monthDate] = current
                }
            }
            
            chartData = monthlyData.map { date, values in
                ActivityDataPoint(
                    id: UUID(),
                    activityId: UUID(),
                    date: date,
                    distance: values.distance,
                    duration: values.duration,
                    calories: values.calories,
                    unitSystem: profile.first?.units ?? .metric
                )
            }.sorted { $0.date < $1.date }
            
        case .allTime:
            chartData = []
        }
        
        // Cache the result
        chartDataCache[offset] = chartData
        return chartData
    }
    
    /// Simplified updateChartData for allTime view only
    private func updateChartData() {
        // Only used for allTime view - all other views use generateChartDataForOffset()
        guard selectedPeriod == .allTime else { return }
        
        let calendar = Calendar.current
        let now = Date()
        
        // Get earliest activity date or default to 3 years ago
        let earliestDate = activities.map { $0.startTime }.min() ??
            calendar.date(byAdding: .year, value: -3, to: now)!
        // Ensure we start from the beginning of the earliest year
        let yearComponents = calendar.dateComponents([.year], from: earliestDate)
        let periodStartDate = calendar.date(from: yearComponents)!
        
        var aggregatedData: [Date: (distance: Double, duration: TimeInterval, calories: Double)] = [:]
        
        // Pre-populate dates
        var currentDate = periodStartDate
        while currentDate <= now {
            let yearComps = calendar.dateComponents([.year], from: currentDate)
            let keyDate = calendar.date(from: yearComps) ?? currentDate
            aggregatedData[keyDate] = (0, 0, 0)
            
            // Advance to next year
            currentDate = calendar.date(byAdding: .year, value: 1, to: currentDate) ?? currentDate
        }
        
        // Aggregate activity data by year
        for activity in activities {
            let yearComps = calendar.dateComponents([.year], from: activity.startTime)
            let keyDate = calendar.date(from: yearComps) ?? activity.startTime
            
            if var existing = aggregatedData[keyDate] {
                existing.distance += activity.distance
                existing.duration += activity.duration
                existing.calories += activity.calories
                aggregatedData[keyDate] = existing
            }
        }
        
        // Convert to chart data points
        chartData = aggregatedData
            .sorted(by: { $0.key < $1.key })
            .map { date, values in
                ActivityDataPoint(
                    id: UUID(),
                    activityId: UUID(),
                    date: date,
                    distance: values.distance,
                    duration: values.duration,
                    calories: values.calories,
                    unitSystem: profile.first?.units ?? .metric
                )
            }
    }
    
    /// Expand range when user approaches boundaries
    private func expandRangeIfNeeded(_ currentOffset: Int) {
        let expandThreshold = 5 // Expand when within 5 periods of boundary
        
        // Expand backwards (more historical data)
        if currentOffset <= availableOffsetRange.lowerBound + expandThreshold {
            let newLowerBound = availableOffsetRange.lowerBound - 25
            availableOffsetRange = newLowerBound...availableOffsetRange.upperBound
            print("📊 Expanded range backwards to: \(availableOffsetRange)")
        }
        
        // Expand forwards (but not beyond current time)
        if currentOffset >= availableOffsetRange.upperBound - expandThreshold && availableOffsetRange.upperBound < 0 {
            let newUpperBound = min(availableOffsetRange.upperBound + 25, 0)
            availableOffsetRange = availableOffsetRange.lowerBound...newUpperBound
            print("📊 Expanded range forwards to: \(availableOffsetRange)")
        }
    }
    
    // MARK: - Performance Optimization Helper Methods
    
    /// Smart sport type initialization using cached value from UserProfile
    private func initializeSmartSportType() {
        if let userProfile = profile.first,
           let lastSportType = userProfile.lastActivitySportType {
            // Convert SportType to ActivityType
            switch lastSportType {
            case .run:
                selectedActivityType = .run
            case .walk:
                selectedActivityType = .walk
            case .hike:
                selectedActivityType = .hike
            case .bike:
                selectedActivityType = .bike
            }
        } else {
            selectedActivityType = .run
        }
    }
    
    /// Load only filtered activities based on sport type
    private func loadFilteredActivities() {
        isLoadingData = true
        
        Task {
            let filteredActivities = await fetchFilteredActivities()
            
            await MainActor.run {
                self.activities = filteredActivities
                self.isLoadingData = false
                updateChartData()
            }
        }
    }
    
    /// Fetch activities with targeted query (background thread)
    private func fetchFilteredActivities() async -> [RunActivity] {
        let currentSportType = selectedActivityType.sportType
        let context = modelContext
        
        return await withCheckedContinuation { continuation in
            Task.detached {
                do {
                    let backgroundContext = ModelContext(context.container)
                    
                    let descriptor = FetchDescriptor<RunActivity>(
                        sortBy: [SortDescriptor(\.startTime, order: .reverse)]
                    )
                    
                    let allActivities = try backgroundContext.fetch(descriptor)
                    
                    // Only filter by sport type
                    let sportFiltered = allActivities.filter { activity in
                        activity.sportType == currentSportType
                    }
                    
                    print("📊 AnalysisView: Loaded \(sportFiltered.count) activities for \(currentSportType)")
                    continuation.resume(returning: sportFiltered)
                } catch {
                    print("🔴 AnalysisView fetch error: \(error)")
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    /// Clear all cached data
    private func clearAllCaches() {
        chartData.removeAll()
        chartDataCache.removeAll()
    }
    
    /// Reset offsets based on selected period
    private func resetOffsetsForPeriod(_ period: TimePeriod) {
        switch period {
        case .sevenDays:
            weekOffset = 0
        case .oneMonth:
            monthOffset = 0
        case .oneYear:
            yearOffset = 0
        case .allTime:
            // No offset needed for all time
            break
        }
        
        // Reset dynamic range and clear cache
        availableOffsetRange = -50...0
        chartDataCache.removeAll()
    }
}

extension View {
    @ViewBuilder func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

#Preview {
    AnalysisView()
} 