import Combine
import CoreLocation
import MapKit
import SwiftData
import SwiftUI

enum ActivityDetailTab: String, CaseIterable {
  case map = "Map"
  case stats = "Stats"

  var systemImage: String {
    switch self {
    case .map: return "map"
    case .stats: return "chart.bar.xaxis"
    }
  }
}

@MainActor
final class ActivityDetailViewModel: ObservableObject {
  // Input
  private let activity: RunActivity

  // Output: Published properties to drive the UI
  @Published var isLoading = true
  @Published var selectedTab: ActivityDetailTab = .map
  @Published var mapRegion: MKCoordinateRegion?
  @Published var speedSegments: [SpeedSegment] = []
  @Published var locationName = ""
  @Published var deletionErrorMessage = ""
  @Published var showDeletionError = false
  @Published var isDeleting = false
  @Published var isInBackground = false  // Track background state to prevent watchdog timeout

  // MARK: - State Preservation for Screen Lock
  private var preservedMapRegion: MKCoordinateRegion?
  private var preservedSpeedSegments: [SpeedSegment] = []
  private var backgroundTimer: Timer?
  private let TRUE_BACKGROUND_DELAY: TimeInterval = 2.0

  init(activity: RunActivity) {
    self.activity = activity
  }

  func loadActivityData() async {
    // Don't load heavy data if we're in background
    guard !isInBackground else {
      isLoading = false
      return
    }

    // Set loading state when actually loading
    isLoading = true

    // Capture activity data for background processing
    let activityRoutes = activity.route
    let activityCopy = activity

    // --- ENHANCED OPTIMIZATION: Use pre-calculated data ---
    // Perform lightweight work in a background task
    let processedData = await Task.detached(priority: .userInitiated) {
      () -> (MKCoordinateRegion?, [SpeedSegment], String) in
      // 1. Calculate Region (from stored simplified coordinates)
      let region = await self.calculateRouteRegion(routes: activityRoutes)

      // 2. Get Speed Segments (already optimized - uses pre-simplified coordinates)
      let segments = activityCopy.speedSegments  // ✅ USES PRE-CALCULATED DATA

      // 3. Reverse Geocode Location (always fetch fresh like ActivityRowView)
      let name = await self.fetchLocationName(routes: activityRoutes)

      return (region, segments, name)
    }.value

    // Update published properties on the main thread
    self.mapRegion = processedData.0
    self.speedSegments = processedData.1
    self.locationName = processedData.2
    self.isLoading = false
  }

  /// Handle background state changes to prevent watchdog timeout
  func setBackgroundState(_ isBackground: Bool) {
    guard isInBackground != isBackground else { return }
    isInBackground = isBackground

    if isBackground {
      // Immediately preserve state
      suspendHeavyOperations()
      // Start timer - if still background after delay, truly backgrounded
      backgroundTimer = Timer.scheduledTimer(
        withTimeInterval: TRUE_BACKGROUND_DELAY, repeats: false
      ) { _ in
        Task { @MainActor in
          self.trulySuspendOperations()
        }
      }
    } else {
      // Cancel timer and restore immediately on foreground
      backgroundTimer?.invalidate()
      backgroundTimer = nil
      resumeOperations()
    }
  }

  /// Preserve state during screen lock (does not clear data immediately)
  private func suspendHeavyOperations() {
    // Preserve current state before potential clearing
    preservedMapRegion = mapRegion
    preservedSpeedSegments = speedSegments
    print("ActivityDetailViewModel: Preserved state for potential background")
  }

  /// Actually clear state for true backgrounding (called by timer)
  private func trulySuspendOperations() {
    // Clear speed segments to reduce memory pressure
    speedSegments.removeAll()
    // Clear map region to prevent map rendering in background
    mapRegion = nil
    // Clear preserved data as well since we're truly backgrounded
    preservedMapRegion = nil
    preservedSpeedSegments.removeAll()
    print("ActivityDetailViewModel: Truly suspended heavy operations for background")
  }

  /// Resume operations when returning to foreground
  private func resumeOperations() {
    // Restore preserved state if available
    if let preserved = preservedMapRegion {
      mapRegion = preserved
    }
    if !preservedSpeedSegments.isEmpty {
      speedSegments = preservedSpeedSegments
    }

    // Only reload if we don't have preserved data
    if mapRegion == nil || speedSegments.isEmpty {
      Task {
        await loadActivityData()
      }
    }
    print("ActivityDetailViewModel: Resumed operations for foreground")
  }

  // Move deletion logic here with background optimization
  func deleteActivity(modelContext: ModelContext, dismiss: @escaping () -> Void) async {
    isDeleting = true

    // --- CRITICAL: Proactively clear the large coordinates array from memory ---
    // This breaks the strong reference to the memory block before dismissal,
    // preventing the main thread from blocking on deallocation.
    activity.prepareForDeletion()

    // Immediately suspend heavy operations to prevent watchdog timeout
    suspendHeavyOperations()

    do {
      let container = modelContext.container
      let deletionActor = ActivityDeletionActor(modelContainer: container)

      // Use higher priority for deletion to complete before background timeout
      try await withThrowingTaskGroup(of: Void.self) { group in
        group.addTask(priority: .high) {
          // The actor now operates on an object that has already shed its memory weight.
          try await deletionActor.deleteActivity(activityId: self.activity.id)
        }
      }

      await MainActor.run {
        // ✅ Send notification to trigger efficient refresh in ActivityView
        NotificationCenter.default.post(name: Notification.Name("ActivityDeleted"), object: nil)
        dismiss()
      }
    } catch {
      await MainActor.run {
        isDeleting = false
        deletionErrorMessage = "Failed to delete activity: \(error.localizedDescription)"
        showDeletionError = true
      }
    }
  }

  /// Manually release the largest data structures to speed up deallocation.
  /// This must run on the MainActor because it modifies @Published properties.
  func onDisappear() {
    // Cancel any pending background timer
    backgroundTimer?.invalidate()
    backgroundTimer = nil

    // Clear all state
    speedSegments.removeAll()
    mapRegion = nil
    preservedMapRegion = nil
    preservedSpeedSegments.removeAll()
  }

  // MARK: - Public Properties for UI

  var activityReference: RunActivity {
    activity
  }

  // MARK: - Optimized Stats Access (No Computation)

  var distance: Double { activity.distance }
  var calories: Double { activity.calories }
  var duration: TimeInterval { activity.activeRunningTime }  // This is active time
  var activeTime: TimeInterval { activity.activeRunningTime }  // Explicit active time property
  var totalTime: TimeInterval { activity.workoutStats.totalTime }
  var route: [Coordinate] { activity.coordinates }

  // Enhanced stats using WorkoutStats from RunActivity
  var averagePace: Double { activity.workoutStats.averagePace }
  var averageSpeed: Double { activity.workoutStats.averageSpeed }
  var maxSpeed: Double { activity.workoutStats.maxSpeed }
  var bestPace: Double { activity.workoutStats.bestPace }
  var pauseTime: TimeInterval { activity.workoutStats.pauseTime }
  var activeCalories: Double {
    let value = activity.workoutStats.activeCalories
    print("🔥 CALORIE DEBUG: ActivityDetailViewModel.activeCalories called, returning: \(value)")
    return value
  }
  var pauseCalories: Double { activity.workoutStats.pauseCalories }

  // MARK: - Private Helper Functions (Moved from View)

  private func calculateRouteRegion(routes: [Coordinate]) -> MKCoordinateRegion? {
    guard !routes.isEmpty else { return nil }

    let coordinates = routes.map { $0.clCoordinate }
    var minLat = coordinates[0].latitude
    var maxLat = minLat
    var minLon = coordinates[0].longitude
    var maxLon = minLon

    for coord in coordinates {
      minLat = min(minLat, coord.latitude)
      maxLat = max(maxLat, coord.latitude)
      minLon = min(minLon, coord.longitude)
      maxLon = max(maxLon, coord.longitude)
    }

    let routeLatSpan = maxLat - minLat
    let routeLonSpan = maxLon - minLon

    // Define padding ratios relative to the route's span.
    // These ratios determine how much extra space is added around the route.
    // A ratio of 0.3 means 30% of the route's height/width is added as padding.
    let topPaddingRatio: Double = 0.3
    let bottomPaddingRatio: Double = 0.6  // Increased for more bottom padding
    let horizontalPaddingRatio: Double = 0.3  // Applied equally to left and right

    // Calculate the effective min/max latitude and longitude for the visible map region
    let effectiveMinLat = minLat - (routeLatSpan * bottomPaddingRatio)
    let effectiveMaxLat = maxLat + (routeLatSpan * topPaddingRatio)
    let effectiveMinLon = minLon - (routeLonSpan * horizontalPaddingRatio)
    let effectiveMaxLon = maxLon + (routeLonSpan * horizontalPaddingRatio)

    // Calculate the center coordinate for the new region
    let center = CLLocationCoordinate2D(
      latitude: (effectiveMinLat + effectiveMaxLat) / 2,
      longitude: (effectiveMinLon + effectiveMaxLon) / 2
    )

    // Calculate the span (width and height) for the new region
    let span = MKCoordinateSpan(
      latitudeDelta: max(effectiveMaxLat - effectiveMinLat, 0.01),  // Ensure minimum span for very small routes
      longitudeDelta: max(effectiveMaxLon - effectiveMinLon, 0.01)  // Ensure minimum span
    )

    return MKCoordinateRegion(center: center, span: span)
  }

  private func fetchLocationName(routes: [Coordinate]) async -> String {
    guard let firstCoordinate = routes.first else { return "Unknown Location" }

    let location = CLLocation(
      latitude: firstCoordinate.latitude, longitude: firstCoordinate.longitude)
    let geocoder = CLGeocoder()

    do {
      let placemarks = try await geocoder.reverseGeocodeLocation(location)
      if let placemark = placemarks.first {
        var components: [String] = []

        if let thoroughfare = placemark.thoroughfare {
          components.append(thoroughfare)
        }
        if let subLocality = placemark.subLocality {
          components.append(subLocality)
        }
        if let locality = placemark.locality {
          components.append(locality)
        }
        if let administrativeArea = placemark.administrativeArea {
          components.append(administrativeArea)
        }

        return components.joined(separator: ", ")
      }
      return "Location Unavailable"
    } catch {
      return "Location Unavailable"
    }
  }
}
