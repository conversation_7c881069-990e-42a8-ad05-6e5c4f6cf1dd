import SwiftUI
import SwiftData

struct LanguageSettingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @State private var selectedLanguage: String
    
    private let languageManager = LanguageManager.shared
    private let voiceManager = VoiceManager.shared
    
    init() {
        _selectedLanguage = State(initialValue: LanguageManager.shared.currentLanguage)
    }
    
    var body: some View {
        NavigationView {
            List {
                ForEach(languageManager.availableLanguages, id: \.self) { languageCode in
                    LanguageRow(
                        languageName: languageManager.getLanguageName(for: languageCode),
                        isSelected: selectedLanguage == languageCode
                    )
                    .contentShape(Rectangle())
                    .onTapGesture {
                        changeLanguage(to: languageCode)
                    }
                }
            }
            .navigationTitle(Text(verbatim: "language".localized))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized) {
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, languageManager.isRTL ? .rightToLeft : .leftToRight)
    }
    
    private func changeLanguage(to languageCode: String) {
        let previousLanguage = selectedLanguage
        selectedLanguage = languageCode
        languageManager.setLanguage(languageCode)
        
        // Handle voice selection for new language
        handleVoiceSelectionForLanguageChange(from: previousLanguage, to: languageCode)
        
        // Trigger voice list refresh for the new language
        voiceManager.refreshVoices()
    }
    
    private func handleVoiceSelectionForLanguageChange(from previousLanguage: String, to newLanguage: String) {
        guard let userProfile = profile.first else { return }
        
        // Check if user has a voice preference for the new language
        let hasVoiceForNewLanguage = userProfile.getVoiceForLanguage(newLanguage) != nil
        
        if !hasVoiceForNewLanguage {
            // No voice preference for new language, set to best available voice
            if let bestVoice = voiceManager.getBestVoiceForLanguage(newLanguage) {
                userProfile.setVoiceForLanguage(newLanguage, voiceIdentifier: bestVoice.id)
                
                // Save the profile
                do {
                    try modelContext.save()
                    
                    // Update the audio alert manager with the new profile
                    AudioAlertManager.shared.updateUserProfile(userProfile)
                } catch {
                    print("Failed to save voice preference for new language: \(error)")
                }
            }
        }
    }
}

struct LanguageRow: View {
    let languageName: String
    let isSelected: Bool
    
    var body: some View {
        HStack {
            Text(languageName)
            Spacer()
            if isSelected {
                Image(systemName: "checkmark")
                    .foregroundColor(.accentColor)
            }
        }
        .contentShape(Rectangle())
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    return LanguageSettingView()
        .modelContainer(container)
}
