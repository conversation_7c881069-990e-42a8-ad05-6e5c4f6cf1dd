//
//  TrialWrapperView.swift
//  RunApp
//
//  Created by <PERSON><PERSON><PERSON> on 4/24/25.
//

import SwiftUI
import SwiftData
import UserNotifications

struct TrialWrapperView: View {
    let modelContainer: ModelContainer
    // Removed trialStatus state and scenePhase environment variable
    // Trial limit check logic moved to ContentView.saveActivity()

    var body: some View {
        // Directly present ContentView
        ContentView()
            .modelContainer(modelContainer)
            .onAppear {
                // Request notifications permission on appear
                UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
                    if granted {
                        print("Notification permission granted")
                    } else if let error = error {
                        print("Notification permission error: \(error.localizedDescription)")
                    }
                }
            }
        // Removed onChange(of: scenePhase) modifier
        // Removed updateTrialStatus() function
    }
}
