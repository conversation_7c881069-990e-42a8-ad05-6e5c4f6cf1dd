//
//  ConditionalPaywallModifier.swift
//  RunApp
//
//  Created by <PERSON><PERSON><PERSON> on 4/24/25.
//

import SwiftUI
import RevenueCatUI

struct ConditionalPaywallModifier: ViewModifier {
    let shouldApplyPaywall: Bool
    
    func body(content: Content) -> some View {
        if shouldApplyPaywall {
            content
                .presentPaywallIfNeeded(
                    requiredEntitlementIdentifier: "Pro",
                    presentationMode: .fullScreen,
                    purchaseCompleted: { customerInfo in
                        print("Purchase completed: \(customerInfo.entitlements)")
                    },
                    restoreCompleted: { customerInfo in
                        print("Purchases restored: \(customerInfo.entitlements)")
                    }
                )
        } else {
            content
        }
    }
}
