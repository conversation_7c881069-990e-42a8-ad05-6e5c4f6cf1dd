import SwiftUI

struct VoiceSelectionView: View {
    @ObservedObject private var voiceManager = VoiceManager.shared
    private var languageManager = LanguageManager.shared
    @Environment(\.dismiss) private var dismiss
    
    @Binding var userProfile: UserProfile
    
    @State private var selectedVoiceId: String?
    @State private var isLoading = false
    @State private var showingPreviewError = false
    @State private var previewErrorMessage = ""
    @State private var showingVoiceInstructions = false
    
    // MARK: - Design Tokens
    private struct DesignTokens {
        static let iconSize: Font = .system(size: 18, weight: .medium)
        static let primaryIconSize: Font = .system(size: 20, weight: .medium)
        static let smallIconSize: Font = .system(size: 14, weight: .medium)
        static let primaryColor: Color = .blue
        static let secondaryColor: Color = .secondary
        static let tertiaryColor: Color = Color(.tertiaryLabel)
        static let premiumColor: Color = .purple
        static let enhancedColor: Color = .blue
        static let standardColor: Color = .gray
        static let standardSpacing: CGFloat = 12
        static let compactSpacing: CGFloat = 8
        static let sectionSpacing: CGFloat = 16
    }
    
    init(userProfile: Binding<UserProfile>) {
        self._userProfile = userProfile
    }
    
    private var currentLanguage: String {
        languageManager.currentLanguage
    }
    
    private var availableVoices: [VoiceInfo] {
        voiceManager.getVoicesForLanguage(currentLanguage)
    }
    
    private var hasPremiumVoices: Bool {
        availableVoices.contains { $0.quality == .premium }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if voiceManager.isLoading {
                    loadingView
                } else if availableVoices.isEmpty {
                    emptyStateView
                } else {
                    voiceListView
                }
            }
            .navigationTitle("voice.selection.title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("common.close".localized) {
                        dismiss()
                    }
                    .font(.body)
                    .fontWeight(.medium)
                }
            }
            .onAppear {
                loadCurrentSelection()
            }
            .alert("voice.preview.error.title".localized, isPresented: $showingPreviewError) {
                Button("common.ok".localized, role: .cancel) { }
            } message: {
                Text(previewErrorMessage)
            }
            .alert("voice.instructions.title".localized, isPresented: $showingVoiceInstructions) {
                Button("voice.instructions.open.settings".localized) {
                    openSettingsApp()
                }
                Button("common.cancel".localized, role: .cancel) { }
            } message: {
                Text("voice.instructions.message".localized)
            }
        }
    }
    
    @ViewBuilder
    private var loadingView: some View {
        VStack(spacing: DesignTokens.sectionSpacing) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(DesignTokens.primaryColor)
            
            Text("voice.loading.voices".localized)
                .font(.headline)
                .fontWeight(.medium)
                .foregroundColor(DesignTokens.secondaryColor)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    @ViewBuilder
    private var emptyStateView: some View {
        VStack(spacing: DesignTokens.sectionSpacing) {
            Image(systemName: "speaker.slash")
                .font(.system(size: 50, weight: .medium))
                .foregroundColor(DesignTokens.secondaryColor)
            
            Text("voice.empty.title".localized)
                .font(.title2)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
            
            Text("voice.empty.message".localized)
                .font(.body)
                .foregroundColor(DesignTokens.secondaryColor)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 24)
            
            VStack(spacing: DesignTokens.standardSpacing) {
                Button("voice.add.new".localized) {
                    openVoiceSettings()
                }
                .buttonStyle(.borderedProminent)
                .font(.body)
                .fontWeight(.medium)
                
                Button("voice.refresh.voices".localized) {
                    refreshVoices()
                }
                .buttonStyle(.bordered)
                .font(.body)
                .fontWeight(.medium)
            }
            
            VStack(spacing: DesignTokens.compactSpacing) {
                Text("voice.instructions.steps.title".localized)
                    .font(.footnote)
                    .fontWeight(.semibold)
                    .foregroundColor(DesignTokens.secondaryColor)
                
                Text("voice.instructions.steps.detail".localized)
                    .font(.footnote)
                    .foregroundColor(DesignTokens.tertiaryColor)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 24)
            .padding(.top, DesignTokens.compactSpacing)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(24)
    }
    
    @ViewBuilder
    private var voiceListView: some View {
        VStack(spacing: 0) {
            // Language info header
            languageInfoHeader
            
            // Voice list
            List {
                // Voice Management section - at top
                Section {
                    // Add New Voice button
                    Button(action: openVoiceSettings) {
                        HStack(spacing: DesignTokens.standardSpacing) {
                            Image(systemName: "plus.circle.fill")
                                .font(DesignTokens.primaryIconSize)
                                .foregroundColor(DesignTokens.primaryColor)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("voice.add.new".localized)
                                    .font(.body)
                                    .fontWeight(.medium)
                                    .foregroundColor(DesignTokens.primaryColor)
                                
                                Text("voice.add.new.subtitle".localized)
                                    .font(.footnote)
                                    .foregroundColor(DesignTokens.secondaryColor)
                            }
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .font(DesignTokens.smallIconSize)
                                .foregroundColor(DesignTokens.tertiaryColor)
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // Refresh Voices button
                    Button(action: refreshVoices) {
                        HStack(spacing: DesignTokens.standardSpacing) {
                            Image(systemName: "arrow.clockwise")
                                .font(DesignTokens.primaryIconSize)
                                .foregroundColor(DesignTokens.primaryColor)
                            
                            Text("voice.refresh.voices".localized)
                                .font(.body)
                                .fontWeight(.medium)
                                .foregroundColor(DesignTokens.primaryColor)
                            
                            Spacer()
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(PlainButtonStyle())
                } footer: {
                    Text("voice.management.footer".localized)
                        .font(.footnote)
                        .foregroundColor(DesignTokens.secondaryColor)
                }
                
                // Premium voice benefits banner (always show to encourage premium voice adoption)
                Section {
                    premiumVoiceBenefitsBanner
                        .listRowInsets(EdgeInsets())
                        .listRowBackground(Color.clear)
                }
                
                // Voice Quality sections
                ForEach(groupedVoices.keys.sorted(by: qualitySortOrder), id: \.self) { quality in
                    Section {
                        ForEach(groupedVoices[quality] ?? [], id: \.id) { voice in
                            VoiceRowView(
                                voice: voice,
                                isSelected: voice.id == selectedVoiceId,
                                onSelect: {
                                    selectedVoiceId = voice.id
                                    saveSelection()
                                },
                                onPreview: {
                                    previewVoice(voice)
                                }
                            )
                            .deleteDisabled(!voice.isDeletable)
                        }
                        .onDelete { indexSet in
                            deleteVoices(at: indexSet, in: quality)
                        }
                    } header: {
                        qualitySectionHeader(for: quality)
                    } 
                }
            }
            .listStyle(InsetGroupedListStyle())
        }
    }
    
    @ViewBuilder
    private var languageInfoHeader: some View {
        VStack(spacing: DesignTokens.compactSpacing) {
            HStack(spacing: DesignTokens.standardSpacing) {
                Image(systemName: "globe")
                    .font(DesignTokens.primaryIconSize)
                    .foregroundColor(DesignTokens.primaryColor)
                
                Text("voice.current.language".localized)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Text(getLanguageDisplayName(currentLanguage))
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(DesignTokens.primaryColor)
            }
            .padding(.horizontal, 20)
            .padding(.top, DesignTokens.sectionSpacing)
            
            Text("voice.selection.simple.description".localized)
                .font(.footnote)
                .foregroundColor(DesignTokens.secondaryColor)
                .multilineTextAlignment(.leading)
                .padding(.horizontal, 20)
                .padding(.bottom, DesignTokens.standardSpacing)
        }
        .background(Color(.systemGroupedBackground))
    }
    
    @ViewBuilder
    private var premiumVoiceBenefitsBanner: some View {
        VStack(spacing: DesignTokens.compactSpacing) {
            HStack(spacing: DesignTokens.standardSpacing) {
                // Premium icon with gradient background
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [DesignTokens.premiumColor.opacity(0.2), DesignTokens.premiumColor.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 32, height: 32)
                    
                    Image(systemName: "crown.fill")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(DesignTokens.premiumColor)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("voice.premium.benefits.recommendation".localized)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, DesignTokens.standardSpacing)
            
            // Benefits list
            VStack(alignment: .leading, spacing: DesignTokens.compactSpacing) {
                benefitRow(icon: "waveform", text: "voice.premium.benefit.clarity".localized)
                benefitRow(icon: "ear", text: "voice.premium.benefit.noise".localized)
                benefitRow(icon: "figure.run", text: "voice.premium.benefit.motivation".localized)
            }
            .padding(.horizontal, 20)
            .padding(.bottom, DesignTokens.standardSpacing)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(DesignTokens.premiumColor.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(DesignTokens.premiumColor.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, 16)
        .padding(.bottom, DesignTokens.standardSpacing)
    }
    
    @ViewBuilder
    private func benefitRow(icon: String, text: String) -> some View {
        HStack(spacing: DesignTokens.compactSpacing) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(DesignTokens.premiumColor)
                .frame(width: 16)
            
            Text(text)
                .font(.footnote)
                .foregroundColor(DesignTokens.secondaryColor)
        }
    }
    
    private var groupedVoices: [VoiceQuality: [VoiceInfo]] {
        Dictionary(grouping: availableVoices, by: \.quality)
    }
    
    private func qualitySortOrder(_ lhs: VoiceQuality, _ rhs: VoiceQuality) -> Bool {
        lhs.priority > rhs.priority
    }
    
    @ViewBuilder
    private func qualitySectionHeader(for quality: VoiceQuality) -> some View {
        HStack(spacing: DesignTokens.standardSpacing) {
            // Enhanced visual treatment for quality icons
            ZStack {
                if quality == .premium {
                    Circle()
                        .fill(DesignTokens.premiumColor.opacity(0.15))
                        .frame(width: 28, height: 28)
                }
                
                Image(systemName: qualityIcon(for: quality))
                    .font(DesignTokens.iconSize)
                    .foregroundColor(qualityColor(for: quality))
            }
            
            VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 6) {
                    Text(quality.displayName)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    if quality == .premium {
                        Text("voice.premium.recommended".localized)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(DesignTokens.premiumColor)
                            .cornerRadius(8)
                    }
                }
                
            }
            
            Spacer()
            
            if let count = groupedVoices[quality]?.count {
                Text("\(count)")
                    .font(.footnote)
                    .fontWeight(.medium)
                    .foregroundColor(DesignTokens.secondaryColor)
            }
        }
        .padding(.vertical, 4)
    }
    
    private func qualityIcon(for quality: VoiceQuality) -> String {
        switch quality {
        case .standard: return "star"
        case .enhanced: return "star.fill"
        case .premium: return "crown.fill"
        }
    }
    
    private func qualityColor(for quality: VoiceQuality) -> Color {
        switch quality {
        case .standard: return DesignTokens.standardColor
        case .enhanced: return DesignTokens.enhancedColor
        case .premium: return DesignTokens.premiumColor
        }
    }
    
    private func getLanguageDisplayName(_ languageCode: String) -> String {
        let locale = Locale.current
        return locale.localizedString(forLanguageCode: languageCode) ?? languageCode
    }
    
    private func loadCurrentSelection() {
        selectedVoiceId = userProfile.getVoiceForLanguage(currentLanguage)
        
        // If no selection, try to select the best available voice
        if selectedVoiceId == nil {
            if let bestVoice = voiceManager.getBestVoiceForLanguage(currentLanguage) {
                selectedVoiceId = bestVoice.id
            }
        }
    }
    
    private func saveSelection() {
        if let voiceId = selectedVoiceId {
            userProfile.setVoiceForLanguage(currentLanguage, voiceIdentifier: voiceId)
            
            // Update the audio alert manager with the new profile
            AudioAlertManager.shared.updateUserProfile(userProfile)
        }
    }
    
    private func previewVoice(_ voice: VoiceInfo) {
        print("VoiceSelectionView: Starting voice preview for \(voice.name) (ID: \(voice.id))")
        
        VoiceManager.shared.testVoice(voice) { success in
            DispatchQueue.main.async {
                print("VoiceSelectionView: Voice preview completed with success: \(success)")
                if !success {
                    previewErrorMessage = String(format: "voice.preview.error.message".localized, voice.name)
                    showingPreviewError = true
                }
            }
        }
    }
    
    private func refreshVoices() {
        isLoading = true
        voiceManager.refreshVoices()
        
        // Refresh will happen automatically via @ObservedObject
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false
        }
    }
    
    private func openVoiceSettings() {
        // Show instructions alert first
        showingVoiceInstructions = true
    }
    
    private func openSettingsApp() {
        // Open iOS Settings app
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            if UIApplication.shared.canOpenURL(settingsUrl) {
                UIApplication.shared.open(settingsUrl, completionHandler: { success in
                    print("VoiceSelectionView: Opened Settings app with success: \(success)")
                })
            }
        }
    }
    
    /// Handle deletion of voices from swipe-to-delete gesture
    private func deleteVoices(at offsets: IndexSet, in quality: VoiceQuality) {
        guard let voicesInQuality = groupedVoices[quality] else { return }
        
        for index in offsets {
            let voice = voicesInQuality[index]
            
            // Only allow deletion if voice is deletable
            guard voice.isDeletable else {
                print("VoiceSelectionView: Cannot delete voice \(voice.name) - it is not deletable")
                continue
            }
            
            print("VoiceSelectionView: Deleting voice: \(voice.name)")
            
            // If this is the currently selected voice, clear the selection
            if voice.id == selectedVoiceId {
                selectedVoiceId = nil
                // Try to select another available voice
                let remainingVoices = availableVoices.filter { $0.id != voice.id }
                if let newVoice = remainingVoices.first {
                    selectedVoiceId = newVoice.id
                    saveSelection()
                }
            }
            
            // Delete the voice asynchronously
            Task {
                let success = await voiceManager.deleteVoice(voice)
                await MainActor.run {
                    if success {
                        print("VoiceSelectionView: Successfully deleted voice \(voice.name)")
                    } else {
                        print("VoiceSelectionView: Failed to delete voice \(voice.name)")
                    }
                }
            }
        }
    }
}

#Preview {
    struct PreviewWrapper: View {
        @State private var profile = UserProfile()
        var body: some View {
            VoiceSelectionView(userProfile: $profile)
        }
    }
    return PreviewWrapper()
}
