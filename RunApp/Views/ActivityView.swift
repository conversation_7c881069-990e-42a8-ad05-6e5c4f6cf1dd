import SwiftUI
import MapKit
import SwiftData

struct ActivityView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Query private var profile: [UserProfile]
    
    // 🚀 PERFORMANCE FIX: Replace expensive @Query with state-based pagination + filtering
    @State private var activities: [RunActivity] = []
    @State private var isLoading = false
    @State private var hasMoreActivities = true
    @State private var currentOffset = 0  // Tracks filtered activities shown
    @State private var rawOffset = 0      // Tracks raw database offset
    @State private var errorMessage: String?
    
    // ✅ ADD: Smart filtering like AnalysisView
    @State private var selectedActivityType: ActivityType = .run
    
    private let pageSize = 20 // Load 20 activities at a time
    
    // ✅ ADD: Filter enums (same as AnalysisView)
    enum ActivityType: String, CaseIterable {
        case run = "Run"
        case walk = "Walk"
        case hike = "Hike"
        case bike = "Bike"
        
        var sportType: SportType {
            switch self {
            case .run: return .run
            case .walk: return .walk
            case .hike: return .hike
            case .bike: return .bike
            }
        }
        
        var localized: String {
            switch self {
            case .run: return "run".localized
            case .walk: return "walk".localized
            case .hike: return "hike".localized
            case .bike: return "bike".localized
            }
        }
    }
    

    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // ✅ Sport Type Filter Only (Simplified)
                VStack(spacing: 12) {
                    HStack {
                        Text("Activity Type")
                            .font(.headline)
                            .foregroundStyle(.primary)
                        Spacer()
                    }
                    
                    Picker("Activity Type", selection: $selectedActivityType) {
                        ForEach(ActivityType.allCases, id: \.self) { type in
                            Text(type.localized).tag(type)
                        }
                    }
                    .pickerStyle(.segmented)
                }
                .padding()
                .background(Color(.systemGroupedBackground))
                
                // Activities List
                ScrollView {
                    LazyVStack(spacing: 16) {
                        if activities.isEmpty && isLoading {
                            // 🚀 Loading skeleton for first load
                            ForEach(0..<5, id: \.self) { _ in
                                ActivityRowSkeleton()
                            }
                            .padding(.horizontal)
                        } else if activities.isEmpty && !isLoading {
                            ContentUnavailableView(
                                "no_activities".localized,
                                systemImage: "figure.run",
                                description: Text("complete_first_run".localized)
                            )
                            .padding(.top, 40)
                        } else {
                            ForEach(activities) { activity in
                                NavigationLink(destination: ActivityDetailView(activity: activity)) {
                                    ActivityRowView(activity: activity)
                                }
                                .buttonStyle(.plain)
                                .onAppear {
                                    // 🚀 Load more when reaching near end
                                    if activity == activities.last && hasMoreActivities && !isLoading {
                                        Task { await loadMoreActivities() }
                                    }
                                }
                            }
                            .padding(.horizontal)
                            
                            // 🚀 Loading indicator at bottom for pagination
                            if isLoading && !activities.isEmpty {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Loading more activities...")
                                        .font(.caption)
                                        .foregroundStyle(.secondary)
                                }
                                .padding()
                            }
                            
                            // 🚀 End of list indicator
                            if !hasMoreActivities && !activities.isEmpty {
                                Text("You've reached the end!")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                                    .padding()
                            }
                        }
                    }
                    .padding(.vertical)
                }
            }
            .navigationTitle("activities".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundStyle(.secondary)
                            .font(.title2)
                    }
                }
            }
            .background(Color(.systemGroupedBackground))
            .task {
                // 🚀 Initialize smart defaults and load initial activities
                await initializeSmartDefaults()
                await loadInitialActivities()
            }
            .refreshable {
                // 🚀 Pull to refresh with current filters
                await refreshActivities()
            }
            // ✅ ADD: Listen for deletion notifications and refresh with current filters
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ActivityDeleted"))) { _ in
                Task {
                    // ✅ Use existing efficient refresh with current filters
                    await refreshActivities()
                }
            }
            // ✅ Refresh when sport type filter changes
            .onChange(of: selectedActivityType) { _, _ in
                Task { await refreshActivities() }
            }
        }
    }
    
    // MARK: - 🚀 SMART FILTERING METHODS (Based on AnalysisView pattern)
    
    /// Initialize smart defaults using user's last activity type
    private func initializeSmartDefaults() async {
        // Use last activity sport type from profile (same as AnalysisView)
        if let userProfile = profile.first,
           let lastSportType = userProfile.lastActivitySportType {
            switch lastSportType {
            case .run: selectedActivityType = .run
            case .walk: selectedActivityType = .walk
            case .hike: selectedActivityType = .hike
            case .bike: selectedActivityType = .bike
            }
            print("📱 Using last activity sport type: \(lastSportType)")
        } else {
            print("📱 No user profile found, defaulting to .run filter")
        }
    }
    
    /// Load initial page of activities with current filters
    @MainActor
    private func loadInitialActivities() async {
        guard activities.isEmpty else { return }
        isLoading = true
        errorMessage = nil
        await loadActivitiesPage(offset: 0, isInitial: true)
        isLoading = false
    }
    
    /// Load more activities for pagination with current filters
    @MainActor
    private func loadMoreActivities() async {
        guard !isLoading && hasMoreActivities else { return }
        isLoading = true
        await loadActivitiesPage(offset: rawOffset, isInitial: false)
        isLoading = false
    }
    
    /// Refresh activities with current filters (pull to refresh + deletion refresh)
    @MainActor
    private func refreshActivities() async {
        activities.removeAll()
        currentOffset = 0
        rawOffset = 0
        hasMoreActivities = true
        errorMessage = nil
        await loadActivitiesPage(offset: 0, isInitial: true)
    }
    
    /// Load a page of activities with sport type filtering only (Simplified & Robust)
    private func loadActivitiesPage(offset: Int, isInitial: Bool) async {
        // ✅ SWIFT 6 FIX: Capture values before crossing actor boundary
        let currentSportType = selectedActivityType.sportType
        
        do {
            let newActivities = try await Task.detached { [modelContext] in
                let backgroundContext = ModelContext(modelContext.container)
                
                // ✅ Quick diagnostic for initial load
                if offset == 0 {
                    let totalCount = try backgroundContext.fetchCount(FetchDescriptor<RunActivity>())
                    print("📊 ActivityView: Total activities: \(totalCount), Filter: \(currentSportType)")
                }
                
                // ✅ SIMPLIFIED: Only sport type filtering, no date complexity
                var descriptor = FetchDescriptor<RunActivity>(
                    sortBy: [SortDescriptor(\.endTime, order: .reverse)]
                )
                // No date filtering needed - simplified approach
                
                // ✅ STEP 2: Implement proper pagination with manual filtering
                // Keep fetching until we get enough filtered results or run out of data
                var allFilteredActivities: [RunActivity] = []
                var currentRawOffset = offset
                let batchSize = pageSize * 2 // Fetch in batches
                var hasMoreData = true
                
                while allFilteredActivities.count < pageSize && hasMoreData {
                    descriptor.fetchLimit = batchSize
                    descriptor.fetchOffset = currentRawOffset
                    
                    let batch = try backgroundContext.fetch(descriptor)
                    print("📊 Batch fetch: offset=\(currentRawOffset), fetched=\(batch.count)")
                    
                    if batch.isEmpty {
                        hasMoreData = false
                        break
                    }
                    
                    // Filter this batch by sport type
                    let filteredBatch = batch.filter { $0.sportType == currentSportType }
                    allFilteredActivities.append(contentsOf: filteredBatch)
                    
                    currentRawOffset += batch.count
                    
                    // If we got fewer than batchSize, we've reached the end
                    if batch.count < batchSize {
                        hasMoreData = false
                    }
                }
                
                // Take only the page size we need
                let finalResults = Array(allFilteredActivities.prefix(pageSize))
                
                print("📊 Final results: \(finalResults.count) filtered activities (sport: \(currentSportType)), hasMoreData: \(hasMoreData), totalProcessed: \(currentRawOffset - offset)")
                
                // ✅ Smart fallback: If no activities found with current filter, try loading all activities
                if finalResults.isEmpty && offset == 0 {
                    print("⚠️ No activities found with filter \(currentSportType), loading all activities instead")
                    var fallbackDescriptor = FetchDescriptor<RunActivity>(
                        sortBy: [SortDescriptor(\.endTime, order: .reverse)]
                    )
                    fallbackDescriptor.fetchLimit = pageSize
                    let allActivities = try backgroundContext.fetch(fallbackDescriptor)
                    return (allActivities: Array(allActivities), rawProcessed: pageSize, hasMore: allActivities.count == pageSize)
                }
                
                return (allActivities: finalResults, rawProcessed: currentRawOffset - offset, hasMore: hasMoreData && finalResults.count == pageSize)
            }.value
            
            await MainActor.run {
                if isInitial {
                    self.activities = newActivities.allActivities
                    self.rawOffset = newActivities.rawProcessed
                } else {
                    self.activities.append(contentsOf: newActivities.allActivities)
                    self.rawOffset += newActivities.rawProcessed
                }
                
                self.currentOffset = self.activities.count
                self.hasMoreActivities = newActivities.hasMore
                
                print("📊 Updated state: activities=\(self.activities.count), rawOffset=\(self.rawOffset), hasMore=\(self.hasMoreActivities)")
            }
        } catch {
            print("🔴 ActivityView pagination error: \(error)")
            await MainActor.run {
                self.errorMessage = "Failed to load activities: \(error.localizedDescription)"
                self.hasMoreActivities = false
            }
        }
    }
}

// MARK: - 🚀 Loading Skeleton Component

struct ActivityRowSkeleton: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header skeleton
            HStack(alignment: .top) {
                HStack(spacing: 12) {
                    // Icon skeleton
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 32, height: 32)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        // Title skeleton
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 120, height: 16)
                        
                        // Location skeleton
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 80, height: 12)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    // Date skeleton
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 60, height: 12)
                    
                    // Time skeleton  
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 80, height: 10)
                }
            }
            .padding()
            .background(Color(.secondarySystemGroupedBackground))
            
            // Stats skeleton
            HStack {
                ForEach(0..<4, id: \.self) { index in
                    VStack(spacing: 4) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 40, height: 20)
                        
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 50, height: 10)
                    }
                    .frame(maxWidth: .infinity)
                    
                    if index < 3 {
                        Divider()
                            .frame(height: 30)
                    }
                }
            }
            .padding(.horizontal, 8)
            .padding(.bottom)
            .background(Color(.secondarySystemGroupedBackground))
        }
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
        .opacity(isAnimating ? 0.6 : 1.0)
        .animation(
            .easeInOut(duration: 1.0).repeatForever(autoreverses: true),
            value: isAnimating
        )
        .onAppear {
            isAnimating = true
        }
    }
}

/*struct MapOverlay: UIViewRepresentable {
    let polyline: MKPolyline
    let startCoordinate: CLLocationCoordinate2D
    let endCoordinate: CLLocationCoordinate2D
    @Binding var mapView: MKMapView?
    
    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        
        // Add route polyline
        mapView.addOverlay(polyline)
        
        // Add start and end markers
        let startAnnotation = MKPointAnnotation()
        startAnnotation.coordinate = startCoordinate
        startAnnotation.title = "Start"
        mapView.addAnnotation(startAnnotation)
        
        let endAnnotation = MKPointAnnotation()
        endAnnotation.coordinate = endCoordinate
        endAnnotation.title = "End"
        mapView.addAnnotation(endAnnotation)
        
        // Store the mapView reference
        self.mapView = mapView
        
        // Set the visible region to show the entire route
        let rect = polyline.boundingMapRect
        let insets = UIEdgeInsets(top: 50, left: 50, bottom: 50, right: 50)
        mapView.setVisibleMapRect(rect, edgePadding: insets, animated: false)
        
        return mapView
    }
    
    func updateUIView(_ uiView: MKMapView, context: Context) {
        // No updates needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, MKMapViewDelegate {
        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            if let polyline = overlay as? MKPolyline {
                let renderer = MKPolylineRenderer(polyline: polyline)
                renderer.strokeColor = .systemBlue
                renderer.lineWidth = 4
                renderer.lineCap = .round
                renderer.lineJoin = .round
                return renderer
            }
            return MKOverlayRenderer(overlay: overlay)
        }
        
        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            let identifier = "RunMarker"
            
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier) as? MKMarkerAnnotationView
            
            if annotationView == nil {
                annotationView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            }
            
            if annotation.title == "Start" {
                annotationView?.markerTintColor = .systemGreen
                annotationView?.glyphImage = UIImage(systemName: "flag.fill")
            } else if annotation.title == "End" {
                annotationView?.markerTintColor = .systemRed
                annotationView?.glyphImage = UIImage(systemName: "flag.checkered")
            }
            
            annotationView?.canShowCallout = true
            return annotationView
        }
    }
}
*/
struct ActivityView_Previews: PreviewProvider {
    static var previews: some View {
        ActivityView()
    }
}
