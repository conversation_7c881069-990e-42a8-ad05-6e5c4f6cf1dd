import SwiftUI
import SwiftData


struct WeightSettingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @State private var weightString: String = ""
    @State private var selectedUnit: String = "kg"
    @State private var showingAlert = false
    
    private let units = ["kg", "lbs"]  // Use "lbs" for pounds
    
    private var validRange: ClosedRange<Double> {
        selectedUnit == "lbs" ? 66.0...661.0 : 30.0...300.0
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    HStack {
                        TextField("weight".localized, text: $weightString)
                            .keyboardType(.decimalPad)
                        
                        Picker("unit".localized, selection: $selectedUnit) {
                            ForEach(units, id: \.self) { unit in
                                Text(unit)
                            }
                        }
                        .pickerStyle(.segmented)
                        .frame(width: 100)
                    }
                } footer: {
                    Text("calorie_calculations_required".localized)
                }
            }
            .navigationTitle("weight".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        saveWeight()
                    }
                }
            }
        }
        .onAppear {
            if let userProfile = profile.first {
                // Set the unit picker to user's preference
                selectedUnit = userProfile.weightUnit ?? "kg"
                
                // Get the weight in kg from database
                if let weightInKg = userProfile.weight {
                    // If user prefers lbs, convert to lbs for display
                    if selectedUnit == "lbs" {
                        let weightInLbs = weightInKg * 2.20462
                        weightString = String(format: "%.1f", weightInLbs)
                    } else {
                        // If kg, display as is
                        weightString = String(format: "%.1f", weightInKg)
                    }
                }
            }
        }
        .onChange(of: selectedUnit) { oldValue, newValue in
            if let weightInKg = profile.first?.weight {
                if newValue == "lbs" {
                    let weightInLbs = weightInKg * 2.20462
                    weightString = String(format: "%.1f", weightInLbs)
                } else {
                    weightString = String(format: "%.1f", weightInKg)
                }
            }
        }
        .alert("invalid_weight".localized, isPresented: $showingAlert) {
            Button("ok".localized, role: .cancel) { }
        } message: {
            Text(String(format: "please_enter_valid_weight".localized, Int(validRange.lowerBound), Int(validRange.upperBound), selectedUnit))
        }
    }
    
    private func saveWeight() {
        guard let inputWeight = Double(weightString),
              inputWeight >= validRange.lowerBound && inputWeight <= validRange.upperBound else {
            showingAlert = true
            return
        }
        
        // Convert to kg if input was in lbs, otherwise use as is
        let weightInKg = selectedUnit == "lbs" ? inputWeight / 2.20462 : inputWeight
        
        if let userProfile = profile.first {
            userProfile.weight = weightInKg       // Always save in kg
            userProfile.weightUnit = selectedUnit // Save user's unit preference
        } else {
            let newProfile = UserProfile(weight: weightInKg)
            newProfile.weightUnit = selectedUnit
            modelContext.insert(newProfile)
        }
        dismiss()
    }
}

// GenderSettingView has been moved to its own file: RunApp/Views/GenderSettingView.swift
struct AgeSettingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @State private var birthDate = Calendar.current.date(byAdding: .year, value: -20, to: Date()) ?? Date()
    @State private var showingAlert = false
    
    private var calculatedAge: Int {
        Calendar.current.dateComponents([.year], from: birthDate, to: Date()).year ?? 0
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    DatePicker("birth_date".localized, selection: $birthDate, in: ...Date(), displayedComponents: .date)
                } footer: {
                    Text(String(format: "%d %@", calculatedAge, "years_old".localized))
                }
            }
            .navigationTitle("age".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        saveAge()
                    }
                }
            }
        }
        .alert("invalid_age".localized, isPresented: $showingAlert) {
            Button("ok".localized, role: .cancel) { }
        } message: {
            Text("age_requirement".localized)
        }
    }
    
    private func saveAge() {
        guard calculatedAge >= 13 && calculatedAge <= 120 else {
            showingAlert = true
            return
        }
        
        if let userProfile = profile.first {
            userProfile.birthDate = birthDate
        } else {
            let newProfile = UserProfile(birthDate: birthDate)
            modelContext.insert(newProfile)
        }
        dismiss()
    }
}


struct HeightSettingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @State private var selectedUnit: String = "cm"
    @State private var heightString: String = ""
    @State private var feetString: String = ""
    @State private var inchesString: String = ""
    @State private var showingAlert = false
    
    private let units = ["cm", "ft/in"]
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    Picker("unit".localized, selection: $selectedUnit) {
                        ForEach(units, id: \.self) { unit in
                            Text(unit)
                        }
                    }
                    .pickerStyle(.segmented)
                    .onChange(of: selectedUnit) { oldValue, newValue in
                        convertAndDisplayHeight()
                    }
                    
                    if selectedUnit == "cm" {
                        TextField("height_cm".localized, text: $heightString)
                            .keyboardType(.decimalPad)
                    } else {
                        HStack {
                            TextField("feet".localized, text: $feetString)
                                .keyboardType(.numberPad)
                                .frame(width: 60)
                            TextField("inches".localized, text: $inchesString)
                                .keyboardType(.decimalPad)
                                .frame(width: 60)
                        }
                    }
                } footer: {
                    Text("bmi_calculations".localized)
                }
            }
            .navigationTitle("height".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("save".localized) {
                        saveHeight()
                    }
                }
            }
        }
        .onAppear {
            if let userProfile = profile.first {
                selectedUnit = userProfile.heightUnit ?? "cm"
                if let height = userProfile.height {
                    if selectedUnit == "cm" {
                        heightString = String(format: "%.1f", height)
                    } else {
                        let (feet, inches) = cmToFeetInches(height)
                        feetString = String(feet)
                        inchesString = String(format: "%.1f", inches)
                    }
                }
            }
        }
        .alert("invalid_height".localized, isPresented: $showingAlert) {
            Button("ok".localized, role: .cancel) { }
        } message: {
            Text("please_enter_valid_height".localized)
        }
    }
    
    private func saveHeight() {
        var heightInCm: Double?
        
        if selectedUnit == "cm" {
            guard let height = Double(heightString), height >= 30.48 && height <= 304.8 else {
                showingAlert = true
                return
            }
            heightInCm = height
        } else {
            guard let feet = Int(feetString),
                  let inches = Double(inchesString) else {
                showingAlert = true
                return
            }
            let cm = feetInchesToCm(feet: feet, inches: inches)
            guard cm >= 30.48 && cm <= 304.8 else {
                showingAlert = true
                return
            }
            heightInCm = cm
        }
        
        if let height = heightInCm, let userProfile = profile.first {
            userProfile.height = height
            userProfile.heightUnit = selectedUnit
        } else if let height = heightInCm {
            let newProfile = UserProfile(height: height)
            newProfile.heightUnit = selectedUnit
            modelContext.insert(newProfile)
        }
        dismiss()
    }
    
    private func convertAndDisplayHeight() {
        if let height = profile.first?.height {
            if selectedUnit == "cm" {
                heightString = String(format: "%.1f", height)
                feetString = ""
                inchesString = ""
            } else {
                let (feet, inches) = cmToFeetInches(height)
                feetString = String(feet)
                inchesString = String(format: "%.1f", inches)
                heightString = ""
            }
        }
    }
    
    private func cmToFeetInches(_ cm: Double) -> (feet: Int, inches: Double) {
        let totalInches = cm / 2.54
        let feet = Int(totalInches / 12)
        let inches = totalInches.truncatingRemainder(dividingBy: 12)
        return (feet, inches)
    }

    private func feetInchesToCm(feet: Int, inches: Double) -> Double {
        let totalInches = Double(feet) * 12 + inches
        return totalInches * 2.54
    }
}

#Preview {
    GenderSettingView()
}
