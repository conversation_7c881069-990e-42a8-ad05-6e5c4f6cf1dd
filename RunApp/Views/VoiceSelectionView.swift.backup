import SwiftUI

struct VoiceSelectionView: View {
    @ObservedObject private var voiceManager = VoiceManager.shared
    private var languageManager = LanguageManager.shared
    @Environment(\.dismiss) private var dismiss
    
    @Binding var userProfile: UserProfile
    
    @State private var selectedVoiceId: String?
    @State private var isLoading = false
    @State private var showingPreviewError = false
    @State private var previewErrorMessage = ""
    @State private var showingVoiceInstructions = false
    
    init(userProfile: Binding<UserProfile>) {
        self._userProfile = userProfile
    }
    
    private var currentLanguage: String {
        languageManager.currentLanguage
    }
    
    private var availableVoices: [VoiceInfo] {
        voiceManager.getVoicesForLanguage(currentLanguage)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if voiceManager.isLoading {
                    loadingView
                } else if availableVoices.isEmpty {
                    emptyStateView
                } else {
                    voiceListView
                }
            }
            .navigationTitle("voice.selection.title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("common.cancel".localized) {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("common.done".localized) {
                        saveSelection()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
            .onAppear {
                loadCurrentSelection()
            }
            .alert("voice.preview.error.title".localized, isPresented: $showingPreviewError) {
                Button("common.ok".localized, role: .cancel) { }
            } message: {
                Text(previewErrorMessage)
            }
            .alert("voice.instructions.title".localized, isPresented: $showingVoiceInstructions) {
                Button("voice.instructions.open.settings".localized) {
                    openSettingsApp()
                }
                Button("common.cancel".localized, role: .cancel) { }
            } message: {
                Text("voice.instructions.message".localized)
            }
        }
    }
    
    @ViewBuilder
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("voice.loading.voices".localized)
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    @ViewBuilder
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "speaker.slash")
                .font(.system(size: 50))
                .foregroundColor(.secondary)
            
            Text("voice.empty.title".localized)
                .font(.headline)
                .multilineTextAlignment(.center)
            
            Text("voice.empty.message".localized)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            VStack(spacing: 12) {
                Button("voice.add.new".localized) {
                    openVoiceSettings()
                }
                .buttonStyle(.borderedProminent)
                
                Button("voice.refresh.voices".localized) {
                    refreshVoices()
                }
                .buttonStyle(.bordered)
            }
            
            VStack(spacing: 8) {
                Text("voice.instructions.steps.title".localized)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                
                Text("voice.instructions.steps.detail".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal)
            .padding(.top, 8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    @ViewBuilder
    private var voiceListView: some View {
        VStack(spacing: 0) {
            // Language info header
            languageInfoHeader
            
            // Voice list
            List {
                ForEach(groupedVoices.keys.sorted(by: qualitySortOrder), id: \.self) { quality in
                    Section {
                        ForEach(groupedVoices[quality] ?? [], id: \.id) { voice in
                            VoiceRowView(
                                voice: voice,
                                isSelected: voice.id == selectedVoiceId,
                                onSelect: {
                                    selectedVoiceId = voice.id
                                },
                                onPreview: {
                                    previewVoice(voice)
                                }
                            )
                        }
                    } header: {
                        qualitySectionHeader(for: quality)
                    }
                }
                
                // Add New Voice section
                Section {
                    Button(action: openVoiceSettings) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(.blue)
                            VStack(alignment: .leading, spacing: 2) {
                                Text("voice.add.new".localized)
                                    .foregroundColor(.blue)
                                Text("voice.add.new.subtitle".localized)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                            Image(systemName: "arrow.up.right.square")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                } footer: {
                    Text("voice.instructions.footer".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Refresh section
                Section {
                    Button(action: refreshVoices) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("voice.refresh.voices".localized)
                        }
                        .foregroundColor(.blue)
                    }
                } footer: {
                    Text("voice.refresh.footer".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .listStyle(InsetGroupedListStyle())
        }
    }
    
    @ViewBuilder
    private var languageInfoHeader: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.blue)
                
                Text("voice.current.language".localized)
                    .font(.headline)
                
                Spacer()
                
                Text(getLanguageDisplayName(currentLanguage))
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
            }
            .padding(.horizontal)
            .padding(.top)
            
            Text("voice.selection.description".localized)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
                .padding(.bottom, 8)
        }
        .background(Color(.systemGroupedBackground))
    }
    
    private var groupedVoices: [VoiceQuality: [VoiceInfo]] {
        Dictionary(grouping: availableVoices, by: \.quality)
    }
    
    private func qualitySortOrder(_ lhs: VoiceQuality, _ rhs: VoiceQuality) -> Bool {
        lhs.priority > rhs.priority
    }
    
    @ViewBuilder
    private func qualitySectionHeader(for quality: VoiceQuality) -> some View {
        HStack {
            Image(systemName: qualityIcon(for: quality))
                .foregroundColor(qualityColor(for: quality))
            
            Text(quality.displayName)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
            
            if let count = groupedVoices[quality]?.count {
                Text("\(count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private func qualityIcon(for quality: VoiceQuality) -> String {
        switch quality {
        case .standard: return "star"
        case .enhanced: return "star.fill"
        case .premium: return "crown.fill"
        }
    }
    
    private func qualityColor(for quality: VoiceQuality) -> Color {
        switch quality {
        case .standard: return .gray
        case .enhanced: return .blue
        case .premium: return .purple
        }
    }
    
    private func getLanguageDisplayName(_ languageCode: String) -> String {
        let locale = Locale.current
        return locale.localizedString(forLanguageCode: languageCode) ?? languageCode
    }
    
    private func loadCurrentSelection() {
        selectedVoiceId = userProfile.getVoiceForLanguage(currentLanguage)
        
        // If no selection, try to select the best available voice
        if selectedVoiceId == nil {
            if let bestVoice = voiceManager.getBestVoiceForLanguage(currentLanguage) {
                selectedVoiceId = bestVoice.id
            }
        }
    }
    
    private func saveSelection() {
        if let voiceId = selectedVoiceId {
            userProfile.setVoiceForLanguage(currentLanguage, voiceIdentifier: voiceId)
            
            // Update the audio alert manager with the new profile
            AudioAlertManager.shared.updateUserProfile(userProfile)
        }
    }
    
    private func previewVoice(_ voice: VoiceInfo) {
        print("VoiceSelectionView: Starting voice preview for \(voice.name) (ID: \(voice.id))")
        
        VoiceManager.shared.testVoice(voice) { success in
            DispatchQueue.main.async {
                print("VoiceSelectionView: Voice preview completed with success: \(success)")
                if !success {
                    previewErrorMessage = String(format: "voice.preview.error.message".localized, voice.name)
                    showingPreviewError = true
                }
            }
        }
    }
    
    private func refreshVoices() {
        isLoading = true
        voiceManager.refreshVoices()
        
        // Refresh will happen automatically via @ObservedObject
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false
        }
    }
    
    private func openVoiceSettings() {
        // Show instructions alert first
        showingVoiceInstructions = true
    }
    
    private func openSettingsApp() {
        // Open iOS Settings app
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            if UIApplication.shared.canOpenURL(settingsUrl) {
                UIApplication.shared.open(settingsUrl, completionHandler: { success in
                    print("VoiceSelectionView: Opened Settings app with success: \(success)")
                })
            }
        }
    }
}

#Preview {
    struct PreviewWrapper: View {
        @State private var profile = UserProfile()
        var body: some View {
            VoiceSelectionView(userProfile: $profile)
        }
    }
    return PreviewWrapper()
}
