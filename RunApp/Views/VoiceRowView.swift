import SwiftUI
import AVFoundation

struct VoiceRowView: View {
    let voice: VoiceInfo
    let isSelected: Bool
    let onSelect: () -> Void
    let onPreview: () -> Void
    
    @State private var isPreviewPlaying = false
    
    // MARK: - Design Tokens (matching VoiceSelectionView)
    private struct DesignTokens {
        static let iconSize: Font = .system(size: 18, weight: .medium)
        static let primaryIconSize: Font = .system(size: 20, weight: .medium)
        static let smallIconSize: Font = .system(size: 14, weight: .medium)
        static let primaryColor: Color = .blue
        static let secondaryColor: Color = .secondary
        static let tertiaryColor: Color = Color(.tertiaryLabel)
        static let premiumColor: Color = .purple
        static let enhancedColor: Color = .blue
        static let standardColor: Color = .gray
        static let standardSpacing: CGFloat = 12
        static let compactSpacing: CGFloat = 8
    }
    
    var body: some View {
        HStack(spacing: DesignTokens.standardSpacing) {
            // Voice selection indicator
            Button(action: onSelect) {
                HStack(spacing: DesignTokens.standardSpacing) {
                    // Selection indicator (simplified - no extra background circles)
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .font(DesignTokens.primaryIconSize)
                        .foregroundColor(isSelected ? qualityColor(for: voice.quality) : DesignTokens.secondaryColor)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        // Voice name with quality indicator
                        HStack(spacing: 6) {
                            Text(voice.name)
                                .font(.headline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                            
                            if voice.quality == .premium {
                                Image(systemName: "crown.fill")
                                    .font(.system(size: 12, weight: .semibold))
                                    .foregroundColor(DesignTokens.premiumColor)
                            }
                        }
                        
                        // Voice details and quality description
                        VStack(alignment: .leading, spacing: 3) {
                            if !voice.isInstalled {
                                installationStatus
                            }
                            
                            // Quality description for workout context
                            Text(qualityDescription(for: voice.quality))
                                .font(.caption)
                                .foregroundColor(qualityColor(for: voice.quality))
                                .fontWeight(.medium)
                        }
                    }
                    
                    Spacer()
                }
                .contentShape(Rectangle())
            }
            .buttonStyle(PlainButtonStyle())
            
            // Preview button (simplified - no extra background circles)
            Button(action: {
                isPreviewPlaying = true
                onPreview()
                
                // Reset preview state after a delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    isPreviewPlaying = false
                }
            }) {
                Image(systemName: isPreviewPlaying ? "speaker.wave.2.fill" : "speaker.wave.2")
                    .font(DesignTokens.primaryIconSize)
                    .foregroundColor(voice.quality == .premium ? DesignTokens.premiumColor : DesignTokens.primaryColor)
                    .scaleEffect(isPreviewPlaying ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: isPreviewPlaying)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(isPreviewPlaying)
        }
        .padding(.vertical, DesignTokens.compactSpacing) // Use same padding for all voice types
        // Remove the background rectangle for premium voices
    }
    
    @ViewBuilder
    private var installationStatus: some View {
        HStack(spacing: 4) {
            Image(systemName: "icloud.and.arrow.down")
                .font(DesignTokens.smallIconSize)
            
            Text("voice.download.required".localized)
                .font(.footnote)
                .fontWeight(.medium)
        }
        .padding(.horizontal, DesignTokens.compactSpacing)
        .padding(.vertical, 4)
        .background(Color.orange.opacity(0.15))
        .foregroundColor(.orange)
        .cornerRadius(6)
    }
    
    private func qualityColor(for quality: VoiceQuality) -> Color {
        switch quality {
        case .standard: return DesignTokens.standardColor
        case .enhanced: return DesignTokens.enhancedColor
        case .premium: return DesignTokens.premiumColor
        }
    }
    
    private func qualityDescription(for quality: VoiceQuality) -> String {
        switch quality {
        case .standard:
            return "voice.quality.standard.description".localized
        case .enhanced:
            return "voice.quality.enhanced.description".localized
        case .premium:
            return "voice.quality.premium.description".localized
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        VoiceRowView(
            voice: VoiceInfo(
                id: "com.apple.ttsbundle.Zoe-premium",
                name: "Zoe",
                language: "en-US",
                quality: .premium,
                isInstalled: true,
                isDeletable: true,
                voice: AVSpeechSynthesisVoice(language: "en-US")!
            ),
            isSelected: true,
            onSelect: {},
            onPreview: {}
        )
        
        VoiceRowView(
            voice: VoiceInfo(
                id: "com.apple.ttsbundle.Samantha-compact",
                name: "Samantha",
                language: "en-US",
                quality: .enhanced,
                isInstalled: true,
                isDeletable: true,
                voice: AVSpeechSynthesisVoice(language: "en-US")!
            ),
            isSelected: false,
            onSelect: {},
            onPreview: {}
        )
        
        VoiceRowView(
            voice: VoiceInfo(
                id: "com.apple.ttsbundle.Alex",
                name: "Alex",
                language: "en-US",
                quality: .standard,
                isInstalled: true,
                isDeletable: false,
                voice: AVSpeechSynthesisVoice(language: "en-US")!
            ),
            isSelected: false,
            onSelect: {},
            onPreview: {}
        )
    }
    .padding()
}
