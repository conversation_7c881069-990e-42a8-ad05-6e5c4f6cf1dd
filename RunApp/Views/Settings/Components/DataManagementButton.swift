import SwiftUI

struct DataManagementButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let isLoading: Bool
    let loadingText: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                ZStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: color))
                    } else {
                        Image(systemName: icon)
                            .font(.system(size: 20, weight: .medium))
                            .foregroundStyle(color)
                    }
                }
                .frame(width: 28, height: 28)
                
                // Text content
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundStyle(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Text(isLoading ? loadingText : subtitle)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Chevron
                if !isLoading {
                    Image(systemName: "chevron.right")
                        .font(.caption.bold())
                        .foregroundStyle(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.tertiarySystemGroupedBackground))
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .disabled(isLoading)
    }
}

#Preview {
    VStack(spacing: 12) {
        DataManagementButton(
            title: "Export All Data",
            subtitle: "Profile and activities in 2 files",
            icon: "doc.on.doc",
            color: .blue,
            isLoading: false,
            loadingText: "Exporting..."
        ) {
            // Action
        }
        
        DataManagementButton(
            title: "Export All Data",
            subtitle: "Profile and activities in 2 files",
            icon: "doc.on.doc",
            color: .blue,
            isLoading: true,
            loadingText: "Exporting..."
        ) {
            // Action
        }
        
        DataManagementButton(
            title: "Delete All Data",
            subtitle: "Permanently remove all data",
            icon: "trash.fill",
            color: .red,
            isLoading: false,
            loadingText: "Deleting..."
        ) {
            // Action
        }
    }
    .padding()
} 