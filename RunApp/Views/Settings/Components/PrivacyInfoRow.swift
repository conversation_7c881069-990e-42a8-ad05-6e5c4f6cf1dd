import SwiftUI

struct PrivacyInfoRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundStyle(.green)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundStyle(.primary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
    }
}

#Preview {
    VStack(spacing: 8) {
        PrivacyInfoRow(
            icon: "person.badge.shield.checkmark",
            text: "Your data belongs to you"
        )
        PrivacyInfoRow(
            icon: "icloud.and.arrow.up",
            text: "Stored securely in your iCloud account"
        )
        PrivacyInfoRow(
            icon: "eye.slash",
            text: "We have no access to your personal data"
        )
        PrivacyInfoRow(
            icon: "hand.raised.shield",
            text: "You have full control over your data"
        )
    }
    .padding()
} 