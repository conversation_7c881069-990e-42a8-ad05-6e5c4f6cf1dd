import SwiftUI
import SwiftData

struct MetronomeSettingsModule: View {
    @Binding var userProfile: UserProfile?
    let modelContext: ModelContext
    let presentationStyle: PresentationStyle
    
    @StateObject private var metronome = MetronomeManager.shared
    
    @State private var bpm: Double = 180
    @State private var temporaryBpm: Double = 180
    @State private var soundEnabled: Bool = true
    @State private var vibrationEnabled: Bool = false
    @State private var selectedSound: Int = 2003
    @State private var alertFrequency: MetronomeAlertFrequency = .everyOtherBeat
    @State private var volume: Float = 0.8
    
    enum PresentationStyle {
        case sheet      // Full navigation with done button
        case embedded   // Embedded in another view
    }
    
    // MARK: - Design Tokens
    private struct DesignTokens {
        static let iconSize: Font = .system(size: 18, weight: .medium)
        static let primaryIconSize: Font = .system(size: 20, weight: .medium)
        static let smallIconSize: Font = .system(size: 14, weight: .medium)
        static let primaryColor: Color = .blue
        static let secondaryColor: Color = .secondary
        static let tertiaryColor: Color = Color(.tertiaryLabel)
        static let accentColor: Color = .accentColor
        static let standardSpacing: CGFloat = 12
        static let compactSpacing: CGFloat = 8
        static let sectionSpacing: CGFloat = 16
    }
    
    // Sound options
    private let soundOptions = [
        (id: 2003, name: "beat_1".localized),
        (id: 2004, name: "beat_2".localized),
        (id: 2005, name: "beat_3".localized),
        (id: 2006, name: "beat_4".localized), // Tink
        (id: 2007, name: "beat_5".localized), // Tock
        (id: 2008, name: "beat_6".localized)  // sq_tock
    ]
    
    init(userProfile: Binding<UserProfile?>, modelContext: ModelContext, presentationStyle: PresentationStyle = .embedded) {
        self._userProfile = userProfile
        self.modelContext = modelContext
        self.presentationStyle = presentationStyle
    }

    var body: some View {
        Group {
            if presentationStyle == .embedded {
                embeddedView
            } else {
                sheetView
            }
        }
        .onAppear {
            setupInitialValues()
        }
        .onDisappear {
            saveSettings()
        }
    }
    
    @ViewBuilder
    private var embeddedView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Master toggle section
            VStack(alignment: .leading, spacing: 8) {
                Toggle("enable_metronome".localized, isOn: Binding(
                    get: { metronome.isEnabled },
                    set: { newValue in
                        metronome.toggleMetronome()
                        if let userProfile = userProfile {
                            userProfile.metronomeEnabled = newValue
                            try? modelContext.save()
                        }
                    }
                ))
                .font(.body)
                .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.secondarySystemGroupedBackground))
            .cornerRadius(10)
            
            if metronome.isEnabled {
                if soundEnabled {
                    embeddedVolumeControl
                }
                embeddedTempoControl
                embeddedFeedbackControl
            }
        }
    }
    
    @ViewBuilder
    private var embeddedVolumeControl: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("audio_volume".localized)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            VStack(alignment: .leading, spacing: DesignTokens.standardSpacing) {
                // Volume header
                HStack(spacing: DesignTokens.compactSpacing) {
                    Image(systemName: "speaker.fill")
                        .font(DesignTokens.iconSize)
                        .foregroundColor(DesignTokens.primaryColor)
                    
                    Text("volume".localized)
                        .font(.body)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(Int(volume * 100))%")
                        .font(.footnote)
                        .fontWeight(.medium)
                        .foregroundColor(DesignTokens.secondaryColor)
                }
                
                // Volume slider
                HStack(spacing: DesignTokens.standardSpacing) {
                    Image(systemName: "speaker.wave.1")
                        .font(DesignTokens.smallIconSize)
                        .foregroundColor(DesignTokens.tertiaryColor)
                    
                    Slider(value: Binding(
                        get: { Double(volume) },
                        set: { newValue in
                            volume = Float(newValue)
                            metronome.setVolume(volume)
                            saveVolumeSettings()
                        }
                    ), in: 0...1, step: 0.01)
                    .tint(DesignTokens.accentColor)
                    
                    Image(systemName: "speaker.wave.3")
                        .font(DesignTokens.smallIconSize)
                        .foregroundColor(DesignTokens.tertiaryColor)
                }
            }
            
            Text("audio_volume_footer".localized)
                .font(.footnote)
                .foregroundColor(DesignTokens.secondaryColor)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(10)
    }
    
    @ViewBuilder
    private var embeddedTempoControl: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("tempo".localized)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            VStack(alignment: .leading, spacing: DesignTokens.standardSpacing) {
                // Tempo header
                HStack(spacing: DesignTokens.compactSpacing) {
                    Text("tempo".localized + ": \(Int(temporaryBpm)) " + "bpm".localized)
                        .font(.body)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text(tempoDescription.localized)
                        .font(.footnote)
                        .fontWeight(.medium)
                        .foregroundColor(DesignTokens.secondaryColor)
                }
                
                // Tempo controls
                HStack(spacing: DesignTokens.standardSpacing) {
                    Button {
                        if temporaryBpm > 40 {
                            temporaryBpm -= 1
                            bpm = temporaryBpm
                            saveSettings()
                        }
                    } label: {
                        Image(systemName: "minus.circle.fill")
                            .font(DesignTokens.primaryIconSize)
                            .foregroundColor(temporaryBpm > 40 ? DesignTokens.primaryColor : DesignTokens.tertiaryColor)
                    }
                    .buttonStyle(.borderless)

                    Slider(value: $temporaryBpm,
                           in: 40...240,
                           step: 1) { editing in
                        if !editing {
                            bpm = temporaryBpm
                            saveSettings()
                        }
                    }
                    .tint(DesignTokens.accentColor)

                    Button {
                        if temporaryBpm < 240 {
                            temporaryBpm += 1
                            bpm = temporaryBpm
                            saveSettings()
                        }
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(DesignTokens.primaryIconSize)
                            .foregroundColor(temporaryBpm < 240 ? DesignTokens.primaryColor : DesignTokens.tertiaryColor)
                    }
                    .buttonStyle(.borderless)
                }
            }
            
            Text("tempo_footer".localized)
                .font(.footnote)
                .foregroundColor(DesignTokens.secondaryColor)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(10)
    }
    
    @ViewBuilder
    private var embeddedFeedbackControl: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("feedback".localized)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            VStack(alignment: .leading, spacing: 12) {
                Toggle("sound".localized, isOn: $soundEnabled)
                    .font(.body)
                    .fontWeight(.medium)
                    .onChange(of: soundEnabled) { _, newValue in
                        saveSettings()
                    }
                
                if soundEnabled {
                    Picker("sound_type".localized, selection: $selectedSound) {
                        ForEach(soundOptions, id: \.id) { option in
                            Text(option.name)
                                .font(.body)
                                .fontWeight(.medium)
                                .tag(option.id)
                        }
                    }
                    .onChange(of: selectedSound) { oldValue, newValue in
                        metronome.previewSound(newValue, times: 5, bpm: 120)
                        saveSettings()
                    }
                }
                
                Toggle("vibration".localized, isOn: $vibrationEnabled)
                    .font(.body)
                    .fontWeight(.medium)
                    .onChange(of: vibrationEnabled) { _, _ in
                        saveSettings()
                    }
                
                if soundEnabled || vibrationEnabled {
                    Picker("alert_frequency".localized, selection: $alertFrequency) {
                        ForEach(MetronomeAlertFrequency.allCases, id: \.self) { frequency in
                            Text(frequency.description.localized)
                                .font(.body)
                                .fontWeight(.medium)
                                .tag(frequency)
                        }
                    }
                    .onChange(of: alertFrequency) { _, _ in
                        saveSettings()
                    }
                }
            }
            
            Text("feedback_footer".localized)
                .font(.footnote)
                .foregroundColor(DesignTokens.secondaryColor)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(10)
    }
    
    @ViewBuilder
    private var sheetView: some View {
        Form {
            // Master toggle section
            Section {
                Toggle("enable_metronome".localized, isOn: Binding(
                    get: { metronome.isEnabled },
                    set: { newValue in
                        metronome.toggleMetronome()
                        if let userProfile = userProfile {
                            userProfile.metronomeEnabled = newValue
                            try? modelContext.save()
                        }
                    }
                ))
                .font(.body)
                .fontWeight(.medium)
            }
            
            if metronome.isEnabled {
                // Volume Control Section
                if soundEnabled {
                    Section {
                        VStack(alignment: .leading, spacing: DesignTokens.standardSpacing) {
                            // Volume header
                            HStack(spacing: DesignTokens.compactSpacing) {
                                Image(systemName: "speaker.fill")
                                    .font(DesignTokens.iconSize)
                                    .foregroundColor(DesignTokens.primaryColor)
                                
                                Text("volume".localized)
                                    .font(.body)
                                    .fontWeight(.medium)
                                
                                Spacer()
                                
                                Text("\(Int(volume * 100))%")
                                    .font(.footnote)
                                    .fontWeight(.medium)
                                    .foregroundColor(DesignTokens.secondaryColor)
                            }
                            
                            // Volume slider
                            HStack(spacing: DesignTokens.standardSpacing) {
                                Image(systemName: "speaker.wave.1")
                                    .font(DesignTokens.smallIconSize)
                                    .foregroundColor(DesignTokens.tertiaryColor)
                                
                                Slider(value: Binding(
                                    get: { Double(volume) },
                                    set: { newValue in
                                        volume = Float(newValue)
                                        metronome.setVolume(volume)
                                        saveVolumeSettings()
                                    }
                                ), in: 0...1, step: 0.01)
                                .tint(DesignTokens.accentColor)
                                
                                Image(systemName: "speaker.wave.3")
                                    .font(DesignTokens.smallIconSize)
                                    .foregroundColor(DesignTokens.tertiaryColor)
                            }
                        }
                        .padding(.vertical, DesignTokens.compactSpacing)
                    } header: {
                        Text("audio_volume".localized)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    } footer: {
                        Text("audio_volume_footer".localized)
                            .font(.footnote)
                            .foregroundColor(DesignTokens.secondaryColor)
                    }
                }
                
                // Tempo Section
                Section {
                    VStack(alignment: .leading, spacing: DesignTokens.standardSpacing) {
                        // Tempo header
                        HStack(spacing: DesignTokens.compactSpacing) {
                            Text("tempo".localized + ": \(Int(temporaryBpm)) " + "bpm".localized)
                                .font(.body)
                                .fontWeight(.medium)
                            
                            Spacer()
                            
                            Text(tempoDescription.localized)
                                .font(.footnote)
                                .fontWeight(.medium)
                                .foregroundColor(DesignTokens.secondaryColor)
                        }
                        
                        // Tempo controls
                        HStack(spacing: DesignTokens.standardSpacing) {
                            Button {
                                if temporaryBpm > 40 {
                                    temporaryBpm -= 1
                                    bpm = temporaryBpm
                                    saveSettings()
                                }
                            } label: {
                                Image(systemName: "minus.circle.fill")
                                    .font(DesignTokens.primaryIconSize)
                                    .foregroundColor(temporaryBpm > 40 ? DesignTokens.primaryColor : DesignTokens.tertiaryColor)
                            }
                            .buttonStyle(.borderless)

                            Slider(value: $temporaryBpm,
                                   in: 40...240,
                                   step: 1) { editing in
                                if !editing {
                                    bpm = temporaryBpm
                                    saveSettings()
                                }
                            }
                            .tint(DesignTokens.accentColor)

                            Button {
                                if temporaryBpm < 240 {
                                    temporaryBpm += 1
                                    bpm = temporaryBpm
                                    saveSettings()
                                }
                            } label: {
                                Image(systemName: "plus.circle.fill")
                                    .font(DesignTokens.primaryIconSize)
                                    .foregroundColor(temporaryBpm < 240 ? DesignTokens.primaryColor : DesignTokens.tertiaryColor)
                            }
                            .buttonStyle(.borderless)
                        }
                    }
                    .padding(.vertical, DesignTokens.compactSpacing)
                } header: {
                    Text("tempo".localized)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                } footer: {
                    Text("tempo_footer".localized)
                        .font(.footnote)
                        .foregroundColor(DesignTokens.secondaryColor)
                }
                
                // Feedback Section
                Section {
                    Toggle("sound".localized, isOn: $soundEnabled)
                        .font(.body)
                        .fontWeight(.medium)
                        .onChange(of: soundEnabled) { _, newValue in
                            saveSettings()
                        }
                    
                    if soundEnabled {
                        Picker("sound_type".localized, selection: $selectedSound) {
                            ForEach(soundOptions, id: \.id) { option in
                                Text(option.name)
                                    .font(.body)
                                    .fontWeight(.medium)
                                    .tag(option.id)
                            }
                        }
                        .onChange(of: selectedSound) { oldValue, newValue in
                            metronome.previewSound(newValue, times: 5, bpm: 120)
                            saveSettings()
                        }
                    }
                    
                    Toggle("vibration".localized, isOn: $vibrationEnabled)
                        .font(.body)
                        .fontWeight(.medium)
                        .onChange(of: vibrationEnabled) { _, _ in
                            saveSettings()
                        }
                    
                    if soundEnabled || vibrationEnabled {
                        Picker("alert_frequency".localized, selection: $alertFrequency) {
                            ForEach(MetronomeAlertFrequency.allCases, id: \.self) { frequency in
                                Text(frequency.description.localized)
                                    .font(.body)
                                    .fontWeight(.medium)
                                    .tag(frequency)
                            }
                        }
                        .onChange(of: alertFrequency) { _, _ in
                            saveSettings()
                        }
                    }
                } header: {
                    Text("feedback".localized)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                } footer: {
                    Text("feedback_footer".localized)
                        .font(.footnote)
                        .foregroundColor(DesignTokens.secondaryColor)
                }
            }
        }
    }
    
    private var tempoDescription: String {
        switch Int(temporaryBpm) {
        case 40...60: return "largo"
        case 61...75: return "adagio"
        case 76...108: return "andante"
        case 109...120: return "moderato"
        case 121...168: return "allegro"
        case 169...200: return "presto"
        default: return "prestissimo"
        }
    }
    
    private func saveSettings() {
        print("[MetronomeSettingsModule] saveSettings: Saving BPM: \(Int(bpm)) to UserProfile and MetronomeManager")
        metronome.updateSettings(
            bpm: Int(bpm),
            soundEnabled: soundEnabled,
            vibrationEnabled: vibrationEnabled,
            soundType: selectedSound,
            alertFrequency: alertFrequency
        )
        
        if let profile = userProfile {
            profile.metronomeBPM = Int(bpm)
            profile.metronomeSound = selectedSound
            profile.metronomeSoundEnabled = soundEnabled
            profile.metronomeVibrationEnabled = vibrationEnabled
            profile.metronomeAlertFrequency = alertFrequency
            
            try? modelContext.save()
        }
    }
    
    private func saveVolumeSettings() {
        if let profile = userProfile {
            profile.metronomeVolume = volume
            try? modelContext.save()
        }
    }
    
    private func setupInitialValues() {
        loadSettings()
    }
    
    private func loadSettings() {
        if let profile = userProfile {
            print("[MetronomeSettingsModule] loadSettings: Loaded BPM from UserProfile: \(profile.metronomeBPM)")
            bpm = Double(profile.metronomeBPM)
            temporaryBpm = bpm
            
            // Ensure selectedSound is valid, otherwise default to Beat 1
            let validSoundIDs = soundOptions.map { $0.id }
            if validSoundIDs.contains(profile.metronomeSound) {
                selectedSound = profile.metronomeSound
            } else {
                print("[MetronomeSettingsModule] loadSettings: Invalid sound ID \(profile.metronomeSound) from profile. Defaulting to Beat 1 (2003).")
                selectedSound = 2003 // Default to Beat 1 (ID 2003)
            }
            
            soundEnabled = profile.metronomeSoundEnabled
            vibrationEnabled = profile.metronomeVibrationEnabled
            alertFrequency = profile.metronomeAlertFrequency
            volume = profile.metronomeVolume
            
            // Set the volume in the manager
            metronome.setVolume(volume)
        }
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    return MetronomeSettingsModule(
        userProfile: .constant(nil),
        modelContext: container.mainContext,
        presentationStyle: .embedded
    )
    .modelContainer(container)
}
