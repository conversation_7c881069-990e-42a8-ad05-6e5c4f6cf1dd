import SwiftUI
import SwiftData

/// Quick test view to verify workout settings routing works correctly
struct WorkoutSettingsTest: View {
    @StateObject private var appStateManager = AppStateManager.shared
    @State private var showingSettings = false
    
    var body: some View {
        VStack(spacing: 30) {
            Text("Workout Settings Test")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            VStack(spacing: 16) {
                Text("App State: \(appStateManager.isInWorkoutMode ? "WORKOUT MODE" : "NORMAL MODE")")
                    .font(.headline)
                    .foregroundColor(appStateManager.isInWorkoutMode ? .green : .blue)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(appStateManager.isInWorkoutMode ? Color.green.opacity(0.1) : Color.blue.opacity(0.1))
                    )
                
                <PERSON><PERSON>("Toggle Workout Mode") {
                    if appStateManager.isInWorkoutMode {
                        appStateManager.exitWorkoutMode()
                    } else {
                        appStateManager.enterWorkoutMode()
                    }
                }
                .buttonStyle(.borderedProminent)
                
                But<PERSON>("Show Settings") {
                    showingSettings = true
                }
                .buttonStyle(.bordered)
                
                Text("Expected: \(appStateManager.isInWorkoutMode ? "WorkoutSettingsView" : "SettingsView")")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .sheet(isPresented: $showingSettings) {
            // This mimics the exact logic from ContentView
            if appStateManager.isInWorkoutMode {
                WorkoutSettingsView()
            } else {
                SettingsView()
            }
        }
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    return WorkoutSettingsTest()
        .modelContainer(container)
}
