import SwiftUI
import SwiftData

/// This is a simple test view to verify the scoped settings implementation
struct TestWorkoutSettingsView: View {
    @StateObject private var appStateManager = AppStateManager.shared
    @State private var showingWorkoutSettings = false
    @State private var showingRegularSettings = false
    
    var body: some View {
        VStack(spacing: 30) {
            Text("Scoped Settings Test")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            VStack(spacing: 16) {
                Text("Current Mode: \(appStateManager.isInWorkoutMode ? "Workout" : "Normal")")
                    .font(.headline)
                    .foregroundColor(appStateManager.isInWorkoutMode ? .green : .blue)
                
                But<PERSON>("Toggle Workout Mode") {
                    if appStateManager.isInWorkoutMode {
                        appStateManager.exitWorkoutMode()
                    } else {
                        appStateManager.enterWorkoutMode()
                    }
                }
                .buttonStyle(.borderedProminent)
                
                Divider()
                
                <PERSON><PERSON>("Show Workout Settings") {
                    showingWorkoutSettings = true
                }
                .buttonStyle(.bordered)
                
                But<PERSON>("Show Regular Settings") {
                    showingRegularSettings = true
                }
                .buttonStyle(.bordered)
            }
            
            Spacer()
        }
        .padding()
        .sheet(isPresented: $showingWorkoutSettings) {
            WorkoutSettingsView()
        }
        .sheet(isPresented: $showingRegularSettings) {
            SettingsView()
        }
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    return TestWorkoutSettingsView()
        .modelContainer(container)
}
