import SwiftUI
import SwiftData
import UniformTypeIdentifiers

struct DataManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    
    // Callback to dismiss both DataManagementView and SettingsView
    let onDismissToContentView: (() -> Void)?
    
    @State private var isExportingProfile = false
    @State private var isExportingActivities = false
    @State private var isImporting = false
    @State private var isDeletingData = false
    @State private var exportError: String?
    @State private var importError: String?
    @State private var deleteError: String?
    @State private var showingShareSheet = false
    @State private var exportURLs: [URL] = []
    @State private var showingImportPicker = false
    @State private var showingDeleteConfirmation = false
    @State private var importSuccess = false
    @State private var deleteSuccess = false
    
    private let languageManager = LanguageManager.shared
    
    // Initializer
    init(onDismissToContentView: (() -> Void)? = nil) {
        self.onDismissToContentView = onDismissToContentView
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Privacy Information Section
                    privacyInformationSection
                    
                    // Export Data Section
                    exportDataSection
                    
                    // Import Data Section  
                    importDataSection
                    
                    // Delete Data Section
                    deleteDataSection
                }
                .padding()
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("data_management".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundStyle(.secondary)
                            .font(.title2)
                    }
                }
            }
        }
        .environment(\.layoutDirection, languageManager.isRTL ? .rightToLeft : .leftToRight)
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(activityItems: exportURLs)
                .presentationDetents([.medium, .large])
        }
        .fileImporter(
            isPresented: $showingImportPicker,
            allowedContentTypes: [.commaSeparatedText],
            allowsMultipleSelection: true
        ) { result in
            handleImportFiles(result)
        }
        .alert("export_error".localized, isPresented: .init(
            get: { exportError != nil },
            set: { if !$0 { exportError = nil } }
        )) {
            Button("ok".localized, role: .cancel) { exportError = nil }
        } message: {
            Text(verbatim: exportError ?? "")
        }
        .alert("import_error".localized, isPresented: .init(
            get: { importError != nil },
            set: { if !$0 { importError = nil } }
        )) {
            Button("ok".localized, role: .cancel) { importError = nil }
        } message: {
            Text(verbatim: importError ?? "")
        }
        .alert("delete_error".localized, isPresented: .init(
            get: { deleteError != nil },
            set: { if !$0 { deleteError = nil } }
        )) {
            Button("ok".localized, role: .cancel) { deleteError = nil }
        } message: {
            Text(verbatim: deleteError ?? "")
        }
        .alert("import_success".localized, isPresented: $importSuccess) {
            Button("ok".localized, role: .cancel) { }
        } message: {
            Text("import_success_message".localized)
        }
        .alert("delete_success".localized, isPresented: $deleteSuccess) {
            Button("ok".localized, role: .cancel) {
                // When delete is successful, return to ContentView
                onDismissToContentView?()
            }
        } message: {
            Text("delete_success_message".localized)
        }
        .alert("delete_confirmation".localized, isPresented: $showingDeleteConfirmation) {
            Button("cancel".localized, role: .cancel) { }
            Button("delete_permanently".localized, role: .destructive) {
                Task {
                    await performDataDeletion()
                }
            }
        } message: {
            Text("delete_confirmation_message".localized)
        }
    }
    
    // MARK: - Privacy Information Section
    
    private var privacyInformationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lock.shield")
                    .foregroundStyle(.green)
                    .font(.title2)
                Text("data_privacy".localized)
                    .font(.headline)
                    .foregroundStyle(.primary)
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 12) {
                PrivacyInfoRow(
                    icon: "person.badge.shield.checkmark",
                    text: "privacy_ownership".localized
                )
                PrivacyInfoRow(
                    icon: "icloud.and.arrow.up",
                    text: "privacy_icloud_storage".localized
                )
                PrivacyInfoRow(
                    icon: "eye.slash",
                    text: "privacy_no_access".localized
                )
                PrivacyInfoRow(
                    icon: "checkmark.shield",
                    text: "privacy_full_control".localized
                )
            }
        }
        .padding(20)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Export Data Section
    
    private var exportDataSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "square.and.arrow.up")
                    .foregroundStyle(.blue)
                    .font(.title2)
                Text("export_data".localized)
                    .font(.headline)
                    .foregroundStyle(.primary)
                Spacer()
            }
            
            Text("export_data_description".localized)
                .font(.subheadline)
                .foregroundStyle(.secondary)
            
            VStack(spacing: 12) {
                DataManagementButton(
                    title: "export_all_data".localized,
                    subtitle: "export_all_description".localized,
                    icon: "doc.on.doc",
                    color: .blue,
                    isLoading: isExportingProfile || isExportingActivities,
                    loadingText: "exporting".localized
                ) {
                    Task {
                        await exportAllData()
                    }
                }
                
                DataManagementButton(
                    title: "export_profile_only".localized,
                    subtitle: "export_profile_description".localized,
                    icon: "person.circle",
                    color: .purple,
                    isLoading: isExportingProfile,
                    loadingText: "exporting".localized
                ) {
                    Task {
                        await exportProfileData()
                    }
                }
                
                DataManagementButton(
                    title: "export_activities_only".localized,
                    subtitle: "export_activities_description".localized,
                    icon: "figure.run.circle",
                    color: .orange,
                    isLoading: isExportingActivities,
                    loadingText: "exporting".localized
                ) {
                    Task {
                        await exportActivitiesData()
                    }
                }
            }
        }
        .padding(20)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Import Data Section
    
    private var importDataSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "square.and.arrow.down")
                    .foregroundStyle(.green)
                    .font(.title2)
                Text("import_data".localized)
                    .font(.headline)
                    .foregroundStyle(.primary)
                Spacer()
            }
            
            Text("import_data_description".localized)
                .font(.subheadline)
                .foregroundStyle(.secondary)
            
            DataManagementButton(
                title: "import_data_files".localized,
                subtitle: "import_data_files_description".localized,
                icon: "folder.badge.plus",
                color: .green,
                isLoading: isImporting,
                loadingText: "importing".localized
            ) {
                showingImportPicker = true
            }
        }
        .padding(20)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Delete Data Section
    
    private var deleteDataSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "trash")
                    .foregroundStyle(.red)
                    .font(.title2)
                Text("delete_data".localized)
                    .font(.headline)
                    .foregroundStyle(.primary)
                Spacer()
            }
            
            Text("delete_data_description".localized)
                .font(.subheadline)
                .foregroundStyle(.secondary)
            
            DataManagementButton(
                title: "delete_all_data".localized,
                subtitle: "delete_all_data_description".localized,
                icon: "trash.fill",
                color: .red,
                isLoading: isDeletingData,
                loadingText: "deleting".localized
            ) {
                showingDeleteConfirmation = true
            }
        }
        .padding(20)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Export Functions
    
    private func exportAllData() async {
        isExportingProfile = true
        isExportingActivities = true
        
        do {
            let profileURL = try await exportProfile()
            let activitiesURL = try await exportActivities()
            
            await MainActor.run {
                self.exportURLs = [profileURL, activitiesURL]
                self.showingShareSheet = true
                self.isExportingProfile = false
                self.isExportingActivities = false
            }
        } catch {
            await MainActor.run {
                self.exportError = "export_failed".localized + ": \(error.localizedDescription)"
                self.isExportingProfile = false
                self.isExportingActivities = false
            }
        }
    }
    
    private func exportProfileData() async {
        isExportingProfile = true
        
        do {
            let profileURL = try await exportProfile()
            
            await MainActor.run {
                self.exportURLs = [profileURL]
                self.showingShareSheet = true
                self.isExportingProfile = false
            }
        } catch {
            await MainActor.run {
                self.exportError = "export_failed".localized + ": \(error.localizedDescription)"
                self.isExportingProfile = false
            }
        }
    }
    
    private func exportActivitiesData() async {
        isExportingActivities = true
        
        do {
            let activitiesURL = try await exportActivities()
            
            await MainActor.run {
                self.exportURLs = [activitiesURL]
                self.showingShareSheet = true
                self.isExportingActivities = false
            }
        } catch {
            await MainActor.run {
                self.exportError = "export_failed".localized + ": \(error.localizedDescription)"
                self.isExportingActivities = false
            }
        }
    }
    
    private func exportProfile() async throws -> URL {
        let exporter = DataExporter(modelContext: modelContext)
        let csvContent = try await exporter.exportProfileToCSV()
        return try await Task.detached {
            return try await exporter.saveCSVToFile(content: csvContent, fileName: "RunApp_Profile")
        }.value
    }
    
    private func exportActivities() async throws -> URL {
        let exporter = DataExporter(modelContext: modelContext)
        let csvContent = try await exporter.exportActivitiesToCSV()
        return try await Task.detached {
            return try await exporter.saveCSVToFile(content: csvContent, fileName: "RunApp_Activities")
        }.value
    }
    
    // MARK: - Import Functions
    
    private func handleImportFiles(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            Task {
                await performImport(urls: urls)
            }
        case .failure(let error):
            importError = "import_failed".localized + ": \(error.localizedDescription)"
        }
    }
    
    private func performImport(urls: [URL]) async {
        isImporting = true
        
        do {
            // Process each file
            for url in urls {
                guard url.startAccessingSecurityScopedResource() else {
                    throw DataManagementError.importFailed("Could not access file")
                }
                defer { url.stopAccessingSecurityScopedResource() }
                
                let content = try String(contentsOf: url)
                
                // Determine file type based on filename or content
                if url.lastPathComponent.contains("Profile") {
                    try await importProfileData(content)
                } else if url.lastPathComponent.contains("Activities") {
                    try await importActivitiesData(content)
                }
            }
            
            await MainActor.run {
                self.importSuccess = true
                self.isImporting = false
            }
        } catch {
            await MainActor.run {
                self.importError = "import_failed".localized + ": \(error.localizedDescription)"
                self.isImporting = false
            }
        }
    }
    
    // MARK: - Delete Functions
    
    private func performDataDeletion() async {
        isDeletingData = true
        
        do {
            try await Task.detached { [modelContext] in
                let backgroundContext = ModelContext(modelContext.container)
                
                // Delete all activities
                let activityDescriptor = FetchDescriptor<RunActivity>()
                let activities = try backgroundContext.fetch(activityDescriptor)
                for activity in activities {
                    backgroundContext.delete(activity)
                }
                
                // Delete all profiles except create a new default one
                let profileDescriptor = FetchDescriptor<UserProfile>()
                let profiles = try backgroundContext.fetch(profileDescriptor)
                for profile in profiles {
                    backgroundContext.delete(profile)
                }
                
                // Create new default profile
                let newProfile = UserProfile()
                backgroundContext.insert(newProfile)
                
                try backgroundContext.save()
            }.value
            
            await MainActor.run {
                self.deleteSuccess = true
                self.isDeletingData = false
            }
        } catch {
            await MainActor.run {
                self.deleteError = "delete_failed".localized + ": \(error.localizedDescription)"
                self.isDeletingData = false
            }
        }
    }
}

// MARK: - Helper Functions

extension DataManagementView {
    
    private func importProfileData(_ content: String) async throws {
        let importer = DataImporter(modelContext: modelContext)
        try await importer.importProfileFromCSV(content)
    }
    
    private func importActivitiesData(_ content: String) async throws {
        let importer = DataImporter(modelContext: modelContext)
        try await importer.importActivitiesFromCSV(content)
    }
}

// Note: ShareSheet is already defined in SettingsView.swift

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, RunActivity.self, configurations: config)
    
    return DataManagementView(onDismissToContentView: nil)
        .modelContainer(container)
} 