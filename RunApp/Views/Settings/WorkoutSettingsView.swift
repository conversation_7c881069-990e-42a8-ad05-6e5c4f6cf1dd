import SwiftUI
import SwiftData

struct WorkoutSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    
    private let languageManager = LanguageManager.shared
    
    // Sheet state variables
    @State private var showingAudioAlertsSheet = false
    @State private var showingMetronomeSheet = false
    
    // Get or create user profile
    private var userProfile: UserProfile? {
        if let existingProfile = profile.first {
            return existingProfile
        } else {
            // Create default profile if none exists
            let newProfile = UserProfile()
            modelContext.insert(newProfile)
            try? modelContext.save()
            return newProfile
        }
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Workout Mode Notice
                    WorkoutModeNotice()
                    
                    // Settings Sections
                    VStack(spacing: 16) {
                        SettingsSection(title: "audio".localized, items: [
                            SettingsItem(title: "audio_prompts".localized, icon: "speaker.wave.2", color: .orange,
                                    value: formatAudioAlertsStatus(),
                                    action: { showingAudioAlertsSheet = true }),
                            SettingsItem(title: "metronome".localized, icon: "metronome", color: .red,
                                    value: "\(profile.first?.metronomeBPM ?? 180) \("bpm".localized)",
                                    action: { showingMetronomeSheet = true }),
                        ])
                    }
                }
                .padding()
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("workout.settings.title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundStyle(.secondary)
                            .font(.title2)
                    }
                }
            }
        }
        .environment(\.layoutDirection, languageManager.isRTL ? .rightToLeft : .leftToRight)
        .sheet(isPresented: $showingAudioAlertsSheet) {
            AudioAlertSettingsView()
        }
        .sheet(isPresented: $showingMetronomeSheet) {
            MetronomeSettingsView()
        }
    }
    
    // MARK: - Helper Functions
    
    private func formatAudioAlertsStatus() -> String {
        guard let userProfile = profile.first else { return "disabled".localized }
        
        if !userProfile.audioAlertsEnabled {
            return "disabled".localized
        }
        
        // Count enabled alert types
        var enabledAlerts: [String] = []
        if userProfile.distanceAlertEnabled {
            enabledAlerts.append("distance_short".localized)
        }
        if userProfile.timeAlertEnabled {
            enabledAlerts.append("time_short".localized)
        }
        if userProfile.calorieAlertEnabled {
            enabledAlerts.append("calorie_short".localized)
        }
        if userProfile.paceAlertEnabled {
            enabledAlerts.append("pace_short".localized)
        }
        
        if enabledAlerts.isEmpty {
            return "enabled".localized + " • " + "no_alerts_configured".localized
        } else if enabledAlerts.count == 1 {
            return enabledAlerts[0]
        } else if enabledAlerts.count == 2 {
            return enabledAlerts.joined(separator: " • ")
        } else if enabledAlerts.count == 4 {
            // All 4 alert types are enabled
            return "all_alerts".localized
        } else {
            // 3 alert types are enabled - show all three names
            return enabledAlerts.joined(separator: " • ")
        }
    }
}

// MARK: - Supporting Views

struct WorkoutModeNotice: View {
    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                Image(systemName: "figure.run")
                    .font(.title2)
                    .foregroundStyle(.green)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("workout.settings.notice".localized)
                        .font(.headline)
                        .foregroundStyle(.primary)
                    
                    Text("workout.settings.safe.only".localized)
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                }
                
                Spacer()
            }
            .padding()
            .background(Color(.secondarySystemGroupedBackground))
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    return WorkoutSettingsView()
        .modelContainer(container)
}
