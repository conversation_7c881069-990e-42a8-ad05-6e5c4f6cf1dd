import SwiftUI

// MARK: - Main Log Viewer

struct LogViewerView: View {
    // MARK: - Properties
    @StateObject private var logManager = LogManager.shared
    @State private var logFiles: [LogFile] = []
    @State private var selectedLog: LogFile?
    @State private var showingLogContent = false
    @State private var showingShareSheet = false
    @State private var shareURL: URL?
    @State private var showingClearAlert = false
    @State private var showingInitialWarning = false
    
    // MARK: - Body
    var body: some View {
        NavigationStack {
            VStack {
                if logFiles.isEmpty {
                    emptyStateView
                } else {
                    logListView
                }
            }
            .navigationTitle("⚠️ DEBUG LOGS (DEV ONLY)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    refreshButton
                }
            }
            .onAppear {
                refreshLogFiles()
                showingInitialWarning = true
            }
            .sheet(isPresented: $showingLogContent) {
                logContentSheet
            }
            .sheet(isPresented: $showingShareSheet) {
                shareSheet
            }
            .alert("Clear All Logs", isPresented: $showingClearAlert) {
                clearLogsAlert
            } message: {
                Text("This will delete all debug log files. This action cannot be undone.")
            }
            .alert("⚠️ DEVELOPER FEATURE WARNING", isPresented: $showingInitialWarning) {
                Button("I Understand", role: .destructive) { }
                Button("Close", role: .cancel) {
                    // Dismiss the entire view
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let window = windowScene.windows.first {
                        window.rootViewController?.dismiss(animated: true)
                    }
                }
            } message: {
                Text("This feature is for developers only and may cause:\n\n• App crashes or freezing\n• High CPU usage and battery drain\n• Large storage usage\n• Performance issues\n\n⚠️ CRITICAL: Must be DISABLED before App Store submission.\n\nProceed only if debugging app issues.")
            }
        }
    }
    
    // MARK: - View Components
    
    private var emptyStateView: some View {
        ContentUnavailableView(
            "No Debug Logs",
            systemImage: "doc.text.magnifyingglass",
            description: Text("Start a run to generate debug logs for pace analysis")
        )
    }
    
    private var logListView: some View {
        List {
            developerWarningSection
            loggingStatusSection
            logFilesSection
        }
    }
    
    private var developerWarningSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundStyle(.red)
                    Text("DEVELOPER ONLY FEATURE")
                        .font(.headline)
                        .foregroundStyle(.red)
                }
                
                Text("This debug feature may cause high CPU usage, battery drain, and app crashes. MUST be disabled before production release.")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            .padding(.vertical, 8)
        }
        .listRowBackground(Color.red.opacity(0.1))
    }
    
    private var loggingStatusSection: some View {
        Section {
            HStack {
                Image(systemName: logManager.isLoggingEnabled ? "record.circle.fill" : "record.circle")
                    .foregroundStyle(logManager.isLoggingEnabled ? .red : .secondary)
                
                Text("Debug Logging")
                    .foregroundStyle(.primary)
                
                Spacer()
                
                Toggle("", isOn: $logManager.isLoggingEnabled)
                    .labelsHidden()
            }
            .padding(.vertical, 4)
        } header: {
            Text("Status")
        } footer: {
            Text("⚠️ WARNING: Enable only for debugging. Disable before App Store submission to prevent rejection.")
        }
    }
    
    private var logFilesSection: some View {
        Section {
            ForEach(logFiles) { logFile in
                LogFileRow(
                    logFile: logFile,
                    onTap: {
                        selectedLog = logFile
                        showingLogContent = true
                    },
                    onShare: {
                        shareURL = logFile.url
                        showingShareSheet = true
                    }
                )
            }
        } header: {
            HStack {
                Text("Debug Log Files")
                Spacer()
                if !logFiles.isEmpty {
                    Button("Clear All") {
                        showingClearAlert = true
                    }
                    .foregroundStyle(.red)
                    .font(.caption)
                }
            }
        } footer: {
            Text("Logs are automatically created for each app session. Tap to view details or share for analysis.")
        }
    }
    
    private var refreshButton: some View {
        Button("Refresh") {
            refreshLogFiles()
        }
    }
    
    private var logContentSheet: some View {
        Group {
            if let selectedLog = selectedLog {
                LogContentView(logFile: selectedLog)
            }
        }
    }
    
    private var shareSheet: some View {
        Group {
            if let shareURL = shareURL {
                ActivityViewController(activityItems: [shareURL])
                    .presentationDetents([.medium, .large])
            }
        }
    }
    
    private var clearLogsAlert: some View {
        Group {
            Button("Cancel", role: .cancel) { }
            Button("Clear All", role: .destructive) {
                logManager.clearAllLogs()
                refreshLogFiles()
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func refreshLogFiles() {
        logFiles = logManager.getLogFiles()
    }
}

// MARK: - Log File Row Component

struct LogFileRow: View {
    // MARK: - Properties
    let logFile: LogFile
    let onTap: () -> Void
    let onShare: () -> Void
    
    // MARK: - Body
    var body: some View {
        HStack {
            logFileInfo
            Spacer()
            shareButton
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
    
    // MARK: - View Components
    
    private var logFileInfo: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(logFile.name)
                .font(.headline)
                .lineLimit(1)
            
            HStack {
                Text(logFile.formattedDate)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                
                Spacer()
                
                Text(logFile.formattedSize)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
    }
    
    private var shareButton: some View {
        Button(action: onShare) {
            Image(systemName: "square.and.arrow.up")
                .font(.title3)
                .foregroundStyle(.blue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Log Content Detail View

struct LogContentView: View {
    // MARK: - Properties
    let logFile: LogFile
    @Environment(\.dismiss) private var dismiss
    @StateObject private var logManager = LogManager.shared
    @State private var logContent = ""
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var showingShareSheet = false
    
    // MARK: - Body
    var body: some View {
        NavigationStack {
            contentView
                .navigationTitle(logFile.name)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        closeButton
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        shareButton
                    }
                }
                .task {
                    await loadLogContent()
                }
                .sheet(isPresented: $showingShareSheet) {
                    ActivityViewController(activityItems: [logFile.url])
                        .presentationDetents([.medium, .large])
                }
        }
    }
    
    // MARK: - View Components
    
    private var contentView: some View {
        Group {
            if isLoading {
                loadingView
            } else if let errorMessage = errorMessage {
                errorView(errorMessage)
            } else {
                scrollableLogContent
            }
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Loading log content...")
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.title)
                .foregroundStyle(.orange)
            
            Text("Failed to Load Log")
                .font(.headline)
            
            Text(message)
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Retry") {
                Task {
                    await loadLogContent()
                }
            }
            .buttonStyle(.bordered)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var scrollableLogContent: some View {
        ScrollView {
            Text(logContent)
                .font(.system(.caption, design: .monospaced))
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .textSelection(.enabled)
        }
    }
    
    private var closeButton: some View {
        Button("Close") {
            dismiss()
        }
    }
    
    private var shareButton: some View {
        Button(action: {
            showingShareSheet = true
        }) {
            Image(systemName: "square.and.arrow.up")
        }
        .disabled(isLoading || errorMessage != nil)
    }
    
    // MARK: - Helper Methods
    
    private func loadLogContent() async {
        // Reset state
        await MainActor.run {
            isLoading = true
            errorMessage = nil
            logContent = ""
        }
        
        do {
            // Use the new async method with proper error handling
            let content = try await logManager.getLogContent(for: logFile)
            
            await MainActor.run {
                logContent = content
                isLoading = false
            }
        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
                isLoading = false
            }
        }
    }
}

// MARK: - Share Sheet Helper

struct ActivityViewController: UIViewControllerRepresentable {
    // MARK: - Properties
    let activityItems: [Any]
    let applicationActivities: [UIActivity]? = nil

    // MARK: - UIViewControllerRepresentable
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: applicationActivities
        )
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No updates needed
    }
}

// MARK: - Preview

#Preview {
    LogViewerView()
}
