import SwiftUI
import SwiftData

struct AudioAlertSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    
    private let languageManager = LanguageManager.shared
    
    var body: some View {
        NavigationStack {
            AudioPromptSettingsModule(
                userProfile: .constant(profile.first),
                modelContext: modelContext,
                presentationStyle: .sheet
            )
            .navigationTitle("audio_prompts".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("done".localized) {
                        dismiss()
                    }
                    .font(.body)
                    .fontWeight(.semibold)
                }
            }
        }
        .environment(\.layoutDirection, languageManager.isRTL ? .rightToLeft : .leftToRight)
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    return AudioAlertSettingsView()
        .modelContainer(container)
}
