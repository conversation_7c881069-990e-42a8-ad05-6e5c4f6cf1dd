import SwiftUI
import SwiftData

struct GenderSettingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profiles: [UserProfile]
    @State private var selectedGender: Gender

    private var userProfile: UserProfile? {
        profiles.first
    }

    init() {
        // Initialize selectedGender with a default.
        // The @Query will update it, and .onAppear ensures it's set from the profile.
        _selectedGender = State(initialValue: Gender.preferNotToSay)
    }

    private let columns: [GridItem] = [
        GridItem(.flexible())
    ]

    var body: some View {
        NavigationStack {
            VStack(alignment: .center, spacing: 20) {
                Text("select_gender_title".localized)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 20)
                    .padding(.bottom, 10)

                LazyVGrid(columns: columns, spacing: 16) {
                    ForEach(Gender.allCases, id: \.self) { gender in
                        GenderOptionButton(
                            gender: gender,
                            isSelected: selectedGender == gender,
                            action: {
                                selectedGender = gender
                                updateProfileGender()
                                // Optional: Add a slight delay before dismissing for visual feedback
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    dismiss()
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, 20)

                Spacer() 
            }
            .background(Color(.systemGroupedBackground).edgesIgnoringSafeArea(.all))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .principal) {
                    Text("gender".localized)
                        .fontWeight(.semibold)
                }
            }
            .onAppear {
                if let profile = userProfile {
                    selectedGender = profile.gender
                }
            }
        }
    }

    private func updateProfileGender() {
        if let profile = userProfile {
            profile.gender = selectedGender
            profile.lastModifiedDate = Date() // Ensure iCloud sync
            do {
                try modelContext.save()
            } catch {
                print("Failed to save gender: \(error)")
            }
        }
    }
}

// MARK: - Helper View for Gender Option Button

struct GenderOptionButton: View {
    let gender: Gender
    let isSelected: Bool
    let action: () -> Void

    private var iconName: String {
        switch gender {
        case .male: return "m.circle.fill" // Changed for clarity/visibility
        case .female: return "f.circle.fill" // Changed for clarity/visibility
        case .other: return "questionmark.circle.fill"
        case .preferNotToSay: return "eye.slash.fill"
        }
    }

    private var backgroundColor: Color {
        if isSelected {
            switch gender {
            case .male: return .blue.opacity(0.9)
            case .female: return .pink.opacity(0.9)
            case .other: return .purple.opacity(0.9)
            case .preferNotToSay: return .gray.opacity(0.8)
            }
        }
        return Color(.secondarySystemGroupedBackground)
    }

    private var foregroundColor: Color {
        isSelected ? .white : .primary
    }
    
    private var iconColor: Color {
        if isSelected {
            return .white
        }
        // Use distinct colors for icons in non-selected state for better visual cue
        switch gender {
            case .male: return .blue
            case .female: return .pink
            case .other: return .purple
            case .preferNotToSay: return .gray
        }
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 15) {
                Image(systemName: iconName)
                    .font(.title2)
                    .foregroundColor(iconColor)
                    .frame(width: 30, alignment: .center)

                Text(gender.localized)
                    .font(.headline)
                    .fontWeight(isSelected ? .bold : .medium) // Slightly bolder when selected
                
                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, 20)
            .padding(.horizontal, 15)
            .frame(maxWidth: .infinity)
            .background(backgroundColor)
            .foregroundColor(foregroundColor)
            .clipShape(RoundedRectangle(cornerRadius: 15))
            .shadow(color: Color.black.opacity(isSelected ? 0.25 : 0.1), radius: isSelected ? 8 : 4, x: 0, y: isSelected ? 4 : 2)
            .scaleEffect(isSelected ? 1.03 : 1.0) // Subtle scale effect
            .animation(.spring(response: 0.35, dampingFraction: 0.65), value: isSelected)
        }
    }
}


#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    // Ensure a UserProfile exists for the preview, otherwise @Query might be empty
    if (try? container.mainContext.fetch(FetchDescriptor<UserProfile>()).first) == nil {
        let userProfile = UserProfile(gender: .female) // Default for preview
        container.mainContext.insert(userProfile)
    }

    return GenderSettingView()
        .modelContainer(container)
    //  .environmentObject(ThemeManager.shared) // If ThemeManager is used by components and needed for preview
}