import SwiftUI
import SwiftData
import PhotosUI
import AVFoundation
import UIKit

// Constants for styling and animations
private enum ProfileViewConstants {
    static let profileImageSize: CGFloat = 160
    static let cornerRadius: CGFloat = 20
    static let editButtonSize: CGFloat = 28
    static let animationDuration: Double = 0.5
}

struct ProfileHeaderView: View {
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]

    @State private var isEditing = false
    @State private var tempName: String = ""
    @State private var tempEmail: String = ""
    @State private var degree: Double = 0
    @State private var selectedItem: PhotosPickerItem?
    @State private var isImageLoading = false
    @State private var showDeletePhotoConfirmation = false
    @State private var showImageAdjustment = false
    @State private var tempImage: UIImage?

    private var userProfile: UserProfile? {
        profile.first
    }

    // MARK: - Haptic Feedback
    private let hapticFeedback = UINotificationFeedbackGenerator()
    
    var body: some View {
        VStack {
            VStack(spacing: -16) { // Reduce spacing for larger image
                // Edit Button
                HStack {
                    Spacer()
                    Button {
                        hapticFeedback.notificationOccurred(.success)
                        if isEditing {
                            if let profile = userProfile {
                                profile.name = tempName.isEmpty ? nil : tempName
                                profile.email = tempEmail.isEmpty ? nil : tempEmail
                            } else {
                                let newProfile = UserProfile(name: tempName.isEmpty ? nil : tempName, email: tempEmail.isEmpty ? nil : tempEmail)
                                modelContext.insert(newProfile)
                            }
                            try? modelContext.save()
                        } else {
                            tempName = userProfile?.name ?? ""
                            tempEmail = userProfile?.email ?? ""
                        }
                        withAnimation(.easeInOut(duration: 0.5)) {
                            degree += 180
                            isEditing.toggle()
                        }
                    } label: {
                        Image(systemName: isEditing ? "checkmark.circle.fill" : "square.and.pencil.circle.fill")
                            .font(.system(size: ProfileViewConstants.editButtonSize, weight: .medium))
                            .symbolRenderingMode(.palette)
                            .foregroundStyle(isEditing ? .green : .blue, .background.opacity(0.2))
                            .padding(.horizontal)
                            .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .zIndex(1)

                // Profile Image Section
                Group {
                    if let image = userProfile?.image, let uiImage = UIImage(data: image) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: ProfileViewConstants.profileImageSize, height: ProfileViewConstants.profileImageSize)
                            .clipShape(Circle())
                            .overlay(Circle().stroke(Color.white.opacity(0.3), lineWidth: 2.5))
                            .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
                            .overlay {
                                if isImageLoading {
                                    ProgressView()
                                        .frame(width: 120, height: 120)
                                        .background(.ultraThinMaterial)
                                        .clipShape(Circle())
                                }
                            }
                    } else {
                            Image(systemName: "person.circle.fill")
                            .font(.system(size: ProfileViewConstants.profileImageSize))
                            .symbolRenderingMode(.hierarchical)
                            .foregroundStyle(.blue)
                            .overlay(Circle().stroke(Color.white.opacity(0.3), lineWidth: 2.5))
                            .overlay {
                                Circle()
                                    .stroke(
                                        LinearGradient(
                                            colors: [.blue.opacity(0.5), .purple.opacity(0.3)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 2.5
                                    )
                                    .opacity(0.6)
                            }
                    }
                }
                .padding(.vertical, 8)
                .overlay {
                    if isEditing {
                        PhotosPicker(selection: $selectedItem,
                                   matching: .images,
                                   photoLibrary: .shared()) {
                            Circle()
                                .fill(.black.opacity(0.3))
                                .overlay {
                                    VStack(spacing: 4) {
                                        Image(systemName: "camera.fill")
                                            .font(.system(size: 36, weight: .medium))
                                            .foregroundStyle(.white)
                                        
                                        Text("change".localized)
                                            .font(.caption.weight(.medium))
                                            .foregroundStyle(.white)
                                    }
                                }
                        }
                        .buttonStyle(.plain)
                        
                        if userProfile?.image != nil {
                            Button {
                                hapticFeedback.notificationOccurred(.warning)
                                showDeletePhotoConfirmation = true
                            } label: {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.system(size: 28))
                                    .symbolRenderingMode(.palette)
                                    .foregroundStyle(.white, .red)
                                    .background(Circle().fill(.white.opacity(0.2)))
                                    .shadow(color: .black.opacity(0.1), radius: 2)
                            }
                            .offset(x: 45, y: -45)
                        }
                    }
                }
                .confirmationDialog("delete_photo".localized,
                                  isPresented: $showDeletePhotoConfirmation) {
                    Button("delete".localized, role: .destructive) {
                        if let profile = userProfile {
                            profile.image = nil
                            try? modelContext.save()
                        }
                    }
                }

                ZStack {
                    // Display mode
                    VStack(spacing: 2) {
                        Text(userProfile?.name ?? "your_name".localized)
                            .font(.system(.title2, design: .rounded, weight: .bold))
                        Text(userProfile?.email ?? "email_example".localized)
                            .font(.system(.subheadline, design: .rounded))
                            .foregroundStyle(.secondary)
                    }
                    .opacity(isEditing ? 0 : 1)
                    .rotation3DEffect(.degrees(-degree), axis: (x: 0.0, y: 1.0, z: 0.0))

                    // Edit mode
                    VStack(spacing: 12) {
                        VStack(spacing: 12) {
                            TextField("name".localized, text: $tempName)
                                .font(.system(.title2, design: .rounded, weight: .bold))
                                .multilineTextAlignment(.center)
                                .textFieldStyle(ModernTextFieldStyle())
                                .frame(maxWidth: 250)

                            TextField("email".localized, text: $tempEmail)
                                .font(.system(.subheadline, design: .rounded))
                                .foregroundStyle(.secondary)
                                .multilineTextAlignment(.center)
                                .textFieldStyle(ModernTextFieldStyle())
                                .frame(maxWidth: 250)
                        }
                    }
                    .padding(.horizontal)
                    .opacity(isEditing ? 1 : 0)
                    .rotation3DEffect(.degrees(degree < 90 ? -degree : -(degree - 180)), axis: (x: 0.0, y: 1.0, z: 0.0))
                }
                .padding(.top, 8)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background {
                ZStack {
                    RoundedRectangle(cornerRadius: ProfileViewConstants.cornerRadius)
                        .fill(.ultraThinMaterial)
                    
                    // Subtle gradient overlay
                    RoundedRectangle(cornerRadius: ProfileViewConstants.cornerRadius)
                        .fill(
                            LinearGradient(
                                colors: [
                                    .blue.opacity(0.1),
                                    .purple.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .blendMode(.overlay)
                }
                .rotation3DEffect(.degrees(degree), axis: (x: 0.0, y: 1.0, z: 0.0))
            }
            .padding(.horizontal)
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
        .onChange(of: selectedItem) { oldValue, newValue in
            Task {
                isImageLoading = true
                if let data = try? await newValue?.loadTransferable(type: Data.self),
                   let uiImage = UIImage(data: data) {
                    tempImage = uiImage
                    showImageAdjustment = true
                }
                isImageLoading = false
                selectedItem = nil
            }
        }
        .sheet(isPresented: $showImageAdjustment, onDismiss: {
            tempImage = nil
        }) {
            if let image = tempImage {
                ImageAdjustmentView(image: image) { processedImage in
                    if let imageData = processedImage.jpegData(compressionQuality: 0.8) {
                        if let profile = userProfile {
                            profile.image = imageData
                        } else {
                            let newProfile = UserProfile(image: imageData)
                            modelContext.insert(newProfile)
                        }
                        try? modelContext.save()
                    }
                }
            }
        }
    }
}

// Modern Text Field Style
struct ModernTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(.background.opacity(0.5))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(.secondary.opacity(0.2), lineWidth: 1)
                    )
            )
    }
}

#Preview {
    ProfileHeaderView()
        .padding()
        .background(Color(.systemGroupedBackground))
        .modelContainer(for: UserProfile.self, inMemory: true)
}
