import SwiftUI
import SwiftData

struct WelcomeView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss: DismissAction
    @AppStorage("hasCompletedOnboarding") private var hasCompletedOnboarding = false
    @State private var showOnboarding = false
    
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            
            // Running Icon
            Image("RunApp-nobg")
                .resizable()
                .scaledToFit()
                .frame(height: 300)
                .foregroundStyle(.black)
            
            // App Name
            Text(NSLocalizedString("welcome.title", comment: "Welcome screen title"))
                .font(.system(size: 28, weight: .bold))
                //.fontWeight(.bold)
                .foregroundStyle(.black)
            
            // Welcome Message
            Text(NSLocalizedString("welcome.subtitle", comment: "Welcome screen subtitle"))
                .font(.system(size: 18, weight: .bold))
                .multilineTextAlignment(.center)
                .foregroundStyle(.black)
            
            
            Spacer()
            
            // Get Started Button
            Button(action: {
                Task { // Perform fetch asynchronously
                    let descriptor = FetchDescriptor<UserProfile>(sortBy: [SortDescriptor(\UserProfile.lastModifiedDate, order: .reverse)])
                    do {
                        let profiles = try modelContext.fetch(descriptor)
                        if let primaryProfile = profiles.first {
                            // Profile found! Skip onboarding.
                            print("UserProfile found in WelcomeView. Skipping onboarding.")
                            
                            // If there were duplicates, delete the older ones
                            if profiles.count > 1 {
                                print("Multiple UserProfiles found in WelcomeView. Keeping the one last modified at \(primaryProfile.lastModifiedDate) and deleting \(profiles.count - 1) older profile(s).")
                                for i in 1..<profiles.count {
                                    modelContext.delete(profiles[i])
                                }
                                try modelContext.save() // Persist deletions
                            }
                            
                            // This will trigger .onChange in RunAppApp and switch the view
                            hasCompletedOnboarding = true 
                        } else {
                            // No profile found, proceed to the next onboarding step
                            print("No UserProfile found in WelcomeView. Proceeding with onboarding.")
                            showOnboarding = true
                        }
                    } catch {
                        print("Error fetching UserProfile in WelcomeView: \(error). Proceeding with onboarding.")
                        showOnboarding = true // Proceed on error
                    }
                }
            }) {
                Text(NSLocalizedString("welcome.button.start", comment: "Get started button"))
                    .font(.headline)
                    .foregroundStyle(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(.black.opacity(0.7))
                    )
                    .padding(.horizontal, 40)
            }
            .padding(.bottom, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(red: 1, green: 0.8, blue: 0.0))
        .fullScreenCover(isPresented: $showOnboarding) {
            NavigationStack {
                VideoView()
            }
        }
        .preferredColorScheme(.light) // Force light mode for this view
    }
}

#Preview {
    WelcomeView()
}
