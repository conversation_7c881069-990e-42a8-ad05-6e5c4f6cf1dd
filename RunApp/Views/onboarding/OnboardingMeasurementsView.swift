import SwiftUI
import SwiftData

struct OnboardingMeasurementsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @Binding var navigationPath: [VideoView.OnboardingRoute] // Use the Route from VideoView
    @State private var showingWeightAlert = false
    @State private var showingHeightAlert = false
    
    // Weight input
    @State private var weightString: String = ""
    @State private var selectedWeightUnit: String = "kg"
    
    // Height input
    @State private var heightString: String = ""
    @State private var feetString: String = ""
    @State private var inchesString: String = ""
    @State private var selectedHeightUnit: String = "cm"

    // Unit System selection
    @State private var selectedUnitSystem: UnitSystem = .metric
    
    private let weightUnits = ["kg", "lbs"]
    private let heightUnits = ["cm", "ft"]
    
    var body: some View {
            ZStack {
                Color(red: 1, green: 0.8, blue: 0.0).ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 35) {
                        headerSection
                        unitSystemSection
                        weightSection
                        heightSection
                        continueButton
                            .padding(.top, 10)
                            .padding(.bottom, 20)
                    }
                    .padding(.horizontal)
                    .padding(.top, 1)
                }
            }
            // No navigationDestination here, it's handled by the root NavigationStack in VideoView
            .scrollDismissesKeyboard(.interactively)
            .contentShape(Rectangle())
            .onTapGesture {
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder),
                                           to: nil, from: nil, for: nil)
            }
            .onAppear {
                loadProfile()
            }
            .preferredColorScheme(.light)
            .alert("invalid_weight".localized, isPresented: $showingWeightAlert) {
                Button("ok".localized, role: .cancel) { }
            } message: {
                Text(String(format: "please_enter_valid_weight".localized, 
                           selectedWeightUnit == "lbs" ? 66 : 30,
                           selectedWeightUnit == "lbs" ? 661 : 300,
                           selectedWeightUnit))
            }
            .alert("invalid_height".localized, isPresented: $showingHeightAlert) {
                Button("ok".localized, role: .cancel) { }
            } message: {
                Text("please_enter_valid_height".localized)
            }
    }
    
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text(NSLocalizedString("basicInfo.header.title", comment: "Personalization screen title"))
                .font(.system(size: 28, weight: .bold))
                .padding(.top, 15)
            
            Text(NSLocalizedString("basicInfo.header.subtitle", comment: "Personalization subtitle"))
                .font(.system(size: 18, weight: .bold))
                .foregroundStyle(.black.opacity(0.7))
        }
        .padding(.bottom, 8)
        .padding(.top, 15)
    }
    
    private var unitSystemSection: some View {
        VStack(spacing: 16) {
            Text(NSLocalizedString("unit_system", comment: "Unit system selection prompt"))
                .font(.system(size: 18, weight: .bold))
                .foregroundStyle(.black)

            HStack(spacing: 16) {
                ForEach(UnitSystem.allCases, id: \.self) { system in
                    Button(action: {
                        withAnimation(.spring(response: 0.3)) {
                            selectedUnitSystem = system
                            selectedWeightUnit = system.weightUnit
                            selectedHeightUnit = (system == .imperial) ? "ft" : system.heightUnit
                            convertAndDisplayHeight()
                        }
                    }) {
                        Text(NSLocalizedString(system.rawValue, comment: "Unit system option"))
                            .font(.headline)
                            .frame(maxWidth: 150, maxHeight: 35)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 8)
                            .background(selectedUnitSystem == system ? Color.black : Color.clear)
                            .foregroundStyle(selectedUnitSystem == system ? Color.white : Color.black)
                            .clipShape(Capsule())
                            .overlay(Capsule().stroke(Color.black, lineWidth: 1))
                            .animation(.easeInOut, value: selectedUnitSystem)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(Color.white.opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
    
    private var weightSection: some View {
        VStack(spacing: 16) {
            Text(NSLocalizedString("basicInfo.weight.title", comment: "Weight input prompt"))
                .font(.system(size: 18, weight: .bold))
                .foregroundStyle(.black)
            
            HStack(spacing: 16) {
                ForEach(weightUnits, id: \.self) { unit in
                    Button(action: {
                        withAnimation { selectedWeightUnit = unit }
                    }) {
                        Text(NSLocalizedString("basicInfo.weight.unit.\(unit)", comment: "Weight unit"))
                            .font(.headline)
                            .lineLimit(1)
                            .frame(width: 45)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 8)
                            .background(selectedWeightUnit == unit ? Color.black : Color.clear)
                            .foregroundStyle(selectedWeightUnit == unit ? Color.white : Color.black)
                            .clipShape(Capsule())
                            .overlay(Capsule().stroke(Color.black, lineWidth: 1))
                    }
                }
                Spacer()
                TextField(selectedWeightUnit, text: $weightString)
                    .keyboardType(.decimalPad)
                    .multilineTextAlignment(.center)
                    .font(.system(size: 24, weight: .bold))
                    .frame(width: 70)
                    .padding(.vertical, 8)
                    .padding(.horizontal, 12)
                    .background(Color.white.opacity(0.3))
                    .cornerRadius(12)
                Spacer()
            }
        }
        .frame(maxWidth: .infinity)
        .padding(16)
        .background(Color.white.opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
    
    private var heightSection: some View {
        VStack(spacing: 16) {
            Text(NSLocalizedString("basicInfo.height.title", comment: "Height input prompt"))
                .font(.system(size: 18, weight: .bold))
                .foregroundStyle(.black)
            
            HStack(spacing: 16) {
                ForEach(heightUnits, id: \.self) { unit in
                    Button(action: {
                        withAnimation {
                            selectedHeightUnit = unit
                            convertAndDisplayHeight()
                        }
                    }) {
                        Text(NSLocalizedString("basicInfo.height.unit.\(unit == "ft" ? "ftIn" : unit)", comment: "Height unit"))
                            .font(.headline)
                            .lineLimit(1)
                            .frame(width: 45)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(selectedHeightUnit == unit ? Color.black : Color.clear)
                            .foregroundStyle(selectedHeightUnit == unit ? Color.white : Color.black)
                            .clipShape(Capsule())
                            .overlay(Capsule().stroke(Color.black, lineWidth: 1))
                    }
                }
                Spacer()
                
                if selectedHeightUnit == "cm" {
                    TextField(NSLocalizedString("basicInfo.height.placeholder.cm", comment: "Centimeter input placeholder"), text: $heightString)
                        .keyboardType(.decimalPad)
                        .multilineTextAlignment(.center)
                        .font(.system(size: 24, weight: .bold))
                        .frame(width: 70)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(Color.white.opacity(0.3))
                        .cornerRadius(12)
                } else {
                    HStack(spacing: 8) {
                        TextField(NSLocalizedString("basicInfo.height.placeholder.ft", comment: "Feet input placeholder"), text: $feetString)
                            .keyboardType(.numberPad)
                            .multilineTextAlignment(.center)
                            .font(.system(size: 24, weight: .bold))
                            .frame(width: 30)
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                            .background(Color.white.opacity(0.3))
                            .cornerRadius(12)
                        
                        TextField(NSLocalizedString("basicInfo.height.placeholder.in", comment: "Inches input placeholder"), text: $inchesString)
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.center)
                            .font(.system(size: 24, weight: .bold))
                            .frame(width: 30)
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                            .background(Color.white.opacity(0.3))
                            .cornerRadius(12)
                    }
                    .frame(width: 70)
                }
                Spacer()
            }
        }
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(16)
        .background(Color.white.opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
    
    private var continueButton: some View {
        Button(action: {
            withAnimation {
                saveProfile()
            }
        }) {
            Text(NSLocalizedString("basicInfo.button.continue", comment: "Continue button"))
                .font(.headline)
                .foregroundStyle(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill((weightString.isEmpty || (selectedHeightUnit == "cm" ? heightString.isEmpty : (feetString.isEmpty || inchesString.isEmpty))) ? Color.black.opacity(0.1) : Color.black.opacity(0.7))
                )
                .padding(.horizontal, 40)
                .opacity((weightString.isEmpty || (selectedHeightUnit == "cm" ? heightString.isEmpty : (feetString.isEmpty || inchesString.isEmpty))) ? 0.7 : 1.0)
        }
        .disabled(weightString.isEmpty || (selectedHeightUnit == "cm" ? heightString.isEmpty : (feetString.isEmpty || inchesString.isEmpty)))
    }
    
    private func cmToFeetInches(_ cm: Double) -> (feet: Int, inches: Double) {
        let totalInches = cm / 2.54
        let feet = Int(totalInches / 12)
        let inches = totalInches.truncatingRemainder(dividingBy: 12)
        return (feet, inches)
    }
    
    private func feetInchesToCm(feet: Int, inches: Double) -> Double {
        let totalInches = Double(feet) * 12 + inches
        return totalInches * 2.54
    }
    
    private func convertAndDisplayHeight() {
        if selectedHeightUnit == "cm" {
            if let feet = Int(feetString), let inches = Double(inchesString) {
                let heightInCm = feetInchesToCm(feet: feet, inches: inches)
                heightString = String(format: "%.1f", heightInCm)
            }
            feetString = ""
            inchesString = ""
        } else {
            if let heightInCm = Double(heightString) {
                let (feet, inches) = cmToFeetInches(heightInCm)
                feetString = String(feet)
                inchesString = String(format: "%.1f", inches)
            }
            heightString = ""
        }
    }
    
    private func loadProfile() {
        if let userProfile = profile.first {
            selectedUnitSystem = userProfile.units

            if let weight = userProfile.weight {
                selectedWeightUnit = userProfile.weightUnit ?? selectedUnitSystem.weightUnit
                let displayWeight = selectedWeightUnit == "lbs" ? weight * 2.20462 : weight
                weightString = String(format: "%.1f", displayWeight)
            } else {
                selectedWeightUnit = selectedUnitSystem.weightUnit
            }

            if let height = userProfile.height {
                selectedHeightUnit = userProfile.heightUnit ?? selectedUnitSystem.heightUnit
                if selectedHeightUnit == "cm" {
                    heightString = String(format: "%.1f", height)
                } else if selectedHeightUnit == "ft/in" {
                    let (feet, inches) = cmToFeetInches(height)
                    feetString = String(feet)
                    inchesString = String(format: "%.1f", inches)
                }
            } else {
                selectedHeightUnit = selectedUnitSystem.heightUnit
            }
        }
    }
    
    private func saveProfile() {
        if let userProfile = profile.first {            
            // Weight validation
            if let weight = Double(weightString) {
                let validRange: ClosedRange<Double> = selectedWeightUnit == "lbs" ? 66.0...661.0 : 30.0...300.0
                if validRange.contains(weight) {
                    let weightInKg = selectedWeightUnit == "lbs" ? weight / 2.20462 : weight
                    userProfile.weight = weightInKg
                    userProfile.weightUnit = selectedWeightUnit.lowercased()
                } else {
                    showingWeightAlert = true
                    return
                }
            }
            
            // Height validation
            var heightInCm: Double? = nil
            if selectedHeightUnit == "cm", let height = Double(heightString) {
                heightInCm = height
            } else if let feet = Int(feetString), let inches = Double(inchesString) {
                heightInCm = feetInchesToCm(feet: feet, inches: inches)
            }
            
            if let heightInCm = heightInCm {
                if heightInCm >= 30.48 && heightInCm <= 304.8 {
                    userProfile.height = heightInCm
                    userProfile.heightUnit = (selectedHeightUnit == "ft") ? "ft/in" : selectedHeightUnit
                } else {
                    showingHeightAlert = true
                    return
                }
            }
            
            userProfile.units = selectedUnitSystem
            
            do {
                try modelContext.save()
                print("Profile saved successfully")
                navigationPath.append(.allSet) // This will now use VideoView.OnboardingRoute.allSet
            } catch {
                print("Failed to save profile: \(error)")
                // TODO: Show error alert to user if needed
                // For now, continue with navigation even if save failed
                navigationPath.append(.allSet)
            }
        }
    }
}

// Update Preview to provide a dummy binding
#Preview {
    struct PreviewWrapper: View {
        @State var path: [VideoView.OnboardingRoute] = []
        var body: some View {
            OnboardingMeasurementsView(navigationPath: $path)
                .modelContainer(for: UserProfile.self)
        }
    }
    return PreviewWrapper()
}
