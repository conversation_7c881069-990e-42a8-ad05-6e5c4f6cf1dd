import SwiftUI

struct AllSetView: View {
    @AppStorage("hasCompletedOnboarding") private var hasCompletedOnboarding = false
    
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            
            // Checkmark icon
            Circle()
                .fill(.white)
                .frame(width: 80, height: 80)
                .overlay {
                    Image(systemName: "checkmark")
                        .font(.system(size: 40, weight: .bold))
                        .foregroundStyle(.black)
                }
                .padding(.bottom, 20)
            
            // Title
            Text(NSLocalizedString("allSet.title", comment: "All set screen title"))
                .font(.system(size: 36, weight: .bold))
            
            // Subtitle
            
            Text(NSLocalizedString("allSet.subtitle.first", comment: "First motivational message"))
                .font(.system(size: 18, weight: .bold))
            Text(NSLocalizedString("allSet.subtitle.second", comment: "Second motivational message"))
                .multilineTextAlignment(.center)
                .font(.system(size: 16, weight: .bold))
                .foregroundStyle(.secondary)
            
            Spacer()
            
            // Start button
            Button(action: {
                hasCompletedOnboarding = true
            }) {
                Text(NSLocalizedString("allSet.button.go", comment: "Final button to start using the app"))
                    .font(.headline)
                    .foregroundStyle(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(.black.opacity(0.7))
                    )
                    .padding(.horizontal, 40)
            }
            .padding(.bottom, 100)
        }
        .background(Color(red: 1, green: 0.8, blue: 0.0))
        .preferredColorScheme(.light) // Force light mode for this view
    }
}

#Preview {
    AllSetView()
}
