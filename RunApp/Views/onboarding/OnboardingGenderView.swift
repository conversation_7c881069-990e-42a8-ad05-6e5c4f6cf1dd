import SwiftUI
import SwiftData
import RevenueCatUI

struct OnboardingGenderView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    @State private var selectedGender: Gender?
    @Binding var navigationPath: [VideoView.OnboardingRoute] // Use the Route from VideoView
    
    var body: some View {
        ZStack {
            Color(red: 1, green: 0.8, blue: 0.0).ignoresSafeArea()

            ScrollView {
                VStack(spacing: 100) {
                    headerSection
                    genderSection
                    continueButton
                        .padding(.top, 10)
                        .padding(.bottom, 20)
                }
                .padding(.horizontal)
                .padding(.top, 15)
            }
        }
        // No navigationDestination here, it's handled by the root NavigationStack in VideoView
        .onAppear {
            loadProfile()
        }
        .preferredColorScheme(.light)
        .presentPaywallIfNeeded(
            requiredEntitlementIdentifier: "Pro",
            presentationMode: .fullScreen,
            purchaseCompleted: { customerInfo in
                print("Purchase completed: \(customerInfo.entitlements)")
            },
            restoreCompleted: { customerInfo in
                // Paywall will be dismissed automatically if "Pro" is now active.
                print("Purchases restored: \(customerInfo.entitlements)")
            }
        )
    }
    
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text(NSLocalizedString("basicInfo.header.title", comment: "Personalization screen title"))
                .font(.system(size: 28, weight: .bold))
                .padding(.top, 15)
            
            Text(NSLocalizedString("basicInfo.header.subtitle", comment: "Personalization subtitle"))
                .font(.system(size: 18, weight: .bold))
                .foregroundStyle(.black.opacity(0.7))
        }
        .padding(.bottom, 8)
        .padding(.top, 15)
    }
    
    private var genderSection: some View {
        VStack(spacing: 16) {
            Text(NSLocalizedString("basicInfo.gender.title", comment: "Gender selection prompt"))
                .font(.headline)
                .foregroundStyle(.black)
            
            HStack(spacing: 40) {
                ForEach([Gender.male, Gender.female], id: \.self) { gender in
                    Button(action: { 
                        withAnimation(.spring(response: 0.3)) { 
                            selectedGender = gender
                        }
                    }) {
                        VStack(spacing: 12) {
                            Image(systemName: gender == .male ? "figure.arms.open" : "figure.stand.dress")
                                .font(.system(size: 30))
                                .foregroundStyle(selectedGender == gender ? .white : .black)
                            
                            Text(NSLocalizedString(gender.localizationKey, comment: "Gender option"))
                                .font(.subheadline.bold())
                                .foregroundStyle(selectedGender == gender ? .white : .black)
                        }
                        .frame(width: 110, height: 100)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(selectedGender == gender ? Color.black : Color.white.opacity(0.3))
                        )
                        .scaleEffect(selectedGender == gender ? 1.05 : 1.0)
                        .animation(.spring(response: 0.3), value: selectedGender)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding(30)
        .background(Color.white.opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
    
    private var continueButton: some View {
        Button(action: {
            withAnimation {
                saveGender()
                navigationPath.append(.measurements) // This will now use VideoView.OnboardingRoute.measurements
            }
        }) {
            Text(NSLocalizedString(selectedGender == nil ? "basicInfo.button.skip" : "basicInfo.button.continue", 
                                 comment: selectedGender == nil ? "Skip button" : "Continue button"))
                .font(.headline)
                .foregroundStyle(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color.black.opacity(0.7))
                )
                .padding(.horizontal, 40)
        }
    }
    
    private func loadProfile() {
        if let userProfile = profile.first, userProfile.gender != .preferNotToSay {
            selectedGender = userProfile.gender
        } else {
            do {
                let newProfile = UserProfile(units: .metric)
                modelContext.insert(newProfile)
                try modelContext.save()
                print("Created new user profile successfully")
            } catch {
                print("Failed to create/save user profile: \(error)")
                // Even if save fails, we can still continue with the UI
                // The profile will be saved later when user makes selections
            }
        }
    }
    
    private func saveGender() {
        if let userProfile = profile.first {
            userProfile.gender = selectedGender ?? .preferNotToSay
            try? modelContext.save()
        }
    }
}

// Update Preview to provide a dummy binding
#Preview {
    // Since OnboardingGenderView now expects a binding for navigationPath,
    // we provide a constant empty array for the preview.
    // In a real scenario, this would be part of a NavigationStack.
    struct PreviewWrapper: View {
        @State var path: [VideoView.OnboardingRoute] = []
        var body: some View {
            OnboardingGenderView(navigationPath: $path)
                .modelContainer(for: UserProfile.self)
        }
    }
    return PreviewWrapper()
}
