import SwiftUI
import AVKit

struct VideoView: View {
    private let player: AVPlayer
    @State private var showContinueButton = false
    
    // Define routes for the entire onboarding flow
    enum OnboardingRoute: Hashable {
        case gender
        case measurements
        case allSet
    }
    @State private var navigationPath: [OnboardingRoute] = []
    
    init() {
        guard let videoURL = Bundle.main.url(forResource: "RunApp_v7_480P", withExtension: "mp4") else {
            fatalError("Failed to find video file")
        }
        self.player = AVPlayer(url: videoURL)
    }
    
    var body: some View {
        NavigationStack(path: $navigationPath) {
            ZStack {
                // Background color matching other onboarding views
                Color.black.ignoresSafeArea()
                
                VStack {
                    VideoPlayer(player: player)
                        .frame(width: UIScreen.main.bounds.width * 0.9)
                        .frame(maxHeight: .infinity)
                        .clipped()
                        .offset(y: -50)
                    
                    Spacer()
                    
                    if showContinueButton {
                        Button(action: {
                            withAnimation {
                                navigationPath.append(.gender)
                            }
                        }) {
                            Text(NSLocalizedString("basicInfo.button.continue", comment: "Continue button"))
                                .font(.system(size: 18, weight: .bold))
                                .foregroundStyle(.black)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 10)
                                        .fill(.yellow.opacity(1.0))
                                )
                                .padding(.horizontal, 40)
                        }
                        .padding(.bottom, 40)
                        .transition(.opacity)
                    }
                }
            }
            .navigationDestination(for: OnboardingRoute.self) { route in
                switch route {
                case .gender:
                    OnboardingGenderView(navigationPath: $navigationPath)
                case .measurements:
                    OnboardingMeasurementsView(navigationPath: $navigationPath)
                case .allSet:
                    AllSetView()
                }
            }
        }
        .onAppear {
            player.seek(to: .zero)
            player.play()
            
            // Add observer for video completion
            NotificationCenter.default.addObserver(
                forName: .AVPlayerItemDidPlayToEndTime,
                object: player.currentItem,
                queue: .main
            ) { _ in
                withAnimation {
                    showContinueButton = true
                }
            }
        }
        .onDisappear {
            player.pause()
        }
        .preferredColorScheme(.light)
    }
}

#Preview {
    VideoView()
}
