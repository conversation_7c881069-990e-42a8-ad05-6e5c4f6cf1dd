import Foundation
import SwiftData
import SwiftUI

@MainActor
class AudioAlertSettingsViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var audioAlertsEnabled: Bool = false
    @Published var distanceAlertEnabled: Bool = false
    @Published var distanceAlertInterval: Double = 1.0
    @Published var timeAlertEnabled: Bool = false
    @Published var timeAlertInterval: Double = 300.0 // 5 minutes in seconds
    @Published var calorieAlertEnabled: Bool = false
    @Published var calorieAlertInterval: Double = 100.0
    @Published var paceAlertEnabled: Bool = false
    @Published var paceAlertInterval: Double = 300.0 // 5 minutes in seconds
    @Published var audioAlertVolume: Float = AppConstants.defaultAudioPromptVolume
    @Published var speechRate: Float = AppConstants.defaultAudioPromptRate // Will be loaded from user profile
    @Published var smartVoiceEnabled: Bool = true
    
    // MARK: - Private Properties
    private var modelContext: ModelContext
    private var userProfile: UserProfile?
    
    // MARK: - Computed Properties
    var unitSystem: UnitSystem {
        userProfile?.units ?? .metric
    }
    
    var distanceUnit: String {
        unitSystem.distanceUnit
    }
    
    // Preset interval options
    var distancePresets: [Double] {
        unitSystem == .metric 
            ? [0.5, 1.0, 2.0, 5.0, 10.0]
            : [0.5, 1.0, 2.0, 5.0, 10.0]
    }
    
    var timePresets: [Double] {
        [60.0, 300.0, 600.0, 1800.0, 3600.0] // 1, 5, 10, 15, 30, 60 minutes
    }
    
    var caloriePresets: [Double] { 
    [50.0, 100.0, 200.0, 300.0, 500.0] 
    }
    
    var pacePresets: [Double] {
        [60.0, 300.0, 600.0, 900.0, 1800.0] // 1, 5, 10, 15, 30 minutes
    }
    
    // MARK: - Computed Properties for UI
    var timeAlertIntervalInMinutes: Double {
        get { timeAlertInterval / 60.0 }
        set { 
            // Safely convert minutes to seconds with bounds checking
            let seconds = newValue * 60.0
            if seconds <= Double(Int.max) && seconds >= 0 {
                timeAlertInterval = seconds
            }
        }
    }
    
    var timePresetsInMinutes: [Double] {
        timePresets.map { $0 / 60.0 }
    }
    
    var paceAlertIntervalInMinutes: Double {
        get { paceAlertInterval / 60.0 }
        set { 
            // Safely convert minutes to seconds with bounds checking
            let seconds = newValue * 60.0
            if seconds <= Double(Int.max) && seconds >= 0 {
                paceAlertInterval = seconds
            }
        }
    }
    
    var pacePresetsInMinutes: [Double] {
        pacePresets.map { $0 / 60.0 }
    }
    
    
    var distanceValidationRange: ClosedRange<Double> {
        unitSystem == .metric 
            ? 0.1...50.0  // 0.1 km to 50 km
            : 0.1...50.0 // ~0.1 km to ~50 km in miles
    }
    
    // MARK: - Initializer
    init(modelContext: ModelContext, userProfile: UserProfile?) {
        self.modelContext = modelContext
        self.userProfile = userProfile
        loadSettings()
    }
    
    // MARK: - Methods
    func updateProfile(_ profile: UserProfile?) {
        self.userProfile = profile
        loadSettings()
    }
    
    func updateModelContext(_ context: ModelContext) {
        self.modelContext = context
    }
    
    private func loadSettings() {
        guard let profile = userProfile else { return }
        
        audioAlertsEnabled = profile.audioAlertsEnabled
        distanceAlertEnabled = profile.distanceAlertEnabled
        distanceAlertInterval = profile.distanceAlertInterval
        timeAlertEnabled = profile.timeAlertEnabled
        timeAlertInterval = profile.timeAlertInterval
        calorieAlertEnabled = profile.calorieAlertEnabled
        calorieAlertInterval = profile.calorieAlertInterval
        paceAlertEnabled = profile.paceAlertEnabled
        paceAlertInterval = profile.paceAlertInterval
        audioAlertVolume = profile.audioAlertVolume
        speechRate = profile.speechRate
        smartVoiceEnabled = profile.smartVoiceEnabled
        
        // Set the volume in the AudioAlertManager
        AudioAlertManager.shared.setVolume(audioAlertVolume)
    }
    
    func saveSettings() {
        guard let profile = userProfile else { return }
        
        profile.audioAlertsEnabled = audioAlertsEnabled
        profile.distanceAlertEnabled = distanceAlertEnabled
        profile.distanceAlertInterval = distanceAlertInterval
        profile.timeAlertEnabled = timeAlertEnabled
        profile.timeAlertInterval = timeAlertInterval
        profile.calorieAlertEnabled = calorieAlertEnabled
        profile.calorieAlertInterval = calorieAlertInterval
        profile.paceAlertEnabled = paceAlertEnabled
        profile.paceAlertInterval = paceAlertInterval
        profile.audioAlertVolume = audioAlertVolume
        profile.speechRate = speechRate
        profile.smartVoiceEnabled = smartVoiceEnabled
        
        do {
            try modelContext.save()
        } catch {
            print("Failed to save audio alert settings: \(error)")
        }
    }
    
    // MARK: - Formatting Methods
    func formatDistanceInterval(_ interval: Double) -> String {
        // Always format distance to 1 decimal place for display on buttons
        return String(format: "%.1f", interval)
    }
    
    func formatTimeInterval(_ interval: Double) -> String {
        // Safely convert to minutes with bounds checking
        let minutes = interval / 60.0
        guard minutes <= Double(Int.max) && minutes >= 0 else {
            return "invalid".localized
        }
        
        let minutesInt = Int(minutes)
        if minutesInt < 60 {
            return "\(minutesInt) \("min".localized)"
        } else {
            let hours = minutesInt / 60
            let remainingMinutes = minutesInt % 60
            if remainingMinutes == 0 {
                return "\(hours) \("hour".localized)\(hours == 1 ? "" : "s")"
            } else {
                return "\(hours)\("h".localized) \(remainingMinutes)\("m".localized)"
            }
        }
    }
    
    func formatCalorieInterval(_ interval: Double) -> String {
        // For display purposes, show as integer but preserve the Double precision
        if caloriePresets.contains(interval) {
            return String(Int(interval))
        } else {
            // For custom values, still show as integer but preserve the underlying Double value
            return String(Int(interval.rounded()))
        }
    }
    
    func formatPaceInterval(_ interval: Double) -> String {
        let minutes = interval / 60.0
        return "\(Int(minutes))"
    }
}
