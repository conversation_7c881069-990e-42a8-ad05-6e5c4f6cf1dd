import SwiftUI
import AudioToolbox

struct CountdownView: View {
    @Binding var isPresented: Bool
    let onFinished: () -> Void
    @State private var countdown = 3
    
    var body: some View {
        ZStack {
            Color.yellow
                .ignoresSafeArea()
            
            Text(countdown == 0 ? "go".localized : "\(countdown)")
                .font(.system(size: 80, weight: .bold))
                .foregroundColor(.black)
                .contentTransition(.numericText())
        }
        .onAppear {
            withAnimation {
                startCountdown()
            }
        }
    }
    
    private func startCountdown() {
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            withAnimation(.spring()) {
                if countdown > 0 {
                    if countdown > 1 {
                        AudioServicesPlaySystemSound(1103) // Play countdown beep
                    } else {
                        AudioServicesPlaySystemSound(1259) // Play GO! sound
                    }
                    countdown -= 1
                } else {
                    timer.invalidate()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        isPresented = false
                        onFinished()
                    }
                }
            }
        }
    }
}

#Preview {
    CountdownView(isPresented: .constant(true)) {}
}