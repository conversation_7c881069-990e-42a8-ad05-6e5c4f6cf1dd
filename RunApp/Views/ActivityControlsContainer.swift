import SwiftUI
import AudioToolbox

struct ActivityControlsContainer: View {
    @Binding var isRunning: <PERSON><PERSON>
    @Binding var isPaused: <PERSON><PERSON>
    @Binding var selectedSportType: SportType?
    @Binding var showingCountdown: <PERSON><PERSON>
    @Binding var showingAnalysisView: <PERSON><PERSON>
    @Binding var showingCompleteAlert: Bool
    let onPause: () -> Void
    let onResume: () -> Void
    let onShowSettings: () -> Void
    
    var body: some View {
        VStack {
            // Sport Type Selector
            if !isRunning && !isPaused {
                SportTypeSelector(selectedSportType: $selectedSportType)
            }
            
            // Activity Controls
            ActivityControls(
                isRunning: isRunning,
                isPaused: isPaused,
                selectedSportType: selectedSportType,
                onStart: {
                    showingCountdown = true
                    AudioServicesPlaySystemSound(1103)
                },
                onPause: onPause,
                onResume: onResume,
                onEnd: { showingCompleteAlert = true },
                onShowAnalysis: { showingAnalysisView = true },
                onShowSettings: onShowSettings
            )
        }
        .padding(.bottom, 30)
    }
}
