import SwiftUI
import SwiftData

struct MetronomeSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    
    var body: some View {
        NavigationStack {
            MetronomeSettingsModule(
                userProfile: .constant(profile.first),
                modelContext: modelContext,
                presentationStyle: .sheet
            )
            .navigationTitle("metronome_settings".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("done".localized) {
                        dismiss()
                    }
                    .font(.body)
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

#Preview {
    MetronomeSettingsView()
        .modelContainer(for: UserProfile.self, inMemory: true)
}
