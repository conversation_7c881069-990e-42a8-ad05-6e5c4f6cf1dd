import SwiftUI
import SwiftData
import UniformTypeIdentifiers
import CoreLocation
import RevenueCat
import RevenueCatUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.modelContext) private var modelContext
    @Query private var profile: [UserProfile]
    // 🚀 PERFORMANCE FIX: Remove expensive activities query
    // @Query private var activities: [RunActivity] // ❌ REMOVED
    
    // 🚀 PERFORMANCE FIX: Cache activity count and load on demand
    @State private var activityCount: Int = 0
    @State private var isLoadingActivityCount = false
    
    @Bindable private var themeManager = ThemeManager.shared
    private let languageManager = LanguageManager.shared
    @State private var showingGenderSheet = false
    @State private var showingAgeSheet = false
    @State private var showingWeightSheet = false
    @State private var showingHeightSheet = false
    @State private var showingUnitsSheet = false
    @State private var showingLogoutAlert = false
    @State private var showingThemeSheet = false
    @State private var showingMetronomeSheet = false
    @State private var showingLanguageSheet = false
    @State private var showingAudioAlertsSheet = false

    @State private var showingActivityView = false
    @State private var showingDebugLogsView = false
    @State private var showingDataManagementView = false
    // Removed local constant, now using AppConstants.freeTrialLimit
    // RevenueCat State
    @State private var isCheckingSubscription = false
    @State private var isShowingPaywall = false
    @State private var isShowingAlreadySubscribedAlert = false
    @State private var hasProEntitlement = false // Track if user has Pro subscription

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Profile Header
                    ProfileHeaderView()
                        .task {
                            // Check subscription status when view appears
                            await checkSubscriptionStatus()
                            // 🚀 PERFORMANCE FIX: Load activity count efficiently
                            await loadActivityCount()
                        }
                    
                    // Settings Sections
                    VStack(spacing: 16) {
                        // --- Custom Subscription Section ---
                        VStack(alignment: .leading, spacing: 12) {
                            // Use NSLocalizedString for clarity or if key differs from text
                            Text("settings.subscription.title".localized)
                                .font(.headline)
                                .foregroundStyle(.secondary)
                                .padding(.leading, 8)

                            VStack(spacing: 0) {
                                // Trial Status Text
                                Text(trialStatusText)
                                    .font(.subheadline)
                                    .foregroundStyle(trialStatusColor)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12) // Match item padding
                                    .frame(maxWidth: .infinity, alignment: .leading) // Ensure it takes full width
                                    .background(Color(.secondarySystemGroupedBackground)) // Match item background

                                Divider().padding(.leading, 16) // Add divider between text and button

                                // Unlimited Access Item (Replicated structure)
                                Button(action: checkSubscriptionAndShowPaywall) {
                                    HStack(spacing: 16) {
                                        Image(systemName: "star.fill")
                                            .font(.system(size: 20))
                                            .foregroundStyle(.yellow)
                                            .frame(width: 28)

                                        Text("settings.subscription.unlimitedAccess".localized)
                                            .foregroundStyle(.primary)

                                        Spacer()

                                        Text(isCheckingSubscription ? "settings.subscription.checkingStatus".localized : "")
                                            .foregroundStyle(.secondary)
                                            .font(.subheadline)

                                        Image(systemName: "chevron.right")
                                            .font(.caption.bold())
                                            .foregroundStyle(.secondary)
                                    }
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(Color(.secondarySystemGroupedBackground))
                                }
                            }
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                        }
                        // --- End Custom Subscription Section ---

                        SettingsSection(title: "profile".localized, items: [
                            SettingsItem(title: "gender".localized, icon: "person.2", color: .blue,
                                    value: profile.first?.gender.localized ?? "not_set".localized,
                                    action: { showingGenderSheet = true }),
                            SettingsItem(title: "age".localized, icon: "birthday.cake", color: .orange,
                                    value: profile.first?.age != nil ? "\(profile.first!.age!) \("years".localized)" : "not_set".localized,
                                    action: { showingAgeSheet = true }),
                            SettingsItem(title: "weight".localized, icon: "scalemass", color: .blue,
                                    value: formatWeight(),
                                    action: { showingWeightSheet = true }),
                            SettingsItem(title: "height".localized, icon: "pencil.and.ruler", color: .green,
                                    value: formatHeight(),
                                    action: { showingHeightSheet = true }),
                        ])
                        
                        SettingsSection(title: "audio".localized, items: [
                            SettingsItem(title: "audio_prompts".localized, icon: "speaker.wave.2", color: .orange,
                                    value: formatAudioAlertsStatus(),
                                    action: { showingAudioAlertsSheet = true }),
                            SettingsItem(title: "metronome".localized, icon: "metronome", color: .red,
                                    value: "\(profile.first?.metronomeBPM ?? 180) \("bpm".localized)",
                                    action: { showingMetronomeSheet = true }),
                        ])
                        
                        SettingsSection(title: "preferences".localized, items: [
                            SettingsItem(title: "units".localized, icon: "ruler.fill", color: .purple,
                                    value: profile.first?.units.rawValue.localized ?? "metric".localized,
                                    action: { showingUnitsSheet = true }),
                            SettingsItem(title: "theme".localized, icon: "paintpalette.fill", color: .pink,
                                    value: ThemeManager.shared.currentTheme.displayName,
                                    action: { showingThemeSheet = true }),
                            SettingsItem(title: "language".localized, icon: "globe", color: .blue,
                                    value: languageManager.getLanguageName(for: languageManager.currentLanguage),
                                    action: { showingLanguageSheet = true }),
                        ])
                        
                        SettingsSection(title: "data".localized, items: [
                            SettingsItem(title: "all_activities".localized, icon: "chart.bar.xaxis", color: .blue,
                                    // 🚀 PERFORMANCE FIX: Use cached count with loading state
                                    value: isLoadingActivityCount ? "Loading..." : "\(activityCount) \("activities".localized)",
                                    action: { showingActivityView = true }),
                            SettingsItem(title: "data_management".localized, icon: "folder.badge.gearshape", color: .green,
                                    value: "",
                                    action: { showingDataManagementView = true })
                        ])
                        
                        SettingsSection(title: "Developer", items: [
                            SettingsItem(title: "Debug Logs", icon: "doc.text.magnifyingglass", color: .gray,
                                    value: "Pace & Audio Debug",
                                    action: { showingDebugLogsView = true })
                        ])
                    }
                }
                .padding()
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle(Text(verbatim: "settings".localized))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundStyle(.secondary)
                            .font(.title2)
                    }
                }
            }
        }
        .environment(\.layoutDirection, languageManager.isRTL ? .rightToLeft : .leftToRight)
        .sheet(isPresented: $showingAgeSheet) {
            AgeSettingView()
        }
        .sheet(isPresented: $showingWeightSheet) {
            WeightSettingView()
        }
        .sheet(isPresented: $showingHeightSheet) {
            HeightSettingView()
        }
        .sheet(isPresented: $showingUnitsSheet) {
            UnitsSettingView()
        }
        .sheet(isPresented: $showingThemeSheet) {
            NavigationStack {
                ThemeSettingView()
            }
        }
        .sheet(isPresented: $showingMetronomeSheet) {
            MetronomeSettingsView()
        }
        .sheet(isPresented: $showingGenderSheet) {
            GenderSettingView()
        }
        .sheet(isPresented: $showingLanguageSheet) {
            LanguageSettingView()
        }
        .sheet(isPresented: $showingAudioAlertsSheet) {
            AudioAlertSettingsView()
        }

        .sheet(isPresented: $showingActivityView) {
            NavigationStack {
                ActivityView()
            }
        }
        .sheet(isPresented: $showingDebugLogsView) {
            LogViewerView()
        }
        .sheet(isPresented: $showingDataManagementView) {
            NavigationStack {
                DataManagementView(onDismissToContentView: {
                    // Dismiss both DataManagementView and SettingsView to return to ContentView
                    showingDataManagementView = false
                    dismiss()
                })
            }
        }
        .sheet(isPresented: $isShowingPaywall) {
            // Present RevenueCat's PaywallView
            PaywallView(displayCloseButton: true)
        }
        // Use NSLocalizedString for alert title and message
        .alert("settings.subscription.alert.alreadySubscribed.title".localized, isPresented: $isShowingAlreadySubscribedAlert) {
            Button("ok".localized, role: .cancel) { }
        } message: {
            Text("settings.subscription.alert.alreadySubscribed.message".localized)
        }
    }

    // MARK: - Trial Status Computed Properties
    private var hasExceededTrial: Bool {
        // Safely check the profile and the flag
        profile.first?.hasExceededTrialLimit ?? false
    }

    // MARK: - Subscription Status
    private func checkSubscriptionStatus() async {
        do {
            let customerInfo = try await Purchases.shared.customerInfo()
            hasProEntitlement = customerInfo.entitlements.active.keys.contains("Pro")
        } catch {
            print("Error checking subscription status: \(error)")
            hasProEntitlement = false
        }
    }

    // MARK: - 🚀 PERFORMANCE OPTIMIZATION: Background Activity Count Loading
    
    /// Load activity count efficiently in background without loading coordinate data
    private func loadActivityCount() async {
        isLoadingActivityCount = true
        
        // First: Use cached value for instant display
        if let profile = profile.first, profile.cachedActivityCount > 0 {
            activityCount = profile.cachedActivityCount
        }
        
        // Then: Fetch accurate count in background
        do {
            let descriptor = FetchDescriptor<RunActivity>()
            let actualCount = try await Task.detached {
                let backgroundContext = ModelContext(modelContext.container)
                return try backgroundContext.fetchCount(descriptor)
            }.value
            
            // Update both state and cache
            await MainActor.run {
                self.activityCount = actualCount
                self.isLoadingActivityCount = false
                
                // Update UserProfile cache
                if let profile = profile.first {
                    profile.cachedActivityCount = actualCount
                    profile.lastActivityCountUpdate = Date()
                    try? modelContext.save()
                }
            }
        } catch {
            print("Error loading activity count: \(error)")
            await MainActor.run {
                self.isLoadingActivityCount = false
                // Keep showing cached count on error
            }
        }
    }

    private var trialStatusText: String {
        if hasProEntitlement {
            return "settings.subscription.status.unlimited".localized
        }
        
        if hasExceededTrial {
            return "settings.subscription.status.exceeded".localized
        }
        
        // 🚀 PERFORMANCE FIX: Use cached activityCount instead of activities.count
        let usedCount = activityCount
        let remainingCount = AppConstants.freeTrialLimit - usedCount
        let displayRemaining = max(0, remainingCount)
        
        // Show different messages for singular and plural cases
        if displayRemaining == 1 {
            return "settings.subscription.status.oneRemaining".localized
        }
        
        return String(format: "settings.subscription.status.remaining".localized, displayRemaining)
    }

    private var trialStatusColor: Color {
        if hasProEntitlement {
            return .green // Show green for unlimited access
        }
        return hasExceededTrial ? .orange : .secondary
    }

    // MARK: - RevenueCat Action
    private func checkSubscriptionAndShowPaywall() {
        guard !isCheckingSubscription else { return }
        isCheckingSubscription = true

        Task {
            defer { isCheckingSubscription = false }
            do {
                let customerInfo = try await Purchases.shared.customerInfo()
                hasProEntitlement = customerInfo.entitlements.active.keys.contains("Pro")
                if hasProEntitlement {
                    isShowingAlreadySubscribedAlert = true
                } else {
                    // User not subscribed, show paywall
                    isShowingPaywall = true
                }
            } catch {
                print("Error fetching customer info: \(error)")
                // Use String(format:) with localized format key for error message
                print("Failed to check subscription status: \(error)")
            }
        }
    }
    
    // MARK: - Formatting Helpers
    private func formatWeight() -> String {
        guard let userProfile = profile.first,
              let weightInKg = userProfile.weight else { return "not_set".localized }
        
        let unit = userProfile.weightUnit ?? "kg"
        let displayWeight = unit == "lbs" ? weightInKg * 2.20462 : weightInKg
        return String(format: "%.1f %@", displayWeight, unit)
    }
    
    private func formatHeight() -> String {
        //print("DEBUG: Formatting height for display")
        guard let userProfile = profile.first,
              let height = userProfile.height else {
            //print("DEBUG: Height not found in profile")
            return "not_set".localized
        }
        
        if let heightUnit = userProfile.heightUnit {
            //print("DEBUG: Height unit found: \(heightUnit)")
            if heightUnit == "cm" {
                let formatted = String(format: "%.1f cm", height)
                //print("DEBUG: Formatted cm height: \(formatted)")
                return formatted
            } else if heightUnit == "ft/in" {
                let (feet, inches) = cmToFeetInches(height)
                let formatted = String(format: "%d'%0.1f\"", feet, inches)
                //print("DEBUG: Formatted ft/in height: \(formatted)")
                return formatted
            }
            //print("DEBUG: Unrecognized height unit: \(heightUnit)")
        } else {
            print("DEBUG: No height unit found in profile")
        }
        return "not_set".localized
    }
    
    private func cmToFeetInches(_ cm: Double) -> (feet: Int, inches: Double) {
        let totalInches = cm / 2.54
        let feet = Int(totalInches / 12)
        let inches = totalInches.truncatingRemainder(dividingBy: 12)
        return (feet, inches)
    }
    
    private func formatAudioAlertsStatus() -> String {
        guard let userProfile = profile.first else { return "disabled".localized }
        
        if !userProfile.audioAlertsEnabled {
            return "disabled".localized
        }
        
        // Count enabled alert types
        var enabledAlerts: [String] = []
        if userProfile.distanceAlertEnabled {
            enabledAlerts.append("distance_short".localized)
        }
        if userProfile.timeAlertEnabled {
            enabledAlerts.append("time_short".localized)
        }
        if userProfile.calorieAlertEnabled {
            enabledAlerts.append("calorie_short".localized)
        }
        if userProfile.paceAlertEnabled {
            enabledAlerts.append("pace_short".localized)
        }
        
        if enabledAlerts.isEmpty {
            return "enabled".localized + " • " + "no_alerts_configured".localized
        } else if enabledAlerts.count == 1 {
            return enabledAlerts[0]
        } else if enabledAlerts.count == 2 {
            return enabledAlerts.joined(separator: " • ")
        } else if enabledAlerts.count == 4 {
            // All 4 alert types are enabled
            return "all_alerts".localized
        } else {
            // 3 alert types are enabled - show all three names
            return enabledAlerts.joined(separator: " • ")
        }
    }
    
}

struct StatCard: View {
    let title: String
    let value: String
    let unit: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(.yellow)
            
            Text(value)
                .font(.title3.bold())
            
            if !unit.isEmpty {
                Text(unit)
                    .font(.caption2)
                    .foregroundStyle(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 20)
        .background {
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemGroupedBackground))
        }
    }
}

struct SettingsItem: Identifiable {
    let id = UUID()
    let title: String
    let icon: String
    let color: Color
    let value: String
    let action: () -> Void
}

struct SettingsSection: View {
    let title: String
    let items: [SettingsItem]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundStyle(.secondary)
                .padding(.leading, 8)
            
            VStack(spacing: 0) {
                ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                    Button(action: item.action) {
                        HStack(spacing: 16) {
                            Image(systemName: item.icon)
                                .font(.system(size: 20))
                                .foregroundStyle(item.color)
                                .frame(width: 28)
                            
                            Text(item.title)
                                .foregroundStyle(.primary)
                            
                            Spacer()
                            
                            Text(item.value)
                                .foregroundStyle(.secondary)
                                .font(.subheadline)
                            
                            Image(systemName: "chevron.right")
                                .font(.caption.bold())
                                .foregroundStyle(.secondary)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color(.secondarySystemGroupedBackground))
                    }
                    
                    if index < items.count - 1 {
                        Divider()
                            .padding(.leading, 60)
                    }
                }
            }
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    let applicationActivities: [UIActivity]? = nil

    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: applicationActivities
        )
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: UserProfile.self, configurations: config)
    
    return SettingsView()
        .modelContainer(container)
}
