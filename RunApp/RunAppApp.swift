//
//  RunAppApp.swift
//  RunApp
//
//  Created by <PERSON><PERSON><PERSON> on 1/11/25.
//

import SwiftUI
import SwiftData
import UserNotifications
import BackgroundTasks
import RevenueCat
import RevenueCatUI
import HealthKit

@main
struct RunAppApp: App {
    @State private var modelContainer: ModelContainer?
    @State private var error: Error?
    @Bindable private var themeManager = ThemeManager.shared
    @State private var themeUpdate = UUID()
    @State private var languageUpdate = UUID()
    private let languageManager = LanguageManager.shared
    @AppStorage("hasCompletedOnboarding") private var localHasCompletedOnboarding: Bool = false // Renamed
    @State private var effectiveHasCompletedOnboarding: Bool = false // New state for view logic
    @State private var isLoadingProfile: Bool = true // To show progress view while checking profile
    @StateObject private var locationManager = LocationManager.shared
    
    // Use class-based task handler to avoid 'mutating self' capture issues
    private let backgroundTaskHandler = BackgroundTaskHandler()
    
    init() {
        // Eagerly initialize LanguageManager to ensure bundle is set early
        _ = LanguageManager.shared
        
        // The language bundle will be initialized in LanguageManager's init
        print(URL.applicationSupportDirectory.path(percentEncoded: false))
        
        // Register background tasks using the handler class to avoid capturing 'self'
        backgroundTaskHandler.registerBackgroundTasks()

        Purchases.logLevel = .info
        Purchases.configure(withAPIKey: "appl_rxnNiaibGgOEnqUtFAWiNFbcjkY")
    }
    
    var body: some Scene {
        WindowGroup {
            Group {
                if isLoadingProfile {
                    ProgressView() // Show loading indicator while checking profile
                } else if let container = modelContainer {
                    if !effectiveHasCompletedOnboarding { // Use the new state variable
                            WelcomeView()
                        .modelContainer(container)
                    } else {
                        TrialWrapperView(modelContainer: container)
                    }
                } else if let error = error {
                    Text(verbatim: error.localizedDescription)
                } else {
                    ProgressView() // Initial loading of modelContainer
                }
            }
            .id("\(themeUpdate)-\(languageUpdate)") // Force view refresh on theme or language change
            .preferredColorScheme(themeManager.colorScheme)
            .environment(\.layoutDirection, languageManager.isRTL ? .rightToLeft : .leftToRight)
            .onChange(of: themeManager.currentTheme) { _, _ in
                themeUpdate = UUID()
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("LanguageChanged"))) { _ in
                languageUpdate = UUID()
            }
            .onChange(of: localHasCompletedOnboarding) { oldValue, newValue in
                if newValue == true {
                    self.effectiveHasCompletedOnboarding = true
                }
            }
            .task {
                // Initialize modelContainer first
                do {
                    let schema = Schema([
                        RunActivity.self,
                        UserProfile.self
                    ])
                    // Configure for CloudKit with automatic migration
                    let cloudKitContainerIdentifier = "iCloud.app.isotropic.runapp"
                    let configuration = ModelConfiguration(
                        url: URL.applicationSupportDirectory.appending(path: "RunApp.sqlite"),
                        cloudKitDatabase: .private(cloudKitContainerIdentifier)
                    )
                    
                    let newContainer = try ModelContainer(for: schema, configurations: configuration)
                    self.modelContainer = newContainer // Assign to the @State property
                    
                    // Now check for existing profile
                    await checkExistingUserProfile(container: newContainer)
                    
                    // Register for remote notifications to facilitate CloudKit syncing
                    registerForRemoteNotifications()
                    
                    // Request HealthKit authorization
                    await requestHealthKitAuthorization()
                    
                    print("ModelContainer initialized, profile checked, registered for remote notifications, and HealthKit authorization requested.")
                
                } catch {
                    print("Failed to initialize ModelContainer with CloudKit: \(error)")
                    
                    // Fallback: Try creating a local-only container
                    do {
                        print("Attempting fallback with local-only ModelContainer...")
                        let fallbackSchema = Schema([
                            RunActivity.self,
                            UserProfile.self
                        ])
                        let localConfiguration = ModelConfiguration("RunAppLocal")
                        let fallbackContainer = try ModelContainer(for: fallbackSchema, configurations: localConfiguration)
                        self.modelContainer = fallbackContainer
                        
                        await checkExistingUserProfile(container: fallbackContainer)
                        print("Fallback ModelContainer initialized successfully.")
                        
                    } catch {
                        self.error = error
                        self.isLoadingProfile = false
                        print("Failed to initialize fallback ModelContainer: \(error)")
                    }
                }
            }
        }
    }

    func checkExistingUserProfile(container: ModelContainer) async {
        defer { isLoadingProfile = false }

        // Fetch UserProfiles, sorted by lastModifiedDate descending
        let descriptor = FetchDescriptor<UserProfile>(sortBy: [SortDescriptor(\UserProfile.lastModifiedDate, order: .reverse)])
        
        do {
            let profiles = try container.mainContext.fetch(descriptor)
            
            if let primaryProfile = profiles.first {
                // At least one profile exists. Use this one (the newest).
                self.effectiveHasCompletedOnboarding = true
                self.localHasCompletedOnboarding = true // Keep AppStorage in sync

                // If there were duplicates, delete the older ones
                if profiles.count > 1 {
                    print("Multiple UserProfiles found. Keeping the one last modified at \(primaryProfile.lastModifiedDate) and deleting \(profiles.count - 1) older profile(s).")
                    // Iterate from the second element to the end to delete older profiles
                    for i in 1..<profiles.count {
                        container.mainContext.delete(profiles[i])
                    }
                    
                    // Attempt to save the deletions.
                    try container.mainContext.save()
                    print("Older UserProfiles deleted.")
                }
            } else {
                // No UserProfile found locally or synced from iCloud.
                // Rely on local AppStorage to decide if onboarding is needed.
                self.effectiveHasCompletedOnboarding = self.localHasCompletedOnboarding
            }
        } catch {
            print("Failed to fetch or process UserProfile for onboarding check: \(error)")
            // Fallback: rely on local AppStorage if fetch/processing fails
            self.effectiveHasCompletedOnboarding = self.localHasCompletedOnboarding
        }
    }
}

// Class to handle background tasks to avoid struct mutation issues
class BackgroundTaskHandler {
    func registerBackgroundTasks() {
        BGTaskScheduler.shared.register(forTaskWithIdentifier: "app.isotropic.runapp.locationUpdate", using: nil) { task in
            self.handleLocationUpdateTask(task: task as! BGProcessingTask)
        }
        
        print("BackgroundTaskHandler: Registered background tasks")
    }
    
    func handleLocationUpdateTask(task: BGProcessingTask) {
        // Schedule a new background task
        scheduleLocationUpdateTask()
        
        // Create a task assertion to keep the app running in background
        task.expirationHandler = {
            // If the task expires, clean up any resources
            print("Background location task expired")
        }
        
        // Mark the task complete
        task.setTaskCompleted(success: true)
    }
    
    func scheduleLocationUpdateTask() {
        let request = BGProcessingTaskRequest(identifier: "app.isotropic.runapp.locationUpdate")
        request.requiresNetworkConnectivity = false
        request.requiresExternalPower = false
        
        do {
            try BGTaskScheduler.shared.submit(request)
            print("Background location task scheduled")
        } catch {
            print("Could not schedule background location task: \(error.localizedDescription)")
        }
    }
}

// Extension to handle remote notification registration
extension RunAppApp {
    func registerForRemoteNotifications() {
        UIApplication.shared.registerForRemoteNotifications()
    }
    
    func requestHealthKitAuthorization() async {
        do {
            try await HealthKitManager.shared.requestAuthorization()
            print("HealthKit authorization completed successfully")
        } catch {
            print("HealthKit authorization failed: \(error)")
            // App continues to work without HealthKit if authorization fails
        }
    }
}
