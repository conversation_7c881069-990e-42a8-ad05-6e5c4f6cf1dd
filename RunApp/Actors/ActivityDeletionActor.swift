//
//  ActivityDeletionActor.swift
//  RunApp
//
//  Created by <PERSON>oo on 6/4/25.
//

import SwiftData
import Foundation

@ModelActor
actor ActivityDeletionActor {
    
    /// Safely delete a workout activity with large location data on background thread
    func deleteActivity(activityId: UUID) async throws {
        // Fetch the activity by ID on the background context
        let descriptor = FetchDescriptor<RunActivity>(
            predicate: #Predicate<RunActivity> { activity in
                activity.id == activityId
            }
        )
        
        guard let activities = try? modelContext.fetch(descriptor),
              let activity = activities.first else {
            throw DeletionError.activityNotFound
        }
        
        // Log the deletion operation for debugging
        let coordinateCount = activity.coordinates.count
        print("ActivityDeletionActor: Deleting activity with \(coordinateCount) coordinates")
        
        // Clear any cached data to free memory before deletion
        activity.clearSimplificationCache()
        
        // Perform the deletion on the background serial thread
        modelContext.delete(activity)
        
        // Save the changes - this is the heavy operation that was blocking UI
        try modelContext.save()
        
        print("ActivityDeletionActor: Successfully deleted activity \(activityId)")
    }
    
    /// Batch delete multiple activities with progress reporting
    func deleteActivities(
        activityIds: [UUID], 
        progressCallback: @Sendable (Int, Int) async -> Void
    ) async throws {
        let totalCount = activityIds.count
        var deletedCount = 0
        
        for activityId in activityIds {
            let descriptor = FetchDescriptor<RunActivity>(
                predicate: #Predicate<RunActivity> { activity in
                    activity.id == activityId
                }
            )
            
            if let activities = try? modelContext.fetch(descriptor),
               let activity = activities.first {
                
                // Clear cache before deletion
                activity.clearSimplificationCache()
                modelContext.delete(activity)
                deletedCount += 1
                
                // Report progress every 5 deletions or at the end
                if deletedCount % 5 == 0 || deletedCount == totalCount {
                    await progressCallback(deletedCount, totalCount)
                }
            }
        }
        
        // Batch save all deletions
        try modelContext.save()
    }
    
    /// Delete all activities of a specific sport type
    func deleteAllActivities(ofType sportType: SportType? = nil) async throws {
        var descriptor: FetchDescriptor<RunActivity>
        
        if let sportType = sportType {
            descriptor = FetchDescriptor<RunActivity>(
                predicate: #Predicate<RunActivity> { activity in
                    activity.sportType == sportType
                }
            )
        } else {
            descriptor = FetchDescriptor<RunActivity>()
        }
        
        let activities = try modelContext.fetch(descriptor)
        print("ActivityDeletionActor: Deleting \(activities.count) activities of type \(sportType?.rawValue ?? "all")")
        
        for activity in activities {
            activity.clearSimplificationCache()
            modelContext.delete(activity)
        }
        
        try modelContext.save()
        print("ActivityDeletionActor: Successfully deleted \(activities.count) activities")
    }
    
    /// Log memory usage before deletion for monitoring
    private func logMemoryUsage(before activity: RunActivity) {
        let memoryUsage = activity.estimatedMemoryUsage
        if memoryUsage > 1_000_000 { // 1MB threshold
            print("ActivityDeletionActor: Deleting large activity (\(memoryUsage) bytes)")
        }
    }
    
    /// Enhanced deleteActivity with memory monitoring
    func deleteActivityWithMonitoring(activityId: UUID) async throws {
        // Fetch the activity by ID on the background context
        let descriptor = FetchDescriptor<RunActivity>(
            predicate: #Predicate<RunActivity> { activity in
                activity.id == activityId
            }
        )
        
        guard let activities = try? modelContext.fetch(descriptor),
              let activity = activities.first else {
            throw DeletionError.activityNotFound
        }
        
        // Log memory usage before deletion
        logMemoryUsage(before: activity)
        
        // Log the deletion operation for debugging
        let coordinateCount = activity.coordinates.count
        print("ActivityDeletionActor: Deleting activity with \(coordinateCount) coordinates (Memory: \(activity.estimatedMemoryUsage) bytes)")
        
        // Clear any cached data to free memory before deletion
        activity.clearSimplificationCache()
        
        // Perform the deletion on the background serial thread
        modelContext.delete(activity)
        
        // Save the changes - this is the heavy operation that was blocking UI
        try modelContext.save()
        
        print("ActivityDeletionActor: Successfully deleted activity \(activityId)")
    }
}

enum DeletionError: Error, LocalizedError {
    case activityNotFound
    case deletionFailed(underlying: Error)
    
    var errorDescription: String? {
        switch self {
        case .activityNotFound:
            return "Activity not found for deletion"
        case .deletionFailed(let error):
            return "Deletion failed: \(error.localizedDescription)"
        }
    }
}