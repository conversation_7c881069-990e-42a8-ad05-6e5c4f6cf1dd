import Foundation
import SwiftUI

@Observable
class LanguageManager {
    static let shared = LanguageManager()
    // Static property to hold the currently active language bundle
    static var currentBundle = Bundle.main
    
    // Store the effective language code
    var currentLanguage: String {
        didSet {
            // Save the user's explicit choice if different from old value
            if oldValue != currentLanguage {
                 UserDefaults.standard.set(currentLanguage, forKey: "app_language")
                 UserDefaults.standard.synchronize()
                 // Post notification for language change - Keep this
                 NotificationCenter.default.post(name: Notification.Name("LanguageChanged"), object: nil)
            }
        }
    }
    
    var isRTL: Bool {
        return currentLanguage == "ar"
    }
    
    private init() {
        // Try to get saved language, otherwise use system language
        if let savedLanguage = UserDefaults.standard.string(forKey: "app_language") {
            // Migrate "zh" to "zh-Hans" for existing users
            if savedLanguage == "zh" {
                self.currentLanguage = "zh-Hans"
            } else {
                self.currentLanguage = savedLanguage
            }
        } else {
            // No saved language, determine the best initial language based on system preferences
            // Use Bundle.preferredLocalizations to find the best match between user's preferred languages and app's available languages
            let preferredOrDefaultLang = Bundle.preferredLocalizations(from: availableLanguages, forPreferences: Locale.preferredLanguages).first ?? "en"
            self.currentLanguage = preferredOrDefaultLang
            print("LanguageManager Init: No saved language found. Using preferred or default: \(self.currentLanguage)")
        }
        // Load the initial bundle based on the determined language
        updateCurrentBundle(for: self.currentLanguage)
    }

    // Helper function to load and set the current bundle
    private func updateCurrentBundle(for languageCode: String) {
        guard let path = Bundle.main.path(forResource: languageCode, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            // Fallback to main bundle if specific language bundle not found
            LanguageManager.currentBundle = Bundle.main
            print("Warning: Could not find bundle for language code '\(languageCode)'. Falling back to main bundle.")
            return
        }
        LanguageManager.currentBundle = bundle
        print("LanguageManager: Set current bundle to '\(languageCode)'")
    }
    
    let availableLanguages = ["ar", "zh-Hans", "zh-Hant", "de", "en", "fr", "id", "it", "ja", "ko", "pt", "ru", "es"]
    
    let languageNames = [
        "ar": "العربية",
        "zh-Hans": "简体中文",
        "zh-Hant": "繁體中文",
        "de": "Deutsch",
        "en": "English",
        "fr": "Français",
        "id": "Bahasa Indonesia",
        "it": "Italiano",
        "ja": "日本語",
        "ko": "한국어",
        "pt": "Português",
        "ru": "Русский",
        "es": "Español"
    ]
    func setLanguage(_ code: String) {
        guard availableLanguages.contains(code) else { return }
        // Set the standard AppleLanguages key to inform iOS of the preference
        UserDefaults.standard.set([code], forKey: "AppleLanguages")
        UserDefaults.standard.synchronize() // Ensure it's saved immediately

        // Update the active bundle for the current session
        updateCurrentBundle(for: code)

        // Update our internal state and notify listeners
        // The didSet observer will save to "app_language" and post notification
        currentLanguage = code
    }

    func getLanguageName(for code: String) -> String {
        return languageNames[code] ?? code
    }
}

// Extension to make localization easier to use in SwiftUI
extension String {
    var localized: String {
        // Explicitly use the currently set bundle for immediate updates
        return LanguageManager.currentBundle.localizedString(forKey: self, value: nil, table: nil)
    }
}

extension Text {
    init(localized key: String) {
        // Explicitly use the currently set bundle for immediate updates
        self.init(LanguageManager.currentBundle.localizedString(forKey: key, value: nil, table: nil))
    }
}

// Removed custom Bundle extension for language switching
