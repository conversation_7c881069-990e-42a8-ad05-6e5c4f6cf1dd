import Foundation
import SwiftUI
import Combine

@MainActor
final class LogManager: ObservableObject {
    // MARK: - Singleton
    static let shared = LogManager()
    
    // MARK: - Published Properties
    @Published var isLoggingEnabled = false // Default disabled for production safety
    
    // MARK: - Private Properties
    private let maxLogFiles = 10
    private let logsDirectory: URL
    private var currentLogFile: URL?
    private var currentLogFileHandle: FileHandle?
    private var warningTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Log Categories
    enum LogCategory: String, CaseIterable {
        case pace = "PACE"
        case audio = "AUDIO"
        case location = "LOCATION"
        case general = "GENERAL"
        
        var emoji: String {
            switch self {
            case .pace: return "⏱️"
            case .audio: return "🔊"
            case .location: return "📍"
            case .general: return "ℹ️"
            }
        }
    }
    
    // MARK: - Initialization
    private init() {
        // Create logs directory in Documents
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        logsDirectory = documentsPath.appendingPathComponent("DebugLogs")
        
        // Create directory if it doesn't exist
        try? FileManager.default.createDirectory(at: logsDirectory, withIntermediateDirectories: true)
        
        // Clean up old logs and start new session
        cleanupOldLogs()
        startNewLogSession()
        
        // Set up observer for logging state changes
        setupLoggingStateObserver()
    }
    
    deinit {
        currentLogFileHandle?.closeFile()
        currentLogFileHandle = nil
        currentLogFile = nil
        warningTimer?.invalidate()
        warningTimer = nil
    }
    
    // MARK: - Public Logging Methods
    
    /// Generic logging method for all categories with optimized performance
    nonisolated func writeLog(_ message: String, category: LogCategory = .general) {
        // Early exit check to prevent ALL overhead when logging is disabled
        Task { @MainActor in
            guard LogManager.shared.isLoggingEnabled else { return }
            
            // Only perform expensive operations when logging is enabled
            let timestamp = DateFormatter.logTimestamp.string(from: Date())
            let logEntry = "[\(timestamp)] \(category.emoji) \(category.rawValue): \(message)\n"
            
            // Print to console for immediate debugging with warning prefix
            print("⚠️ DEBUG LOG: \(logEntry.trimmingCharacters(in: .whitespacesAndNewlines))")
            
            LogManager.shared.writeToCurrentLogFile(logEntry)
        }
    }
    
    /// Logs with MainActor safety and optimized performance
    func log(_ message: String, category: LogCategory = .general) {
        guard isLoggingEnabled else { return }
        
        let timestamp = DateFormatter.logTimestamp.string(from: Date())
        let logEntry = "[\(timestamp)] \(category.emoji) \(category.rawValue): \(message)\n"
        
        // Print to console for immediate debugging with warning prefix
        print("⚠️ DEBUG LOG: \(logEntry.trimmingCharacters(in: .whitespacesAndNewlines))")
        
        writeToCurrentLogFile(logEntry)
    }
    
    /// Log pace calculation details with validation status - optimized for zero overhead when disabled
    nonisolated func logPace(rawSpeed: Double, calculatedPace: Double, isValid: Bool, source: String, additionalInfo: String = "") {
        // Early exit to prevent ALL string formatting overhead when logging is disabled
        Task { @MainActor in
            guard LogManager.shared.isLoggingEnabled else { return }
            
            // Only perform expensive string formatting when logging is enabled
            let speedText = rawSpeed > 0 ? String(format: "%.2f m/s", rawSpeed) : "invalid"
            let paceText = calculatedPace > 0 ? String(format: "%.2f min/km", calculatedPace) : "invalid"
            let validText = isValid ? "✅" : "❌"
            let info = additionalInfo.isEmpty ? "" : " | \(additionalInfo)"
            
            let message = "[\(source)] Speed: \(speedText) → Pace: \(paceText) \(validText)\(info)"
            LogManager.shared.writeLog(message, category: .pace)
        }
    }
    
    /// Log audio alert trigger decisions with context - optimized for zero overhead when disabled
    nonisolated func logAudioAlert(type: String, triggered: Bool, pace: Double, reason: String = "") {
        // Early exit to prevent ALL string formatting overhead when logging is disabled
        Task { @MainActor in
            guard LogManager.shared.isLoggingEnabled else { return }
            
            // Only perform expensive string formatting when logging is enabled
            let triggerText = triggered ? "🎯 TRIGGERED" : "⏭️ SKIPPED"
            let paceText = pace > 0 ? String(format: "%.2f min/km", pace) : "invalid"
            let reasonText = reason.isEmpty ? "" : " | Reason: \(reason)"
            
            let message = "[\(type)] \(triggerText) | Pace: \(paceText)\(reasonText)"
            LogManager.shared.writeLog(message, category: .audio)
        }
    }
    
    /// Log location update details with accuracy and validity - optimized for zero overhead when disabled
    nonisolated func logLocation(accuracy: Double, speed: Double, isValid: Bool, additionalInfo: String = "") {
        // Early exit to prevent ALL string formatting overhead when logging is disabled
        Task { @MainActor in
            guard LogManager.shared.isLoggingEnabled else { return }
            
            // Only perform expensive string formatting when logging is enabled
            let accuracyText = String(format: "±%.1fm", accuracy)
            let speedText = speed >= 0 ? String(format: "%.2f m/s", speed) : "invalid"
            let validText = isValid ? "✅" : "❌"
            let info = additionalInfo.isEmpty ? "" : " | \(additionalInfo)"
            
            let message = "Location: \(accuracyText), Speed: \(speedText) \(validText)\(info)"
            LogManager.shared.writeLog(message, category: .location)
        }
    }
    
    // MARK: - Logging State Management
    
    private func setupLoggingStateObserver() {
        $isLoggingEnabled
            .sink { [weak self] isEnabled in
                if isEnabled {
                    self?.handleLoggingEnabled()
                } else {
                    self?.handleLoggingDisabled()
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleLoggingEnabled() {
        print("🚨 WARNING: DEBUG LOGGING ENABLED - This may cause performance issues and MUST be disabled before production!")
        log("🚨 DEBUG LOGGING ENABLED - DEVELOPER MODE ACTIVE", category: .general)
        log("⚠️ WARNING: High CPU usage and battery drain expected", category: .general)
        log("⚠️ CRITICAL: DISABLE before App Store submission!", category: .general)
        startWarningTimer()
    }
    
    private func handleLoggingDisabled() {
        print("✅ Debug logging disabled - Performance restored")
        stopWarningTimer()
    }
    
    private func startWarningTimer() {
        stopWarningTimer()
        
        warningTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            Task { @MainActor [weak self] in
                self?.log("⚠️ PERIODIC WARNING: Debug logging still active - Remember to disable before production!", category: .general)
            }
            print("🚨 REMINDER: Debug logging is still enabled - Disable before App Store submission!")
        }
    }
    
    private func stopWarningTimer() {
        warningTimer?.invalidate()
        warningTimer = nil
    }
    
    // MARK: - File Management
    
    private func startNewLogSession() {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let timestamp = dateFormatter.string(from: Date())
        let fileName = "PaceDebug_\(timestamp).log"
        
        currentLogFile = logsDirectory.appendingPathComponent(fileName)
        
        // Create file and add header
        let header = """
        ==========================================
        RunApp Debug Log
        Session started: \(DateFormatter.logTimestamp.string(from: Date()))
        ==========================================
        
        """
        
        do {
            try header.write(to: currentLogFile!, atomically: true, encoding: .utf8)
            currentLogFileHandle = try FileHandle(forWritingTo: currentLogFile!)
            currentLogFileHandle?.seekToEndOfFile()
            
            log("Debug log session started", category: .general)
        } catch {
            print("Failed to create log file: \(error)")
        }
    }
    
    private func writeToCurrentLogFile(_ entry: String) {
        guard let handle = currentLogFileHandle,
              let data = entry.data(using: .utf8) else { return }
        
        handle.write(data)
    }
    
    private func closeCurrentLogFile() {
        currentLogFileHandle?.closeFile()
        currentLogFileHandle = nil
        currentLogFile = nil
    }
    
    private func cleanupOldLogs() {
        do {
            let logFiles = try FileManager.default.contentsOfDirectory(
                at: logsDirectory,
                includingPropertiesForKeys: [.creationDateKey]
            )
            .filter { $0.pathExtension == "log" }
            .sorted { (file1: URL, file2: URL) in
                let date1 = (try? file1.resourceValues(forKeys: [.creationDateKey]).creationDate) ?? Date.distantPast
                let date2 = (try? file2.resourceValues(forKeys: [.creationDateKey]).creationDate) ?? Date.distantPast
                return date1 > date2
            }
            
            // Keep only the most recent logs
            if logFiles.count > maxLogFiles {
                let filesToDelete = Array(logFiles.dropFirst(maxLogFiles))
                for file in filesToDelete {
                    try? FileManager.default.removeItem(at: file)
                }
            }
        } catch {
            print("Error cleaning up old logs: \(error)")
        }
    }
    
    // MARK: - Log Retrieval
    
    /// Get all available log files sorted by creation date (newest first)
    func getLogFiles() -> [LogFile] {
        do {
            let logFiles = try FileManager.default.contentsOfDirectory(
                at: logsDirectory,
                includingPropertiesForKeys: [.creationDateKey, .fileSizeKey]
            )
            .filter { $0.pathExtension == "log" }
            .compactMap { (url: URL) -> LogFile? in
                guard let resourceValues = try? url.resourceValues(forKeys: [.creationDateKey, .fileSizeKey]),
                      let creationDate = resourceValues.creationDate,
                      let fileSize = resourceValues.fileSize else { return nil }
                
                return LogFile(
                    url: url,
                    name: url.lastPathComponent,
                    creationDate: creationDate,
                    size: Int64(fileSize)
                )
            }
            .sorted { $0.creationDate > $1.creationDate }
            
            return logFiles
        } catch {
            print("Error retrieving log files: \(error)")
            return []
        }
    }
    
    /// Get the content of a specific log file asynchronously
    func getLogContent(for logFile: LogFile) async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            Task.detached {
                do {
                    // Check if file exists first
                    guard FileManager.default.fileExists(atPath: logFile.url.path) else {
                        continuation.resume(throwing: LogError.fileNotFound)
                        return
                    }
                    
                    // Read file content
                    let content = try String(contentsOf: logFile.url, encoding: .utf8)
                    continuation.resume(returning: content)
                } catch {
                    continuation.resume(throwing: LogError.readError(error.localizedDescription))
                }
            }
        }
    }
    
    /// Get the content of a specific log file synchronously (legacy support)
    func getLogContentSync(for logFile: LogFile) -> String {
        do {
            guard FileManager.default.fileExists(atPath: logFile.url.path) else {
                return "Error: Log file not found"
            }
            return try String(contentsOf: logFile.url, encoding: .utf8)
        } catch {
            return "Error reading log file: \(error.localizedDescription)"
        }
    }
    
    /// Clear all existing log files and start a new session
    func clearAllLogs() {
        closeCurrentLogFile()
        
        do {
            let logFiles = try FileManager.default.contentsOfDirectory(
                at: logsDirectory,
                includingPropertiesForKeys: nil
            )
            .filter { $0.pathExtension == "log" }
            
            for file in logFiles {
                try FileManager.default.removeItem(at: file)
            }
        } catch {
            print("Error clearing logs: \(error)")
        }
        
        // Start new session
        startNewLogSession()
    }
    
    /// Export a log file for sharing
    func exportLog(_ logFile: LogFile) -> URL {
        return logFile.url
    }
}

// MARK: - Error Types

enum LogError: Error, LocalizedError {
    case fileNotFound
    case readError(String)
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "Log file not found"
        case .readError(let message):
            return "Failed to read log file: \(message)"
        }
    }
}

// MARK: - Supporting Types

struct LogFile: Identifiable {
    let id = UUID()
    let url: URL
    let name: String
    let creationDate: Date
    let size: Int64
    
    var formattedDate: String {
        DateFormatter.displayFormatter.string(from: creationDate)
    }
    
    var formattedSize: String {
        ByteCountFormatter.string(fromByteCount: size, countStyle: .file)
    }
}

// MARK: - Date Formatters Extension

extension DateFormatter {
    static let logTimestamp: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
    
    static let displayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
}
