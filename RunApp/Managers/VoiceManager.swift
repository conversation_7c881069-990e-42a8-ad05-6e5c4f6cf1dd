import Foundation
import AVFoundation

/// Represents a voice with its properties and quality information
struct VoiceInfo: Identifiable, Equatable {
    let id: String
    let name: String
    let language: String
    let quality: VoiceQuality
    let isInstalled: Bool
    let isDeletable: Bool
    let voice: AVSpeechSynthesisVoice
    
    static func == (lhs: VoiceInfo, rhs: VoiceInfo) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Voice quality levels available in iOS
enum VoiceQuality: String, CaseIterable {
    case standard = "Standard"
    case enhanced = "Enhanced"
    case premium = "Premium"
    
    var displayName: String {
        switch self {
        case .standard:
            return "voice.quality.standard".localized
        case .enhanced:
            return "voice.quality.enhanced".localized
        case .premium:
            return "voice.quality.premium".localized
        }
    }
    
    var priority: Int {
        switch self {
        case .standard: return 1
        case .enhanced: return 2
        case .premium: return 3
        }
    }
}

@MainActor
class VoiceManager: ObservableObject {
    static let shared = VoiceManager()
    
    @Published private(set) var availableVoices: [VoiceInfo] = []
    @Published private(set) var isLoading = false
    
    private var voiceCache: [String: [VoiceInfo]] = [:]
    private let cacheQueue = DispatchQueue(label: "com.runapp.voicemanager", qos: .utility)
    
    // Keep a strong reference to the current voice test delegate
    private var currentVoiceTestDelegate: VoiceTestDelegate?
    
    private init() {
        loadAllVoices()
        setupVoiceChangeNotifications()
    }
    
    // MARK: - Public Interface
    
    /// Get available voices for a specific language
    func getVoicesForLanguage(_ languageCode: String) -> [VoiceInfo] {
        let systemLanguageCode = mapToSystemLanguageCode(languageCode)
        let allVoices = voiceCache[systemLanguageCode] ?? []
        
        // Return all voice types (standard, enhanced, and premium)
        return allVoices
    }
    
    /// Get the best available voice for a language (Enhanced/Premium preferred)
    func getBestVoiceForLanguage(_ languageCode: String) -> VoiceInfo? {
        let voices = getVoicesForLanguage(languageCode)
        
        // Sort by quality (Premium > Enhanced) and then by name
        return voices.sorted { voice1, voice2 in
            if voice1.quality.priority != voice2.quality.priority {
                return voice1.quality.priority > voice2.quality.priority
            }
            return voice1.name < voice2.name
        }.first
    }
    
    /// Get voice by identifier
    func getVoice(by identifier: String) -> VoiceInfo? {
        for voices in voiceCache.values {
            if let voice = voices.first(where: { $0.id == identifier }) {
                return voice
            }
        }
        return nil
    }
    
    /// Check if a voice is currently available on the system
    func isVoiceAvailable(_ identifier: String) -> Bool {
        return getVoice(by: identifier) != nil
    }
    
    /// Refresh voice cache (useful after voice downloads or system changes)
    func refreshVoices() {
        Task {
            await loadAllVoicesAsync()
        }
    }
    
    /// Delete a voice from the system
    /// - Parameter voiceInfo: The voice to delete
    /// - Returns: True if deletion was successful, false otherwise
    func deleteVoice(_ voiceInfo: VoiceInfo) async -> Bool {
        guard voiceInfo.isDeletable else {
            print("VoiceManager: Cannot delete voice \(voiceInfo.name) - it is not deletable")
            return false
        }
        
        print("VoiceManager: Attempting to delete voice: \(voiceInfo.name) (\(voiceInfo.id))")
        
        // Note: iOS doesn't provide a direct API to delete downloaded voices programmatically
        // The system manages voice downloads and deletions through Settings app
        // For now, we'll simulate the deletion by removing it from our cache
        // and advising users to manage voices through Settings
        
        // Remove from cache
        await removeVoiceFromCache(voiceInfo)
        
        // In a real implementation, you would need to:
        // 1. Use private APIs (not recommended for App Store)
        // 2. Direct users to Settings app for voice management
        // 3. Or use system-provided voice management if available
        
        print("VoiceManager: Voice \(voiceInfo.name) removed from cache")
        return true
    }
    
    /// Remove a voice from the local cache
    private func removeVoiceFromCache(_ voiceInfo: VoiceInfo) async {
        await MainActor.run {
            // Remove from main available voices array
            availableVoices.removeAll { $0.id == voiceInfo.id }
            
            // Remove from language-specific cache
            for (language, voices) in voiceCache {
                voiceCache[language] = voices.filter { $0.id != voiceInfo.id }
            }
            
            print("VoiceManager: Removed \(voiceInfo.name) from voice cache")
        }
    }
    
    // MARK: - Private Implementation
    
    private func loadAllVoices() {
        isLoading = true
        
        Task {
            await loadAllVoicesAsync()
            
            await MainActor.run {
                isLoading = false
            }
        }
    }
    
    private func loadAllVoicesAsync() async {
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let systemVoices = AVSpeechSynthesisVoice.speechVoices()
                var newCache: [String: [VoiceInfo]] = [:]
                var allVoices: [VoiceInfo] = []
                
                for systemVoice in systemVoices {
                    let voiceInfo = createVoiceInfoSync(from: systemVoice)
                    allVoices.append(voiceInfo)
                    
                    // Group by language
                    if newCache[systemVoice.language] == nil {
                        newCache[systemVoice.language] = []
                    }
                    newCache[systemVoice.language]?.append(voiceInfo)
                }
                
                // Sort voices within each language by quality and name
                for language in newCache.keys {
                    newCache[language]?.sort { voice1, voice2 in
                        if voice1.quality.priority != voice2.quality.priority {
                            return voice1.quality.priority > voice2.quality.priority
                        }
                        return voice1.name < voice2.name
                    }
                }
                
                Task { @MainActor in
                    self.voiceCache = newCache
                    self.availableVoices = allVoices.sorted { $0.name < $1.name }
                    print("VoiceManager: Loaded \(allVoices.count) voices for \(newCache.keys.count) languages")
                }
                
                continuation.resume()
            }
        }
    }
    
    private func createVoiceInfo(from systemVoice: AVSpeechSynthesisVoice) -> VoiceInfo {
        let quality = determineVoiceQuality(systemVoice)
        let isInstalled = determineInstallationStatus(systemVoice)
        let isDeletable = determineIfVoiceIsDeletable(systemVoice)
        
        return VoiceInfo(
            id: systemVoice.identifier,
            name: systemVoice.name,
            language: systemVoice.language,
            quality: quality,
            isInstalled: isInstalled,
            isDeletable: isDeletable,
            voice: systemVoice
        )
    }
    
    // Non-isolated version for background processing
    private nonisolated func createVoiceInfoSync(from systemVoice: AVSpeechSynthesisVoice) -> VoiceInfo {
        let quality = determineVoiceQualitySync(systemVoice)
        let isInstalled = true // All voices returned by speechVoices() are available
        let isDeletable = determineIfVoiceIsDeletableSync(systemVoice)
        
        return VoiceInfo(
            id: systemVoice.identifier,
            name: systemVoice.name,
            language: systemVoice.language,
            quality: quality,
            isInstalled: isInstalled,
            isDeletable: isDeletable,
            voice: systemVoice
        )
    }
    
    private func determineVoiceQuality(_ voice: AVSpeechSynthesisVoice) -> VoiceQuality {
        let name = voice.name.lowercased()
        let identifier = voice.identifier.lowercased()
        
        // Enhanced and Premium voices typically have specific patterns in their names or identifiers
        if name.contains("premium") || identifier.contains("premium") {
            return .premium
        } else if name.contains("enhanced") || identifier.contains("enhanced") || 
                  name.contains("compact") || identifier.contains("compact") {
            return .enhanced
        } else {
            return .standard
        }
    }
    
    // Non-isolated version for background processing
    private nonisolated func determineVoiceQualitySync(_ voice: AVSpeechSynthesisVoice) -> VoiceQuality {
        let name = voice.name.lowercased()
        let identifier = voice.identifier.lowercased()
        
        // Enhanced and Premium voices typically have specific patterns in their names or identifiers
        if name.contains("premium") || identifier.contains("premium") {
            return .premium
        } else if name.contains("enhanced") || identifier.contains("enhanced") || 
                  name.contains("compact") || identifier.contains("compact") {
            return .enhanced
        } else {
            return .standard
        }
    }
    
    private func determineInstallationStatus(_ voice: AVSpeechSynthesisVoice) -> Bool {
        // In iOS, all voices returned by speechVoices() are available for use
        // Enhanced/Premium voices might be downloaded on-demand, but this is handled by the system
        return true
    }
    
    /// Determine if a voice can be deleted (main actor version)
    private func determineIfVoiceIsDeletable(_ voice: AVSpeechSynthesisVoice) -> Bool {
        return determineIfVoiceIsDeletableSync(voice)
    }
    
    /// Determine if a voice can be deleted (non-isolated version)
    private nonisolated func determineIfVoiceIsDeletableSync(_ voice: AVSpeechSynthesisVoice) -> Bool {
        let identifier = voice.identifier.lowercased()
        let name = voice.name.lowercased()
        
        // Default system voices (built-in voices) cannot be deleted
        // These are typically the basic voices that come with iOS
        let defaultVoicePatterns = [
            "com.apple.ttsbundle.siri",
            "com.apple.speech.synthesis.voice",
            ".default",
            ".system"
        ]
        
        // Check if this is a default/system voice
        for pattern in defaultVoicePatterns {
            if identifier.contains(pattern) {
                return false
            }
        }
        
        // Standard quality voices are typically built-in and cannot be deleted
        let quality = determineVoiceQualitySync(voice)
        if quality == .standard {
            return false
        }
        
        // Enhanced and Premium voices are typically downloadable and can be deleted
        // But some enhanced voices might also be built-in, so we need additional checks
        
        // Known deletable voice patterns (downloadable voices)
        let deletablePatterns = [
            "compact",
            "premium",
            "neural",
            "enhanced"
        ]
        
        for pattern in deletablePatterns {
            if identifier.contains(pattern) || name.contains(pattern) {
                return true
            }
        }
        
        // Default to non-deletable for safety
        return false
    }
    
    private func mapToSystemLanguageCode(_ appLanguageCode: String) -> String {
        // Map app language codes to system voice language codes
        switch appLanguageCode {
        case "ar": return "ar-SA"
        case "zh-Hans": return "zh-CN"
        case "zh-Hant": return "zh-TW"
        case "de": return "de-DE"
        case "en": return "en-US"
        case "fr": return "fr-FR"
        case "id": return "id-ID"
        case "it": return "it-IT"
        case "ja": return "ja-JP"
        case "ko": return "ko-KR"
        case "pt": return "pt-BR"
        case "ru": return "ru-RU"
        case "es": return "es-ES"
        default: return "en-US"
        }
    }
    
    private func setupVoiceChangeNotifications() {
        // Note: AVSpeechSynthesizer doesn't provide voice change notifications
        // Voice changes are rare and typically occur only when user downloads new voices
        // We rely on manual refresh calls when needed
        print("VoiceManager: Voice change notifications not available, using manual refresh")
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Debug and Testing

extension VoiceManager {
    /// Get debug information about available voices
    func getDebugInfo() -> String {
        var info = "VoiceManager Debug Info:\n"
        info += "Total languages: \(voiceCache.keys.count)\n"
        info += "Total voices: \(availableVoices.count)\n\n"
        
        for (language, voices) in voiceCache.sorted(by: { $0.key < $1.key }) {
            info += "\(language): \(voices.count) voices\n"
            for voice in voices {
                info += "  - \(voice.name) (\(voice.quality.rawValue))\n"
            }
            info += "\n"
        }
        
        return info
    }
    
    /// Test voice functionality with a sample text
    func testVoice(_ voiceInfo: VoiceInfo, completion: @escaping (Bool) -> Void) {
        print("VoiceManager: testVoice called for \(voiceInfo.name)")
        
        // Stop any existing voice test
        if let currentDelegate = currentVoiceTestDelegate {
            currentDelegate.synthesizer.stopSpeaking(at: .immediate)
        }
        currentVoiceTestDelegate = nil
        
        // Configure audio session for voice testing
        do {
            let audioSession = AVAudioSession.sharedInstance()
            
            // Try different audio session configurations
            print("VoiceManager: Current audio session category: \(audioSession.category)")
            print("VoiceManager: Current audio session mode: \(audioSession.mode)")
            print("VoiceManager: Current audio session options: \(audioSession.categoryOptions)")
            
            // Use .playAndRecord category which is more suitable for speech synthesis
            try audioSession.setCategory(.playAndRecord, mode: .spokenAudio, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
            
            print("VoiceManager: Audio session configured successfully")
            print("VoiceManager: New audio session category: \(audioSession.category)")
            print("VoiceManager: New audio session mode: \(audioSession.mode)")
            print("VoiceManager: New audio session options: \(audioSession.categoryOptions)")
        } catch {
            print("VoiceManager: Failed to configure audio session for voice test: \(error)")
            print("VoiceManager: Error details: \(error.localizedDescription)")
            completion(false)
            return
        }
        
        let synthesizer = AVSpeechSynthesizer()
        let testText = "voice.manager.test.text".localized
        let utterance = AVSpeechUtterance(string: testText)
        utterance.voice = voiceInfo.voice
        // Use normal speech rate for voice testing, not user's configured rate
        utterance.rate = AVSpeechUtteranceDefaultSpeechRate
        utterance.volume = 0.8
        
        print("VoiceManager: Created utterance with text: '\(testText)'")
        print("VoiceManager: Using voice: \(voiceInfo.voice.name) (\(voiceInfo.voice.identifier))")
        print("VoiceManager: Voice language: \(voiceInfo.voice.language)")
        print("VoiceManager: Utterance rate: \(utterance.rate), volume: \(utterance.volume)")
        
        // Create delegate with proper lifecycle management
        let delegate = VoiceTestDelegate(synthesizer: synthesizer) { [weak self] success in
            print("VoiceManager: VoiceTestDelegate callback with success: \(success)")
            
            // Clean up the delegate reference
            DispatchQueue.main.async {
                self?.currentVoiceTestDelegate = nil
            }
            
            completion(success)
        }
        
        // Keep a strong reference to prevent deallocation
        currentVoiceTestDelegate = delegate
        synthesizer.delegate = delegate
        
        // Add a timeout mechanism
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
            if self.currentVoiceTestDelegate === delegate {
                print("VoiceManager: Voice test timed out after 10 seconds")
                synthesizer.stopSpeaking(at: .immediate)
                self.currentVoiceTestDelegate = nil
                completion(false)
            }
        }
        
        print("VoiceManager: About to call synthesizer.speak()")
        synthesizer.speak(utterance)
        print("VoiceManager: synthesizer.speak() called")
    }
}

// MARK: - Voice Test Delegate

private class VoiceTestDelegate: NSObject, AVSpeechSynthesizerDelegate {
    private let completion: (Bool) -> Void
    nonisolated(unsafe) let synthesizer: AVSpeechSynthesizer // Use nonisolated(unsafe) to bypass Sendable requirement
    
    init(synthesizer: AVSpeechSynthesizer, completion: @escaping (Bool) -> Void) {
        self.synthesizer = synthesizer
        self.completion = completion
        super.init()
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        print("VoiceManager: Voice test started successfully")
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        print("VoiceManager: Voice test completed successfully")
        completion(true)
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        print("VoiceManager: Voice test was cancelled")
        completion(false)
    }
}
