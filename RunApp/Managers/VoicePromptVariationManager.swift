import Foundation

/// Manages variations of voice prompts to avoid repetition and keep users engaged
@MainActor
class VoicePromptVariationManager: ObservableObject {
    static let shared = VoicePromptVariationManager()
    
    // Track recently used variations per metric type
    private var recentlyUsed: [String: [Int]] = [:]
    private let maxRecentTracking = 5  // Track last 5 used variations
    private let totalVariations = 10   // Total variations available per metric
    
    private init() {
        resetSession()
    }
    
    /// Get random variation avoiding recent ones to prevent repetition
    /// - Parameter metricType: The type of metric ("distance", "time", "calories", "pace")
    /// - Returns: Variation number (1-10)
    func getRandomVariation(for metricType: String) -> Int {
        let recent = recentlyUsed[metricType] ?? []
        let available = Array(1...totalVariations).filter { !recent.contains($0) }
        
        // If all variations used recently, reset for this metric
        if available.isEmpty {
            recentlyUsed[metricType] = []
            let selectedVariation = Int.random(in: 1...totalVariations)
            trackUsage(metricType: metricType, variation: selectedVariation)
            return selectedVariation
        }
        
        let selectedVariation = available.randomElement() ?? 1
        trackUsage(metricType: metricType, variation: selectedVariation)
        return selectedVariation
    }
    
    /// Track usage of a variation to avoid immediate repetition
    private func trackUsage(metricType: String, variation: Int) {
        if recentlyUsed[metricType] == nil {
            recentlyUsed[metricType] = []
        }
        
        recentlyUsed[metricType]?.append(variation)
        
        // Keep only last N variations to prevent memory growth
        if let recent = recentlyUsed[metricType], recent.count > maxRecentTracking {
            recentlyUsed[metricType] = Array(recent.suffix(maxRecentTracking))
        }
    }
    
    /// Reset variation tracking for new workout session
    func resetSession() {
        recentlyUsed = [
            "distance": [],
            "time": [],
            "calories": [],
            "pace": []
        ]
        print("VoicePromptVariationManager: Session reset - variation tracking cleared")
    }
    
    /// Get debug info about current variation usage
    func getDebugInfo() -> String {
        var info = "VoicePromptVariationManager Debug:\n"
        for (metric, variations) in recentlyUsed {
            info += "\(metric): recently used \(variations)\n"
        }
        return info
    }
}
