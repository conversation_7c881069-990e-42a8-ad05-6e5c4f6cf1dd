import Foundation
import AVFoundation
import CoreHaptics
import SwiftUI
import AudioToolbox
import UIKit

@MainActor
class MetronomeManager: ObservableObject {
    static let shared = MetronomeManager()

    private var hapticEngine: CHHapticEngine?
    private var audioEngine: AVAudioEngine?
    private var playerNode: AVAudioPlayerNode?
    private var volumeMixerNode: AVAudioMixerNode?
    private var singleClickBuffer: AVAudioPCMBuffer?
    private var metronomeLoopBuffer: AVAudioPCMBuffer?
    private var previewAudioPlayer: AVAudioPlayer?

    private var displayLink: CADisplayLink? // For precise UI and haptic timing
    private var lastHapticBeatIndex: Int = -1
    private var isTapInstalled = false

    @Published private(set) var isEnabled = true
    @Published private(set) var isPlaying = false
    @Published private(set) var isOnBeat = false
    @Published private(set) var showingMessage = false
    private var messageTimer: Timer?
    private var isInBackground = false
    private var isHibernating = false

    // Metronome settings
    private var bpm: Int = 180
    private var soundEnabled = true
    private var vibrationEnabled = false
    private var activityInProgress = false
    private var currentSoundID: Int = SoundType.beat1Wav
    private var alertFrequency: MetronomeAlertFrequency = .everyOtherBeat

    // Sound type constants
    private struct SoundType {
        static let beat1Wav = 2003
        static let beat2Wav = 2004
        static let beat3Wav = 2005
        static let beat4Wav = 2006
        static let beat5Wav = 2007
        static let beat6Wav = 2008

        static func isValid(_ id: Int) -> Bool {
            return [beat1Wav, beat2Wav, beat3Wav, beat4Wav, beat5Wav, beat6Wav].contains(id)
        }

        static func isSystemSound(_ id: Int) -> Bool {
            return false
        }

        static func isWavSound(_ id: Int) -> Bool {
            return isValid(id)
        }

        static func wavBaseFileName(for id: Int) -> String? {
            switch id {
            case beat1Wav: return "Tink"
            case beat2Wav: return "beat1"
            case beat3Wav: return "beat2"
            case beat4Wav: return "beat3"
            case beat5Wav: return "Tock"
            case beat6Wav: return "sq_tock"
            default: return nil
            }
        }
    }

    private init() {
        print("MetronomeManager: Initializing...")
        print("[MetronomeManager] init: Default self.bpm = \(self.bpm)")
        setupHapticEngine()
        setupAudioEngine()
        setupAudioSession()
        setupScenePhaseObserver()
        setupBackgroundTaskHandling()
        setupDisplayLink()
    }

    private func setupDisplayLink() {
        displayLink = CADisplayLink(target: self, selector: #selector(updateMetronomeStateFromEngine))
        displayLink?.preferredFramesPerSecond = 20 
        displayLink?.add(to: .main, forMode: .common)
        displayLink?.isPaused = true
    }

    @objc private func updateMetronomeStateFromEngine() {
        guard isPlaying, !isInBackground, // Ensure this only runs effectively for UI in foreground
              let engine = audioEngine, engine.isRunning,
              let playerNode = playerNode, playerNode.isPlaying, // playerNode might be playing silent buffer
              let loopBuffer = metronomeLoopBuffer, loopBuffer.frameLength > 0, // loopBuffer could be silent
              let outputFormat = engine.mainMixerNode.outputFormat(forBus: 0) as AVAudioFormat?,
              let nodeTime = playerNode.lastRenderTime,
              let playerTime = playerNode.playerTime(forNodeTime: nodeTime) else {
            if isOnBeat { isOnBeat = false }
            return
        }

        let samplesPerBeat = AVAudioFramePosition(outputFormat.sampleRate * 60.0 / Double(bpm))
        guard samplesPerBeat > 0 else {
            if isOnBeat { isOnBeat = false }
            return
        }

        let currentFrameInLoop = playerTime.sampleTime % AVAudioFramePosition(loopBuffer.frameLength)
        let currentBeatInLoop = Int(currentFrameInLoop / samplesPerBeat)

        var shouldSignalThisBeat = false
        switch alertFrequency {
        case .everyBeat:
            shouldSignalThisBeat = true
        case .everyOtherBeat:
            shouldSignalThisBeat = (currentBeatInLoop % 2 == 0)
        case .every4thBeat:
            shouldSignalThisBeat = (currentBeatInLoop % 4 == 0)
        case .every6thBeat:
            shouldSignalThisBeat = (currentBeatInLoop % 6 == 0)
        @unknown default:
            print("MetronomeManager: updateMetronomeStateFromEngine - Unhandled alertFrequency \(alertFrequency). Defaulting to signal on beat 0 of loop.")
            shouldSignalThisBeat = (currentBeatInLoop == 0)
        }
       
        let effectiveBeatForHapticAndUI = currentBeatInLoop

        if shouldSignalThisBeat {
            if effectiveBeatForHapticAndUI != lastHapticBeatIndex {
                // System sound playback removed, as all sounds are WAVs played by the engine.
                if vibrationEnabled { // Haptic check for foreground via DisplayLink
                    createHapticEvent()
                }
                // UI updates only in foreground (already guarded by !isInBackground for the whole function execution)
                self.isOnBeat = true
                Task { @MainActor in
                    try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                    if self.isOnBeat {
                       self.isOnBeat = false
                    }
                }
                lastHapticBeatIndex = effectiveBeatForHapticAndUI
            }
        } else {
            if effectiveBeatForHapticAndUI != lastHapticBeatIndex && self.isOnBeat {
                 // Let the async task handle turning off isOnBeat
            }
        }
    }

    // MARK: - Audio Session and Engine Setup

    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(
                .playback,
                mode: .default,
                options: [.mixWithOthers]
            )
            try audioSession.setActive(true)

            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleAudioSessionInterruption),
                name: AVAudioSession.interruptionNotification,
                object: audioSession
            )

            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleAudioRouteChange),
                name: AVAudioSession.routeChangeNotification,
                object: audioSession
            )
        } catch {
            print("MetronomeManager: Failed to setup audio session: \(error)")
        }
    }

    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()
        playerNode = AVAudioPlayerNode()
        volumeMixerNode = AVAudioMixerNode()

        if let engine = audioEngine, let player = playerNode, let volumeMixer = volumeMixerNode {
            engine.attach(player)
            engine.attach(volumeMixer)
            
            // Connect playerNode -> volumeMixerNode -> mainMixerNode
            let mainMixerNode = engine.mainMixerNode
            let mixerOutputFormat = mainMixerNode.outputFormat(forBus: 0)
            
            // Connect player to volume mixer, then volume mixer to main mixer
            engine.connect(player, to: volumeMixer, format: mixerOutputFormat)
            engine.connect(volumeMixer, to: mainMixerNode, format: mixerOutputFormat)

            do {
                try engine.start()
            } catch {
                print("MetronomeManager: Failed to start audio engine: \(error)")
            }
        }
    }

    private func prepareWavSound(soundID: Int) {
        guard SoundType.isWavSound(soundID), let baseFileName = SoundType.wavBaseFileName(for: soundID) else {
            self.singleClickBuffer = nil
            return
        }

        if let soundFileURL = Bundle.main.url(forResource: baseFileName, withExtension: "wav") {
            do {
                let audioFile = try AVAudioFile(forReading: soundFileURL)
                let buffer = AVAudioPCMBuffer(pcmFormat: audioFile.processingFormat,
                                              frameCapacity: AVAudioFrameCount(audioFile.length))
                try audioFile.read(into: buffer!)
                self.singleClickBuffer = buffer
            } catch {
                print("MetronomeManager: Failed to load sound file \(baseFileName).wav: \(error)")
                self.singleClickBuffer = nil
            }
        } else {
            print("MetronomeManager: Sound file \(baseFileName).wav not found in bundle.")
            self.singleClickBuffer = nil
        }
    }

    @objc private func handleAudioSessionInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }

        switch type {
        case .began:
            if isPlaying {
                // Don't call stopPlayback directly as it deactivates session.
                // Pause the displayLink and playerNode.
                displayLink?.isPaused = true
                playerNode?.pause() // Pause instead of stop to preserve schedule
                // isPlaying should remain true if we intend to resume.
                print("MetronomeManager: Audio session interrupted, player paused.")
            }
        case .ended:
            guard let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt else { return }
            let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)

            if options.contains(.shouldResume) {
                Task {
                    do {
                        try AVAudioSession.sharedInstance().setActive(true)
                        if isEnabled && activityInProgress {
                             print("MetronomeManager: Audio session interruption ended - attempting to resume playback.")
                             if audioEngine?.isRunning == false { try audioEngine?.start() }
                             startPlayback()
                        }
                    } catch {
                        print("MetronomeManager: Failed to reactivate audio session: \(error)")
                    }
                }
            }
        @unknown default:
            break
        }
    }

    @objc private func handleAudioRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }

        switch reason {
        case .oldDeviceUnavailable:
            if isPlaying {
                print("MetronomeManager: Audio route changed (old device unavailable) - stopping and restarting.")
                stopPlayback(temporary: true)
                Task { @MainActor in
                    try? await Task.sleep(nanoseconds: 500_000_000)
                    if self.isEnabled && self.activityInProgress {
                        self.startPlayback()
                    }
                }
            }
        default:
            break
        }
    }

    // MARK: - Haptic Engine Setup
    private func setupHapticEngine() {
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else { return }
        do {
            hapticEngine = try CHHapticEngine()
            try hapticEngine?.start()
            hapticEngine?.resetHandler = { [weak self] in
                do { try self?.hapticEngine?.start() } catch { print("MetronomeManager: Error restarting haptic engine: \(error)") }
            }
            hapticEngine?.stoppedHandler = { reason in print("MetronomeManager: Haptic engine stopped: \(reason)") }
        } catch { print("MetronomeManager: Error creating haptic engine: \(error)") }
    }

    // MARK: - Scene Phase Observers
    private func setupScenePhaseObserver() {
        NotificationCenter.default.addObserver(self, selector: #selector(handleScenePhaseChange), name: UIScene.didActivateNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleSceneDeactivation), name: UIScene.willDeactivateNotification, object: nil)
    }

    @objc private func handleScenePhaseChange() { // App became active
        Task { @MainActor in
            if hapticEngine == nil { setupHapticEngine() }
            if audioEngine?.isRunning == false { setupAudioEngine() }
            
            if isPlaying {
                stopPlayback(temporary: true)
                startPlayback()
            }
        }
    }

    @objc private func handleSceneDeactivation() {
        // Let system handle haptic engine lifecycle
    }

    // MARK: - Haptic & Vibration Methods
    private func createHapticEvent() {
        AudioServicesPlaySystemSound(1520) // Standard "tock" vibration
    }

    private func stopHaptics() {
        // For AudioServicesPlaySystemSound, there's no explicit stop for a single event.
    }

    // MARK: - Beat & Audio Engine Methods

    private func generateMetronomeLoopBuffer(bpm: Double, alertFrequency: MetronomeAlertFrequency) -> AVAudioPCMBuffer? {
        guard let engine = audioEngine,
              let outputFormat = engine.mainMixerNode.outputFormat(forBus: 0) as AVAudioFormat?
        else {
            print("MetronomeManager: Missing audio engine or output format for loop generation.")
            return nil
        }

        // Determine if we should use the loaded WAV sound from singleClickBuffer
        let shouldUseWavSound = self.soundEnabled && SoundType.isValid(self.currentSoundID) && self.singleClickBuffer != nil
        var clickBufferToUse: AVAudioPCMBuffer? = nil

        if shouldUseWavSound {
            // Ensure singleClickBuffer is in the correct format for the engine
            if let sfxBuffer = self.singleClickBuffer, let sfxFormat = sfxBuffer.format as AVAudioFormat? {
                if sfxFormat.sampleRate != outputFormat.sampleRate || sfxFormat.channelCount != outputFormat.channelCount {
                    print("MetronomeManager: WAV singleClickBuffer format (\(sfxFormat)) does not match engine output format (\(outputFormat)). Attempting conversion.")
                    var successfullyConvertedSfx: AVAudioPCMBuffer? = nil
                    if let converter = AVAudioConverter(from: sfxFormat, to: outputFormat) {
                        if let tempConvertedBuffer = AVAudioPCMBuffer(pcmFormat: outputFormat,
                                                                     frameCapacity: AVAudioFrameCount(Double(sfxBuffer.frameLength) * outputFormat.sampleRate / sfxFormat.sampleRate)) {
                            var error: NSError?
                            let inputBlock: AVAudioConverterInputBlock = { inNumPackets, outStatus in
                                outStatus.pointee = .haveData
                                return sfxBuffer
                            }
                            converter.convert(to: tempConvertedBuffer, error: &error, withInputFrom: inputBlock)
                            if let error = error {
                                print("MetronomeManager: Error converting WAV singleClickBuffer: \(error)")
                            } else {
                                successfullyConvertedSfx = tempConvertedBuffer
                                print("MetronomeManager: Successfully converted WAV singleClickBuffer to engine output format.")
                            }
                        } else {
                            print("MetronomeManager: Could not create AVAudioPCMBuffer for WAV single click for conversion.")
                        }
                    } else {
                        print("MetronomeManager: Could not create AVAudioConverter for WAV singleClickBuffer.")
                    }
                    clickBufferToUse = successfullyConvertedSfx
                } else {
                    clickBufferToUse = sfxBuffer
                }
            } else {
                 clickBufferToUse = nil
            }
        }

        let beatsInLoop = 12
        let samplesPerBeatAsCount: AVAudioFrameCount = AVAudioFrameCount(outputFormat.sampleRate * 60.0 / bpm)
        let loopFrameLength = AVAudioFrameCount(beatsInLoop) * samplesPerBeatAsCount

        guard loopFrameLength > 0 else {
            print("MetronomeManager: Calculated loop frame length is zero or negative.")
            return nil
        }

        guard let loopBuffer = AVAudioPCMBuffer(pcmFormat: outputFormat, frameCapacity: loopFrameLength) else {
            print("MetronomeManager: Could not create metronomeLoopBuffer with output format.")
            return nil
        }
        loopBuffer.frameLength = loopFrameLength
        let channelCount = Int(outputFormat.channelCount)

        let soundSourceBuffer = clickBufferToUse

        for i in 0..<beatsInLoop {
            let currentBeatInLoop = i % beatsInLoop
            var shouldPlaySoundThisBeatInLoop = false

            if soundSourceBuffer != nil {
                switch alertFrequency {
                case .everyBeat:
                    shouldPlaySoundThisBeatInLoop = true
                case .everyOtherBeat:
                    shouldPlaySoundThisBeatInLoop = (currentBeatInLoop % 2 == 0)
                case .every4thBeat:
                    shouldPlaySoundThisBeatInLoop = (currentBeatInLoop % 4 == 0)
                case .every6thBeat:
                    shouldPlaySoundThisBeatInLoop = (currentBeatInLoop % 6 == 0)
                @unknown default:
                    print("MetronomeManager: generateMetronomeLoopBuffer (WAV) - Unhandled alertFrequency \(alertFrequency). Defaulting to sound on beat 0 of loop.")
                    shouldPlaySoundThisBeatInLoop = (currentBeatInLoop == 0)
                }
            }

            if shouldPlaySoundThisBeatInLoop, let srcBuffer = soundSourceBuffer, srcBuffer.frameLength > 0, let srcFloatDataPointers = srcBuffer.floatChannelData {
                for channel in 0..<channelCount {
                    let currentSrcChannelData = srcFloatDataPointers[channel] 
                    guard let destChannelData = loopBuffer.floatChannelData?[channel] else { continue } 
                    
                    let destStartSample = AVAudioFramePosition(i * Int(samplesPerBeatAsCount))
                    let samplesToCopy: AVAudioFrameCount = min(samplesPerBeatAsCount, srcBuffer.frameLength)

                    if AVAudioFramePosition(loopBuffer.frameLength) >= (destStartSample + AVAudioFramePosition(samplesToCopy)) && samplesToCopy > 0 {
                        memcpy(destChannelData.advanced(by: Int(destStartSample)),
                               currentSrcChannelData,
                               Int(samplesToCopy) * MemoryLayout<Float>.size)
                    }
                }
            } else {
                for channel in 0..<channelCount {
                    guard let destChannelData = loopBuffer.floatChannelData?[channel] else { continue }
                    let destStartSample = AVAudioFramePosition(i * Int(samplesPerBeatAsCount))
                    if AVAudioFramePosition(loopBuffer.frameLength) >= (destStartSample + AVAudioFramePosition(samplesPerBeatAsCount)) {
                         memset(destChannelData.advanced(by: Int(destStartSample)), 0, Int(samplesPerBeatAsCount) * MemoryLayout<Float>.size)
                    }
                }
            }
        }
        return loopBuffer
    }

    private func setupBackgroundTaskHandling() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppTermination),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
        
        // Add hibernation observers
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleHibernation),
            name: .appShouldHibernate,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWakeUp),
            name: .appShouldWakeUp,
            object: nil
        )
    }

    @objc private func handleAppBackground() {
        isInBackground = true
        if isPlaying {
            displayLink?.isPaused = true // Pause UI updates and foreground haptics via DisplayLink
            removeAudioTap() // Ensure no old tap exists
            
            // OPTIMIZATION: Only install audio tap if vibration is enabled
            if vibrationEnabled {
                installAudioTap() // Install tap for background haptics
                print("MetronomeManager: App entered background. DisplayLink paused, audio tap installed for haptics.")
            } else {
                print("MetronomeManager: App entered background. DisplayLink paused, audio tap skipped (vibration disabled).")
            }
        } else {
            print("MetronomeManager: App entered background, not playing.")
        }
    }

    @objc private func handleAppForeground() {
        isInBackground = false
        removeAudioTap()

        Task { @MainActor in
            if isPlaying {
                print("MetronomeManager: App entered foreground, was playing. Reconfiguring for foreground playback.")
                displayLink?.isPaused = false // Resume DisplayLink for foreground UI/haptics

                let currentBPM = self.bpm
                let currentSoundEnabled = self.soundEnabled
                let currentVibrationEnabled = self.vibrationEnabled
                let currentSoundType = self.currentSoundID
                let currentAlertFreq = self.alertFrequency

                stopPlayback(temporary: true)
                
                self.updateSettings(bpm: currentBPM, soundEnabled: currentSoundEnabled, vibrationEnabled: currentVibrationEnabled, soundType: currentSoundType, alertFrequency: currentAlertFreq)
                
                if self.isEnabled && self.activityInProgress {
                    startPlayback()
                }

            } else {
                if !activityInProgress {
                    do {
                        try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                         print("MetronomeManager: App entered foreground, was not playing. Deactivated audio session.")
                    } catch { print("MetronomeManager: Failed to deactivate audio session on foreground (no activity): \(error)") }
                } else {
                    print("MetronomeManager: App entered foreground, was not playing but activity in progress.")
                }
            }
        }
    }

    @objc private func handleAppTermination() {
        stopPlayback()
    }

    // MARK: - Hibernation Handlers
    
    @objc private func handleHibernation() {
        // Only hibernate if NOT in activity (workout mode)
        guard !activityInProgress else {
            print("MetronomeManager: Hibernation request ignored - activity in progress")
            return
        }
        
        isHibernating = true
        print("MetronomeManager: Entering hibernation mode")
        
        // Deep sleep: Completely stop all metronome operations
        if isPlaying {
            stopPlayback()
        }
        
        // Tear down audio engine to save maximum battery
        if let engine = audioEngine, engine.isRunning {
            engine.stop()
            print("MetronomeManager: Audio engine stopped for hibernation")
        }
        
        // Pause displayLink completely
        displayLink?.isPaused = true
        
        // Release haptic engine resources
        hapticEngine?.stop()
        hapticEngine = nil
        
        // Clear audio buffers to free memory
        singleClickBuffer = nil
        metronomeLoopBuffer = nil
        
        // Stop any preview audio
        previewAudioPlayer?.stop()
        previewAudioPlayer = nil
        
        // Cancel message timer
        messageTimer?.invalidate()
        messageTimer = nil
        showingMessage = false
        
        // Remove audio session observers to reduce overhead
        NotificationCenter.default.removeObserver(
            self,
            name: AVAudioSession.interruptionNotification,
            object: AVAudioSession.sharedInstance()
        )
        NotificationCenter.default.removeObserver(
            self,
            name: AVAudioSession.routeChangeNotification,
            object: AVAudioSession.sharedInstance()
        )
        
        // Deactivate audio session
        Task { @MainActor in
            do {
                try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                print("MetronomeManager: Audio session deactivated for hibernation")
            } catch {
                print("MetronomeManager: Failed to deactivate audio session during hibernation: \(error)")
            }
        }
    }
    
    @objc private func handleWakeUp() {
        guard isHibernating else { return }
        
        isHibernating = false
        print("MetronomeManager: Waking up from hibernation")
        
        // Restore haptic engine
        setupHapticEngine()
        
        // Restore audio engine and session
        setupAudioEngine()
        setupAudioSession()
        
        // Reset state flags
        isOnBeat = false
        lastHapticBeatIndex = -1
        
        print("MetronomeManager: Hibernation wake-up complete")
    }

    // MARK: - Audio Tap for Background Haptics

    private func installAudioTap() {
        // OPTIMIZATION: Only install if vibration is actually enabled
        guard let playerNode = self.playerNode, let engine = self.audioEngine, engine.isRunning,
              self.isPlaying, self.isInBackground, !self.isTapInstalled,
              self.vibrationEnabled else { // Added vibrationEnabled check
            print("MetronomeManager: Skipping audio tap installation - vibration disabled or conditions not met")
            return
        }

        // Ensure metronomeLoopBuffer is valid, even if silent, for timing.
        guard let mainLoopBuffer = self.metronomeLoopBuffer, mainLoopBuffer.frameLength > 0,
              let outputFormat = engine.mainMixerNode.outputFormat(forBus: 0) as AVAudioFormat? else {
            print("MetronomeManager: Cannot install audio tap, missing main metronome loop buffer or output format.")
            return
        }

        let tapBufferSize: AVAudioFrameCount = 1024 

        playerNode.installTap(onBus: 0, bufferSize: tapBufferSize, format: playerNode.outputFormat(forBus: 0)) {
            [weak self] (tapProvidedBuffer, audioTimeWhen) in
            guard let self = self, self.isPlaying, self.isInBackground,
                  let playerNode = self.playerNode,
                  let currentLoopBuffer = self.metronomeLoopBuffer, currentLoopBuffer.frameLength > 0 else {
                return
            }

            guard let nodeTime = playerNode.lastRenderTime,
                  let playerTimelineTime = playerNode.playerTime(forNodeTime: nodeTime) else {
                return
            }

            let samplesPerBeat = AVAudioFramePosition(outputFormat.sampleRate * 60.0 / Double(self.bpm))
            guard samplesPerBeat > 0 else { return }

            let currentFrameInLoop = playerTimelineTime.sampleTime % AVAudioFramePosition(currentLoopBuffer.frameLength)
            let currentBeatInLoop = Int(currentFrameInLoop / samplesPerBeat)

            var shouldSignalThisBeat = false
            switch self.alertFrequency {
            case .everyBeat: shouldSignalThisBeat = true
            case .everyOtherBeat: shouldSignalThisBeat = (currentBeatInLoop % 2 == 0)
            case .every4thBeat: shouldSignalThisBeat = (currentBeatInLoop % 4 == 0)
            case .every6thBeat: shouldSignalThisBeat = (currentBeatInLoop % 6 == 0)
            @unknown default: 
                shouldSignalThisBeat = (currentBeatInLoop == 0)
            }

            if shouldSignalThisBeat {
                if currentBeatInLoop != self.lastHapticBeatIndex {
                    if self.vibrationEnabled {
                        self.createHapticEvent()
                    }
                    self.lastHapticBeatIndex = currentBeatInLoop
                }
            }
        }
        isTapInstalled = true
        print("MetronomeManager: Audio tap installed for background haptics.")
    }

    private func removeAudioTap() {
        if isTapInstalled, let playerNode = self.playerNode {
            playerNode.removeTap(onBus: 0)
            isTapInstalled = false
            print("MetronomeManager: Audio tap removed.")
        }
    }

    // MARK: - Playback Control

    private func startPlayback() {
        guard !isPlaying && isEnabled && activityInProgress else { return }
        print("[MetronomeManager] startPlayback: Attempting to start with self.bpm = \(self.bpm), soundID = \(self.currentSoundID)")

        do {
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("MetronomeManager: Failed to activate audio session in startPlayback: \(error)")
            return
        }

        if audioEngine?.isRunning == false {
            print("MetronomeManager: Audio engine not running, attempting to start.")
            do { try audioEngine?.start() } catch {
                print("MetronomeManager: Failed to start audio engine in startPlayback: \(error)")
                return
            }
        }
        
        guard let playerNode = self.playerNode, let engine = self.audioEngine, engine.isRunning else {
            print("MetronomeManager: PlayerNode or AudioEngine not ready in startPlayback.")
            return
        }

        if playerNode.isPlaying { playerNode.stop() }
        self.singleClickBuffer = nil
        self.metronomeLoopBuffer = nil

        if soundEnabled {
            prepareWavSound(soundID: currentSoundID)
            self.metronomeLoopBuffer = generateMetronomeLoopBuffer(bpm: Double(bpm), alertFrequency: alertFrequency)
        } else {
             self.metronomeLoopBuffer = generateMetronomeLoopBuffer(bpm: Double(bpm), alertFrequency: alertFrequency)
             print("MetronomeManager: Sound is disabled. Metronome loop buffer will be silent (if generated).")
        }
        
        if let loopBuffer = self.metronomeLoopBuffer {
            if soundEnabled || vibrationEnabled {
                playerNode.scheduleBuffer(loopBuffer, at: nil, options: [.loops, .interruptsAtLoop]) {
                    // This callback is on an internal audio queue.
                }
                
                if !playerNode.isPlaying {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                        guard let self = self,
                              let engine = self.audioEngine,
                              let playerNode = self.playerNode,
                              engine.isRunning else {
                            print("MetronomeManager: Engine or player node not ready for delayed play")
                            return
                        }
                        
                        let hasProcessedCycles = engine.mainMixerNode.lastRenderTime != nil && 
                                                engine.mainMixerNode.lastRenderTime?.sampleTime ?? 0 > 0
                        
                        if hasProcessedCycles {
                            if !playerNode.isPlaying {
                                playerNode.play()
                                let soundTypeMsg = SoundType.isWavSound(self.currentSoundID) ? "WAV (\(SoundType.wavBaseFileName(for: self.currentSoundID) ?? "N/A"))" : "OFF/Unknown"
                                print("MetronomeManager: Playback started successfully. Effective sound: \(self.soundEnabled ? soundTypeMsg : "OFF (Silent Buffer)").")
                            }
                        } else {
                            print("MetronomeManager: Engine not ready, attempting restart")
                            self.handlePlaybackError()
                        }
                    }
                }
                
                print("MetronomeManager: Buffer scheduled, playback will start after engine verification.")
            } else {
                 if playerNode.isPlaying { playerNode.stop() }
                 print("MetronomeManager: Playback not started as sound and vibration are both off.")
            }
        } else {
            if playerNode.isPlaying { playerNode.stop() }
            self.metronomeLoopBuffer = nil 
            print("MetronomeManager: Metronome loop buffer generation failed. Playback not started with audio.")
        }

        if soundEnabled || vibrationEnabled {
            isPlaying = true 
        } else {
            isPlaying = false
            if playerNode.isPlaying { playerNode.stop() }
        }

        if isInBackground {
            if isPlaying && vibrationEnabled {
                 removeAudioTap() 
                 installAudioTap() 
            } else {
                removeAudioTap()
            }
        } else {
            removeAudioTap() 
            if isPlaying {
                displayLink?.isPaused = false // Resume DisplayLink for foreground UI/haptics
            } else {
                displayLink?.isPaused = true // Pause DisplayLink when not playing
            }
        }
        lastHapticBeatIndex = -1
    }

    private func stopPlayback(temporary: Bool = false) {
        print("MetronomeManager: Stopping playback (temporary: \(temporary))")
        
        removeAudioTap()
        displayLink?.isPaused = true // Pause DisplayLink

        if let playerNode = self.playerNode, playerNode.isPlaying {
            playerNode.stop()
        }
        
        isPlaying = false
        metronomeLoopBuffer = nil 

        stopHaptics() 
        isOnBeat = false 
        lastHapticBeatIndex = -1
        
        if !temporary && !activityInProgress {
            Task { @MainActor in
                do {
                    try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                    print("MetronomeManager: Deactivated audio session in stopPlayback (not temporary, no activity)")
                } catch {
                    print("MetronomeManager: Failed to deactivate audio session in stopPlayback: \(error)")
                }
            }
        }
    }

    // MARK: - Error Handling
    
    private func handlePlaybackError() {
        print("MetronomeManager: Handling playback error, attempting recovery")
        
        if let playerNode = self.playerNode, playerNode.isPlaying {
            playerNode.stop()
        }
        
        Task { @MainActor in
            do {
                self.audioEngine?.stop()
                try self.audioEngine?.start()
                
                try await Task.sleep(nanoseconds: 200_000_000)
                
                if self.isEnabled && self.activityInProgress {
                    print("MetronomeManager: Engine restarted, attempting playback restart")
                    self.startPlayback()
                } else {
                    print("MetronomeManager: Engine restarted, but conditions no longer met for playback")
                    self.isPlaying = false
                }
            } catch {
                print("MetronomeManager: Failed to restart audio engine during error recovery: \(error)")
                self.isPlaying = false
                displayLink?.isPaused = true
            }
        }
    }

    // MARK: - Volume Control
    
    func setVolume(_ volume: Float) {
        let clampedVolume = max(0.0, min(1.0, volume))
        volumeMixerNode?.outputVolume = clampedVolume
        print("MetronomeManager: Volume set to \(clampedVolume)")
    }
    
    func getVolume() -> Float {
        return volumeMixerNode?.outputVolume ?? 0.8
    }

    // MARK: - Settings & Public Interface
    func updateSettings(bpm: Int,
                        soundEnabled: Bool,
                        vibrationEnabled: Bool,
                        soundType: Int = SoundType.beat1Wav,
                        alertFrequency: MetronomeAlertFrequency = .everyBeat) {
        print("[MetronomeManager] updateSettings: Called with bpm = \(bpm), soundID = \(soundType). Current self.bpm before update = \(self.bpm), current soundID = \(self.currentSoundID)")
        guard SoundType.isValid(soundType) else {
            print("MetronomeManager: Invalid sound type: \(soundType)")
            return
        }

        let needsAudioRestart = self.isPlaying && (
            self.bpm != bpm ||
            self.soundEnabled != soundEnabled ||
            self.currentSoundID != soundType ||
            self.alertFrequency != alertFrequency
        )

        self.bpm = bpm
        print("[MetronomeManager] updateSettings: self.bpm is now = \(self.bpm)")
        self.soundEnabled = soundEnabled
        self.vibrationEnabled = vibrationEnabled
        self.currentSoundID = soundType 
        self.alertFrequency = alertFrequency

        if needsAudioRestart {
            print("MetronomeManager: Settings updated while playing, audio properties changed, restarting playback.")
            stopPlayback(temporary: true)
            startPlayback()
        } else if isPlaying {
            if isInBackground && self.isPlaying {
                removeAudioTap()
                if self.vibrationEnabled {
                    installAudioTap()
                }
            }
            print("MetronomeManager: Settings updated (e.g., vibration only), playback continues. Vibration: \(self.vibrationEnabled).")
        }
    }

    func previewSound(_ soundId: Int, times: Int = 3, bpm: Int? = nil) {
        guard SoundType.isValid(soundId) && times > 0 else {
            print("MetronomeManager: Invalid soundId (\(soundId)) or times (\(times)) for preview.")
            return
        }

        guard let baseFileName = SoundType.wavBaseFileName(for: soundId),
              let soundURL = Bundle.main.url(forResource: baseFileName, withExtension: "wav") else {
            print("MetronomeManager: Could not find WAV file for preview: ID \(soundId), Filename: \(SoundType.wavBaseFileName(for: soundId) ?? "N/A").wav")
            return
        }

        Task { @MainActor in
            do {
                try AVAudioSession.sharedInstance().setActive(true)
            } catch {
                print("MetronomeManager: Failed to activate audio session for preview: \(error)")
                return
            }
            
            self.previewAudioPlayer?.stop()

            let effectiveBPM = Double(bpm ?? 120)
            let beatIntervalSeconds = 60.0 / effectiveBPM

            for i in 0..<times {
                do {
                    self.previewAudioPlayer = try AVAudioPlayer(contentsOf: soundURL)
                    self.previewAudioPlayer?.prepareToPlay()
                    self.previewAudioPlayer?.play()
                    print("MetronomeManager: Previewing WAV sound \(baseFileName).wav (Attempt \(i+1)/\(times)) at \(effectiveBPM) BPM")
                    
                    if i < times - 1 {
                         let delayNanoseconds = UInt64(beatIntervalSeconds * 1_000_000_000)
                         try await Task.sleep(nanoseconds: max(delayNanoseconds, 100_000_000))
                    }
                } catch {
                    print("MetronomeManager: Error playing preview sound \(baseFileName).wav: \(error)")
                    break
                }
            }
        }
    }

    func loadSettingsFromProfile(_ profile: UserProfile) {
        print("[MetronomeManager] loadSettingsFromProfile: Received profile with BPM = \(profile.metronomeBPM). Current self.bpm before update = \(self.bpm)")
        
        let soundToLoad: Int
        if SoundType.isValid(profile.metronomeSound) {
            soundToLoad = profile.metronomeSound
        } else {
            print("MetronomeManager: Migrating sound ID \(profile.metronomeSound) to default \(SoundType.beat1Wav).")
            soundToLoad = SoundType.beat1Wav
        }

        updateSettings(
            bpm: profile.metronomeBPM,
            soundEnabled: profile.metronomeSoundEnabled,
            vibrationEnabled: profile.metronomeVibrationEnabled,
            soundType: soundToLoad,
            alertFrequency: profile.metronomeAlertFrequency
        )
        
        setVolume(profile.metronomeVolume)
        isEnabled = profile.metronomeEnabled
    }
    
    func updateUserProfile(_ profile: UserProfile) {
        print("[MetronomeManager] updateUserProfile: Received profile with BPM = \(profile.metronomeBPM). Current self.bpm before update = \(self.bpm)")
        
        // Only update volume and enabled state, preserve current settings for BPM, sound, etc.
        // This prevents overwriting user changes during a workout
        setVolume(profile.metronomeVolume)
        
        // Only update enabled state if it's different to avoid unnecessary restarts
        if isEnabled != profile.metronomeEnabled {
            isEnabled = profile.metronomeEnabled
            if !isEnabled {
                stopPlayback()
            } else if activityInProgress {
                startPlayback()
            }
        }
        
        print("[MetronomeManager] updateUserProfile: Preserved current settings, only updated volume and enabled state")
    }

    func toggleMetronome(isPaused: Bool = false) {
        isEnabled.toggle()
        messageTimer?.invalidate()
        withAnimation { showingMessage = true }
        messageTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { [weak self] _ in
            guard let strongSelf = self else { return }
            Task { @MainActor in
                withAnimation { strongSelf.showingMessage = false }
            }
        }

        if !isEnabled { 
            stopPlayback() 
        } else if activityInProgress && !isPaused { 
            startPlayback() 
        }
        // Note: When paused, we only update the enabled state but don't start playback
    }

    func handleActivityStart() {
        activityInProgress = true
        if isEnabled { startPlayback() }
    }

    func handleActivityPause() {
        stopPlayback() 
    }

    func handleActivityResume() {
        if isEnabled && activityInProgress { startPlayback() }
    }

    func handleActivityEnd() {
        activityInProgress = false
        stopPlayback()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
        displayLink?.invalidate()
        messageTimer?.invalidate()

        Task { @MainActor [weak self] in
            guard self != nil else { return }
            do {
                try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
            } catch {
                print("MetronomeManager: Error deactivating audio session in deinit: \(error)")
            }
        }
    }
}
