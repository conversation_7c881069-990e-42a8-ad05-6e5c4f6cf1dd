import Foundation
import CoreLocation
import SwiftUI

@MainActor
class RouteManager: ObservableObject {
    static let shared = RouteManager()
    
    @Published private(set) var currentSegment: RouteSegment?
    @Published private(set) var completedSegments: [RouteSegment] = []
    @Published private(set) var isTracking = false
    @Published private(set) var isPaused = false
    @Published private(set) var startLocation: CLLocation? // Store the initial start location
    
    // Persistence
    private let userDefaults = UserDefaults.standard
    private let currentSegmentKey = "currentSegment"
    private let completedSegmentsKey = "completedSegments"
    private let isTrackingKey = "routeIsTracking"
    private let isPausedKey = "routeIsPaused"
    private let startLocationKey = "startLocation"
    
    // Phase 3: Background task management removed - HealthKit provides background priority
    
    // MARK: - Performance Optimization Properties
    private var isDirty = false
    private var lastSaveTime: Date?
    private let saveQueue = DispatchQueue(label: "com.runapp.routemanager.save", qos: .utility)
    
    // MARK: - Douglas-Peucker Simplification Caching
    private var simplifiedSegmentCache: [String: RouteSegment] = [:]
    private var cacheInvalidationTimestamp: Date = Date()
    
    // SIMPLIFIED: Single-Tier Storage Buffer System
    // Storage buffer for batch writing to SwiftData every 30 seconds
    private var storageBuffer: [CLLocation] = [] // Only for batch writing to SwiftData
    private var storageBufferTimer: Timer?
    private let swiftDataSaveInterval: TimeInterval = 30.0
    private var swiftDataLastSave: Date?
    
    // Data safety monitoring
    private var lastSuccessfulSave: Date?
    
    private init() {
        restoreState()
        setupStorageBufferTimer()
    }
    
    // MARK: - Memory Management
    deinit {
        print("RouteManager: deinit called - cleaning up resources")
        
        // End any pending background tasks - needs to be on main actor
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            
            // Clean up storage buffer timer
            self.cleanupStorageBufferTimer()
            
            // Flush any remaining storage buffer data
            self.flushStorageBufferToSwiftData()
            

            
            // Force save any pending changes before cleanup
            if self.isDirty {
                self.saveStateImmediately()
            }
            
            // Clear memory arrays
            self.completedSegments.removeAll()
            self.currentSegment = nil
            self.storageBuffer.removeAll()
        }
        
        print("RouteManager: deinit completed - cleanup scheduled")
    }
    
    func startTracking(initialLocation: CLLocation? = nil) {
        isTracking = true
        isPaused = false
        currentSegment = nil
        completedSegments.removeAll()
        
        // Use provided initial location if available
        if let location = initialLocation {
            startLocation = location
            // Immediately save this crucial start location to UserDefaults
            saveStartLocationImmediately(location)
            print("RouteManager: Immediately saved start location: \(location.coordinate.latitude), \(location.coordinate.longitude)")
        } else {
             // If no initial location provided, ensure any old start location is cleared
             startLocation = nil
             userDefaults.removeObject(forKey: startLocationKey) // Clear from storage too
             print("RouteManager: Started tracking without initial location, cleared previous startLocation.")
        }

        // If a valid initial location was provided, create the first segment immediately
        if let validInitialLocation = initialLocation {
            currentSegment = RouteSegment(coordinates: [validInitialLocation], isPaused: false)
        }

        // Save the rest of the state (isTracking, isPaused, segments etc.)
        saveStateImmediately() // Force immediate save for critical state changes
    }
    
    // Add this helper method to ensure start location is saved immediately
    private func saveStartLocationImmediately(_ location: CLLocation) {
        let encoder = JSONEncoder()
        // Use the existing LocationData struct for encoding
        let locationData = LocationData(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude,
            timestamp: location.timestamp,
            horizontalAccuracy: location.horizontalAccuracy,
            verticalAccuracy: location.verticalAccuracy,
            altitude: location.altitude,
            speed: location.speed,
            course: location.course
        )
        if let encoded = try? encoder.encode(locationData) {
            userDefaults.set(encoded, forKey: startLocationKey)
        } else {
             print("RouteManager: ERROR - Failed to encode immediate start location.")
        }
    }
    
    func pauseTracking() {
        isPaused = true
        print("RouteManager.pauseTracking() - BEFORE - startLocation: \(startLocation?.coordinate.latitude ?? 0), \(startLocation?.coordinate.longitude ?? 0)")
        
        // Save current segment to completed segments
        if let segment = currentSegment {
            completedSegments.append(segment)
            if let lastLocation = segment.coordinates.last {
                // Store the current start location so we can restore it
                let originalStartLocation = startLocation
                
                // Create new paused segment from the last location
                currentSegment = RouteSegment(coordinates: [lastLocation], isPaused: true)
                
                // Restore the original start location
                startLocation = originalStartLocation
            }
        }
        
        print("RouteManager.pauseTracking() - AFTER - startLocation: \(startLocation?.coordinate.latitude ?? 0), \(startLocation?.coordinate.longitude ?? 0)")
        saveStateImmediately() // Force immediate save for critical state changes
    }
    
    func resumeTracking() {
        isPaused = false
        print("RouteManager.resumeTracking() - BEFORE - startLocation: \(startLocation?.coordinate.latitude ?? 0), \(startLocation?.coordinate.longitude ?? 0)")
        
        // Save paused segment and start a new active segment
        if let segment = currentSegment {
            completedSegments.append(segment)
            if let lastLocation = segment.coordinates.last {
                // Store the current start location so we can restore it
                let originalStartLocation = startLocation
                
                // Create new segment from the last location
                currentSegment = RouteSegment(coordinates: [lastLocation], isPaused: false)
                
                // Restore the original start location
                startLocation = originalStartLocation
            }
        }
        
        print("RouteManager.resumeTracking() - AFTER - startLocation: \(startLocation?.coordinate.latitude ?? 0), \(startLocation?.coordinate.longitude ?? 0)")
        saveStateImmediately() // Force immediate save for critical state changes
    }
    
    func stopTracking() {
        // Save final segment if exists
        if let segment = currentSegment {
            completedSegments.append(segment)
        }
        
        isTracking = false
        isPaused = false
        currentSegment = nil
        
        // Force immediate save for critical state changes
        saveStateImmediately()
        
        // Don't clear the completed segments yet - they'll be needed for activity saving
        // The segments will be accessed by ContentView.saveActivity before being cleared
    }
    
    // Call this explicitly after the activity has been saved
    func clearRouteData() {
        // Clear all route data completely
        completedSegments.removeAll()
        currentSegment = nil
        startLocation = nil
        isTracking = false
        isPaused = false
        
        // Clear simplification caches
        clearSimplificationCaches()
        
        // Clear persistent state
        clearState()
    }
    
    // Reset all route data with UI refresh guarantee
    @MainActor
    func resetAllRouteData() {
        // Explicitly reset each property individually
        isTracking = false
        isPaused = false
        
        // Remove all segments
        completedSegments.removeAll()
        currentSegment = nil
        startLocation = nil
        
        // Clear persistent storage
        clearState()
        
        // Send explicit UI refresh signal
        objectWillChange.send()
    }
    
    func processLocationUpdate(_ location: CLLocation) {

        
        // Allow location processing even when paused to draw light blue line
        guard isTracking else { return }
        
        // Skip if accuracy is poor
        guard location.horizontalAccuracy <= 20 else {
            print("RouteManager: Skipping low accuracy location (\(location.horizontalAccuracy)m)")
            return
        }
        
        // TIER 1: Process filtered location for REAL-TIME calculations and UI updates
        processLocationForRealTime(location)
        
        // TIER 2: Add filtered location to STORAGE BUFFER for batch writing to SwiftData
        addLocationToStorageBuffer(location)
        

    }
    
    /// PERFORMANCE FIX: Async route processing to prevent main thread blocking
    /// This method allows heavy route computation to run on background threads
    /// while ensuring @Published properties update the UI automatically
    func processLocationUpdateAsync(_ location: CLLocation) async {
        // Allow location processing even when paused to draw light blue line
        let currentlyTracking = await MainActor.run { isTracking }
        guard currentlyTracking else { return }
        
        // Skip if accuracy is poor
        guard location.horizontalAccuracy <= 20 else {
            print("RouteManager: Skipping low accuracy location (\(location.horizontalAccuracy)m)")
            return
        }
        
        // TIER 1: Process filtered location for REAL-TIME calculations and UI updates
        // Heavy computation can run on background thread
        await processLocationForRealTimeAsync(location)
        
        // TIER 2: Add filtered location to STORAGE BUFFER for batch writing to SwiftData
        await addLocationToStorageBufferAsync(location)
    }
    
    // MARK: - Tier 1: Real-Time Processing
    private func processLocationForRealTime(_ location: CLLocation) {
        // Log background updates
        let isBackgroundUpdate = UIApplication.shared.applicationState != .active
        if isBackgroundUpdate {
            print("RouteManager: Processing location update in background")
        }
        
        if currentSegment == nil {
            // When creating a new segment, use the current pause state
            currentSegment = RouteSegment(coordinates: [location], isPaused: isPaused)
            if startLocation == nil {
                startLocation = location
                // Ensure start location is saved immediately
                saveStartLocationImmediately(location)
            }
        } else {
            var coordinates = currentSegment!.coordinates
            
            // Only add point if it's not too close to the previous point
            if let lastLocation = coordinates.last {
                let distance = location.distance(from: lastLocation)
                let timeDiff = location.timestamp.timeIntervalSince(lastLocation.timestamp)
                let speed = location.speed
                
                // For paused state, use more lenient distance filtering
                let minDistance: CLLocationDistance
                if isPaused {
                    minDistance = speed < 1.0 ? 2.0 : min(speed * 1.0, 10.0)
                } else {
                    minDistance = speed < 1.0 ? 3.0 : min(speed * 1.5, 15.0)
                }
                let maxSpeed: CLLocationSpeed = 60.0 // m/s (~216 km/h)
                
                guard distance >= minDistance &&
                      timeDiff > 0 &&
                      (speed <= maxSpeed || (distance / timeDiff) <= maxSpeed) else {
                    return
                }
            }
            
            coordinates.append(location)
            
            // Limit the number of coordinates to prevent memory issues
            if coordinates.count > 1000 {
                coordinates.removeFirst(coordinates.count - 1000)
            }
            
            currentSegment = RouteSegment(coordinates: coordinates, isPaused: isPaused)
            
            // Log successful update in background
            if isBackgroundUpdate && coordinates.count % 10 == 0 {
                print("RouteManager: Route updated in background, total points: \(coordinates.count)")
            }
        }
        
        // Simple UserDefaults persistence for current segment state (immediate)
        isDirty = true
        saveState()
    }
    
    // MARK: - Tier 2: Storage Buffer Management
    private func addLocationToStorageBuffer(_ location: CLLocation) {
        // Add filtered location to storage buffer
        storageBuffer.append(location)
        
        // Prevent storage buffer overflow
        let maxStorageBufferSize = 200 // Conservative limit
        if storageBuffer.count > maxStorageBufferSize {
            print("RouteManager: Storage buffer overflow, force flushing to SwiftData")
            flushStorageBufferToSwiftData()
        }
        
        print("RouteManager: Added location to storage buffer (total: \(storageBuffer.count))")
    }
    
    // MARK: - Async Background Processing Methods
    
    /// Async version of processLocationForRealTime for background thread processing
    private func processLocationForRealTimeAsync(_ location: CLLocation) async {
        // Log background updates
        let isBackgroundUpdate = await MainActor.run { UIApplication.shared.applicationState != .active }
        if isBackgroundUpdate {
            print("RouteManager: Processing location update in background (async)")
        }
        
        // Get current state from main actor
        let (currentSegmentExists, currentIsPaused) = await MainActor.run {
            (currentSegment != nil, isPaused)
        }
        
        if !currentSegmentExists {
            // When creating a new segment, use the current pause state
            let newSegment = RouteSegment(coordinates: [location], isPaused: currentIsPaused)
            
            await MainActor.run {
                self.currentSegment = newSegment
                if self.startLocation == nil {
                    self.startLocation = location
                    // Ensure start location is saved immediately
                    self.saveStartLocationImmediately(location)
                }
            }
        } else {
            // Get current segment coordinates (this is the heavy computation part)
            let currentCoordinates = await MainActor.run { currentSegment?.coordinates ?? [] }
            var coordinates = currentCoordinates
            
            // Only add point if it's not too close to the previous point
            if let lastLocation = coordinates.last {
                let distance = location.distance(from: lastLocation)
                let timeDiff = location.timestamp.timeIntervalSince(lastLocation.timestamp)
                let speed = location.speed
                
                // For paused state, use more lenient distance filtering
                let minDistance: CLLocationDistance
                if currentIsPaused {
                    minDistance = speed < 1.0 ? 2.0 : min(speed * 1.0, 10.0)
                } else {
                    minDistance = speed < 1.0 ? 3.0 : min(speed * 1.5, 15.0)
                }
                let maxSpeed: CLLocationSpeed = 60.0 // m/s (~216 km/h)
                
                guard distance >= minDistance &&
                      timeDiff > 0 &&
                      (speed <= maxSpeed || (distance / timeDiff) <= maxSpeed) else {
                    return
                }
            }
            
            coordinates.append(location)
            
            // Limit the number of coordinates to prevent memory issues
            if coordinates.count > 1000 {
                coordinates.removeFirst(coordinates.count - 1000)
            }
            
            // Update the segment on main thread
            await MainActor.run {
                self.currentSegment = RouteSegment(coordinates: coordinates, isPaused: currentIsPaused)
                
                // Log successful update in background
                if isBackgroundUpdate && coordinates.count % 10 == 0 {
                    print("RouteManager: Route updated in background (async), total points: \(coordinates.count)")
                }
            }
        }
        
        // Simple UserDefaults persistence for current segment state (immediate)
        await MainActor.run {
            self.isDirty = true
            self.saveState()
        }
    }
    
    /// Async version of addLocationToStorageBuffer for background thread processing
    private func addLocationToStorageBufferAsync(_ location: CLLocation) async {
        await MainActor.run {
            // Add filtered location to storage buffer
            self.storageBuffer.append(location)
            
            // Prevent storage buffer overflow
            let maxStorageBufferSize = 200 // Conservative limit
            if self.storageBuffer.count > maxStorageBufferSize {
                print("RouteManager: Storage buffer overflow, force flushing to SwiftData (async)")
                self.flushStorageBufferToSwiftData()
            }
            
            print("RouteManager: Added location to storage buffer (async) (total: \(self.storageBuffer.count))")
        }
    }
    
    // Phase 3: Async background task management removed - HealthKit provides background priority
    
    // MARK: - Hybrid Bulk Processing Methods
    
    /// PERFORMANCE OPTIMIZATION: Bulk processing for pause-resume scenarios
    /// Processes all accumulated locations in ONE operation (like past route review)
    func processBulkLocationUpdate(_ locations: [CLLocation]) async {
        // Validate tracking state
        let currentlyTracking = await MainActor.run { isTracking }
        guard currentlyTracking else { return }
        
        print("RouteManager: Starting bulk processing of \(locations.count) locations")
        
        // BULK FILTERING - Process all locations at once
        let filteredLocations = await bulkFilterLocations(locations)
        
        print("RouteManager: Bulk filtering complete - \(filteredLocations.count) valid locations from \(locations.count) total")
        
        // BULK ROUTE BUILDING - Create complete route segment
        let processedSegment = await buildRouteSegmentFromBulk(filteredLocations)
        
        // ONE UI UPDATE - Single @Published property update
        await MainActor.run {
            self.currentSegment = processedSegment
            self.isDirty = true
            self.saveState()
            print("RouteManager: Bulk processing complete - route updated with \(processedSegment.coordinates.count) total coordinates")
        }
        
        // Bulk storage buffer update
        await addBulkLocationToStorageBuffer(filteredLocations)
    }
    
    /// Enhanced background processing with simplification
    func processBulkLocationUpdateWithSimplification(_ locations: [CLLocation]) async {
        // Existing bulk processing
        await processBulkLocationUpdate(locations)
        
        // Pre-generate simplified routes for immediate display readiness
        await preGenerateSimplifiedRoutesAsync()
    }
    
    /// Bulk location filtering - maintains same accuracy as individual processing
    private func bulkFilterLocations(_ locations: [CLLocation]) async -> [CLLocation] {
        var filtered: [CLLocation] = []
        var lastValidLocation: CLLocation?
        
        // Get current pause state once for the entire batch
        let currentlyPaused = await MainActor.run { isPaused }
        
        for location in locations.sorted(by: { $0.timestamp < $1.timestamp }) {
            // Same accuracy filtering as individual processing
            guard location.horizontalAccuracy <= 20 else { continue }
            
            if let lastLocation = lastValidLocation {
                let distance = location.distance(from: lastLocation)
                let timeDiff = location.timestamp.timeIntervalSince(lastLocation.timestamp)
                let speed = location.speed
                
                // Same distance/speed validation logic as individual processing
                let minDistance: CLLocationDistance
                if currentlyPaused {
                    minDistance = speed < 1.0 ? 2.0 : min(speed * 1.0, 10.0)
                } else {
                    minDistance = speed < 1.0 ? 3.0 : min(speed * 1.5, 15.0)
                }
                let maxSpeed: CLLocationSpeed = 60.0
                
                guard distance >= minDistance &&
                      timeDiff > 0 &&
                      (speed <= maxSpeed || (distance / timeDiff) <= maxSpeed) else {
                    continue
                }
            }
            
            filtered.append(location)
            lastValidLocation = location
        }
        
        return filtered
    }
    
    /// Build complete route segment from bulk filtered locations
    private func buildRouteSegmentFromBulk(_ locations: [CLLocation]) async -> RouteSegment {
        // Get current segment coordinates to append to
        let existingCoordinates = await MainActor.run {
            currentSegment?.coordinates ?? []
        }
        
        // Combine existing + new filtered locations
        var allCoordinates = existingCoordinates + locations
        
        // Apply memory management (same as individual processing)
        if allCoordinates.count > 1000 {
            allCoordinates.removeFirst(allCoordinates.count - 1000)
        }
        
        let currentlyPaused = await MainActor.run { isPaused }
        return RouteSegment(coordinates: allCoordinates, isPaused: currentlyPaused)
    }
    
    /// Bulk version of addLocationToStorageBuffer
    private func addBulkLocationToStorageBuffer(_ locations: [CLLocation]) async {
        await MainActor.run {
            // Add all filtered locations to storage buffer at once
            self.storageBuffer.append(contentsOf: locations)
            
            // Check for overflow
            let maxStorageBufferSize = 200
            if self.storageBuffer.count > maxStorageBufferSize {
                print("RouteManager: Storage buffer overflow after bulk update, flushing to SwiftData")
                self.flushStorageBufferToSwiftData()
            }
            
            print("RouteManager: Added \(locations.count) locations to storage buffer (bulk) (total: \(self.storageBuffer.count))")
        }
    }
    
    // MARK: - Simplified State Persistence
    
    /// Forces immediate save (for critical state changes)
    private func saveStateImmediately() {
        isDirty = true
        saveState()
    }
    
    /// Optimized background save to prevent main thread blocking
    private func saveState() {
        guard isDirty else { return }
        
        // Capture current state on main thread (fast)
        let segmentToSave = currentSegment
        let completedToSave = completedSegments
        let trackingState = isTracking
        let pausedState = isPaused
        let locationToSave = startLocation
        
        // Update save time and clear dirty flag
        lastSaveTime = Date()
        isDirty = false
        
        // Heavy encoding work on background queue
        saveQueue.async { [weak self] in
            guard let self = self else { return }
            
            var dataToSave: [String: Data] = [:]
            var saveSuccess = true
            
            // Heavy JSON encoding operations (OFF main thread)
            let encoder = JSONEncoder()
            
            // Encode current segment
            if let segment = segmentToSave {
                if let encoded = try? encoder.encode(segment) {
                    dataToSave[self.currentSegmentKey] = encoded
                } else {
                    print("RouteManager: ERROR - Failed to encode current segment")
                    saveSuccess = false
                }
            }
            
            // Encode completed segments
            if let encoded = try? encoder.encode(completedToSave) {
                dataToSave[self.completedSegmentsKey] = encoded
            } else {
                print("RouteManager: ERROR - Failed to encode completed segments")
                saveSuccess = false
            }
            
            // Encode start location
            if let location = locationToSave {
                let locationData = LocationData(
                    latitude: location.coordinate.latitude,
                    longitude: location.coordinate.longitude,
                    timestamp: location.timestamp,
                    horizontalAccuracy: location.horizontalAccuracy,
                    verticalAccuracy: location.verticalAccuracy,
                    altitude: location.altitude,
                    speed: location.speed,
                    course: location.course
                )
                if let encoded = try? encoder.encode(locationData) {
                    dataToSave[self.startLocationKey] = encoded
                } else {
                    print("RouteManager: ERROR - Failed to encode start location")
                    saveSuccess = false
                }
            }
            
            // Quick UserDefaults write on main thread
            DispatchQueue.main.async {
                if saveSuccess {
                    // Write encoded data
                    for (key, data) in dataToSave {
                        self.userDefaults.set(data, forKey: key)
                    }
                    
                    // Write simple boolean states
                    self.userDefaults.set(trackingState, forKey: self.isTrackingKey)
                    self.userDefaults.set(pausedState, forKey: self.isPausedKey)
                    
                    // Remove keys for nil values
                    if segmentToSave == nil {
                        self.userDefaults.removeObject(forKey: self.currentSegmentKey)
                    }
                    if locationToSave == nil {
                        self.userDefaults.removeObject(forKey: self.startLocationKey)
                    }
                    
                    // Track successful save
                    self.lastSuccessfulSave = Date()
                    
                    // Log successful save periodically
                    let totalLocations = completedToSave.reduce(0) { $0 + $1.coordinates.count } + 
                                       (segmentToSave?.coordinates.count ?? 0)
                    if totalLocations % 50 == 0 && totalLocations > 0 {
                        print("RouteManager: Successfully saved route data (\(totalLocations) total locations)")
                    }
                    
                } else {
                    print("RouteManager: Save failed - will retry on next update")
                    self.isDirty = true
                }
            }
        }
    }
    
    private func restoreState() {
        print("RouteManager.restoreState() - BEFORE - startLocation: \(startLocation?.coordinate.latitude ?? 0), \(startLocation?.coordinate.longitude ?? 0)")
        
        // Restore tracking state
        isTracking = userDefaults.bool(forKey: isTrackingKey)
        isPaused = userDefaults.bool(forKey: isPausedKey)
        
        // Restore current segment
        if let data = userDefaults.data(forKey: currentSegmentKey) {
            let decoder = JSONDecoder()
            currentSegment = try? decoder.decode(RouteSegment.self, from: data)
        }
        
        // Restore completed segments
        if let data = userDefaults.data(forKey: completedSegmentsKey) {
            let decoder = JSONDecoder()
            if let decoded = try? decoder.decode([RouteSegment].self, from: data) {
                completedSegments = decoded
            }
        }
        
        // Restore start location
        if let data = userDefaults.data(forKey: startLocationKey) {
            let decoder = JSONDecoder()
            if let locationData = try? decoder.decode(LocationData.self, from: data) {
                startLocation = CLLocation(
                    coordinate: CLLocationCoordinate2D(
                        latitude: locationData.latitude,
                        longitude: locationData.longitude
                    ),
                    altitude: locationData.altitude,
                    horizontalAccuracy: locationData.horizontalAccuracy,
                    verticalAccuracy: locationData.verticalAccuracy,
                    course: locationData.course,
                    speed: locationData.speed,
                    timestamp: locationData.timestamp
                )
            }
        }
        
        print("RouteManager.restoreState() - AFTER - startLocation: \(startLocation?.coordinate.latitude ?? 0), \(startLocation?.coordinate.longitude ?? 0)")
    }
    
    private func clearState() {
        userDefaults.removeObject(forKey: currentSegmentKey)
        userDefaults.removeObject(forKey: completedSegmentsKey)
        userDefaults.removeObject(forKey: isTrackingKey)
        userDefaults.removeObject(forKey: isPausedKey)
        userDefaults.removeObject(forKey: startLocationKey)
        
        // Clear optimization flags
        isDirty = false
        lastSaveTime = nil
        swiftDataLastSave = nil
        
        // Clear storage buffer to prevent data accumulation
        storageBuffer.removeAll()
        
        print("RouteManager: Cleared all state including storage buffer")
    }
    
    // MARK: - Phase 3: Background task management removed - HealthKit provides background priority
    
    // MARK: - Location Data Structure
    private struct LocationData: Codable {
        let latitude: Double
        let longitude: Double
        let timestamp: Date
        let horizontalAccuracy: CLLocationAccuracy
        let verticalAccuracy: CLLocationAccuracy
        let altitude: CLLocationDistance
        let speed: CLLocationSpeed
        let course: CLLocationDirection
    }
    
    // Helper method to check if any route data exists
    var hasAnyRouteData: Bool {
        let hasSegments = !completedSegments.isEmpty
        let hasCurrentSegment = currentSegment != nil
        let hasStartLocation = startLocation != nil
        
        let result = hasSegments || hasCurrentSegment || hasStartLocation
        if result {
            print("RouteManager: Contains route data - segments: \(completedSegments.count), currentSegment: \(hasCurrentSegment), startLocation: \(hasStartLocation)")
        }
        return result
    }
    
    // MARK: - Two-Tier Buffer System Setup
    
    private func setupStorageBufferTimer() {
        storageBufferTimer?.invalidate()
        
        // Timer to flush storage buffer to SwiftData every 30 seconds
        storageBufferTimer = Timer.scheduledTimer(withTimeInterval: swiftDataSaveInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.flushStorageBufferToSwiftData()
            }
        }
        
        print("RouteManager: Set up 30-second timer to flush storage buffer to SwiftData")
    }
    
    private func cleanupStorageBufferTimer() {
        storageBufferTimer?.invalidate()
        storageBufferTimer = nil
    }
    
    // MARK: - Two-Tier Buffer System Core Methods
    
    /// Tier 1: Save latest data to storage buffer (fast)
    private func saveToStorageBuffer() {
        // This method is no longer needed since we're adding locations directly to storage buffer
        // The actual saving happens in addLocationToStorageBuffer when each filtered location arrives
        swiftDataLastSave = Date()
        print("RouteManager: Storage buffer update triggered")
    }
    
    /// Tier 2: Flush storage buffer to SwiftData (slow but permanent)
    private func flushStorageBufferToSwiftData() {
        guard !storageBuffer.isEmpty else { return }
        
        print("RouteManager: Flushing \(storageBuffer.count) locations from storage buffer to SwiftData")
        
        // Convert locations to coordinate format for final storage
        let coordinatesToSave = storageBuffer.map { location in
            Coordinate(
                latitude: location.coordinate.latitude,
                longitude: location.coordinate.longitude,
                altitude: location.altitude,
                timestamp: location.timestamp,
                isPaused: isPaused, // Use current pause state
                speed: location.speed >= 0 ? location.speed : nil
            )
        }
        
        // CRITICAL: Clear storage buffer IMMEDIATELY to prevent accumulation
        storageBuffer.removeAll()
        
        // TODO: Write coordinatesToSave to SwiftData here
        // For now, this is just logging the save operation
        
        print("RouteManager: Storage buffer CLEARED after flushing \(coordinatesToSave.count) coordinates to SwiftData")
        
        // Update save time
        swiftDataLastSave = Date()
    }
    
    // MARK: - Background Buffer Support (Performance Optimization)
    // Note: addLocationToStorageBuffer() method already exists at line 329
    // This section documents that the existing method supports background accumulation
}

// MARK: - Douglas-Peucker Route Simplification
// NOTE: These methods are optimized for HISTORICAL data viewing (ActivityRowView)
// Real-time tracking (ContentView) uses filtered segments directly for best performance
extension RouteManager {
    /// Get simplified current segment for display (HISTORICAL USE ONLY)
    /// For real-time tracking, use currentSegment directly to avoid processing overhead
    func getCurrentSegmentForDisplay(context: SimplificationContext = .realTimeView) -> RouteSegment? {
        guard let current = currentSegment else { return nil }
        
        // If the context is real-time, return the raw (internally filtered) segment directly
        // to avoid any Douglas-Peucker processing overhead for live display.
        if context == .realTimeView {
            return current
        }
        
        let tolerance = RouteSimplifier.adaptiveTolerance(for: context)
        let cacheKey = "current_\(String(format: "%.6f", tolerance))"
        
        // Check if cache is still valid (current segment changes frequently)
        if let cached = simplifiedSegmentCache[cacheKey],
           Date().timeIntervalSince(cacheInvalidationTimestamp) < 10 { // 10-second cache for current
            return cached
        }
        
        // Convert CLLocation to Coordinate for simplification
        let coordinates = current.coordinates.map { location in
            Coordinate(
                latitude: location.coordinate.latitude,
                longitude: location.coordinate.longitude,
                altitude: location.altitude,
                timestamp: location.timestamp,
                isPaused: current.isPaused,
                speed: location.speed >= 0 ? location.speed : nil
            )
        }
        
        // Generate simplified current segment
        let simplified = RouteSimplifier.safeSimplify(coordinates: coordinates, tolerance: tolerance)
        
        // Convert back to CLLocation for RouteSegment
        let simplifiedLocations = simplified.map { coordinate in
            CLLocation(
                coordinate: CLLocationCoordinate2D(latitude: coordinate.latitude, longitude: coordinate.longitude),
                altitude: 0,
                horizontalAccuracy: 5,
                verticalAccuracy: -1,
                course: -1,
                speed: coordinate.speed ?? -1,
                timestamp: coordinate.timestamp
            )
        }
        
        let simplifiedSegment = RouteSegment(coordinates: simplifiedLocations, isPaused: current.isPaused)
        
        simplifiedSegmentCache[cacheKey] = simplifiedSegment
        cacheInvalidationTimestamp = Date()
        
        return simplifiedSegment
    }
    
    /// Get simplified completed segment with persistent caching (HISTORICAL USE ONLY)
    /// For real-time tracking, use completedSegments[index] directly for best performance
    func getCompletedSegmentForDisplay(
        index: Int,
        context: SimplificationContext = .realTimeView
    ) -> RouteSegment? {
        guard index < completedSegments.count else { return nil }
        
        let tolerance = RouteSimplifier.adaptiveTolerance(for: context)
        let cacheKey = "completed_\(index)_\(String(format: "%.6f", tolerance))"
        
        // Completed segments are immutable, so cache indefinitely
        if let cached = simplifiedSegmentCache[cacheKey] {
            return cached
        }
        
        let segment = completedSegments[index]
        
        // Convert CLLocation to Coordinate for simplification
        let coordinates = segment.coordinates.map { location in
            Coordinate(
                latitude: location.coordinate.latitude,
                longitude: location.coordinate.longitude,
                altitude: location.altitude,
                timestamp: location.timestamp,
                isPaused: segment.isPaused,
                speed: location.speed >= 0 ? location.speed : nil
            )
        }
        
        let simplified = RouteSimplifier.safeSimplify(coordinates: coordinates, tolerance: tolerance)
        
        // Convert back to CLLocation for RouteSegment
        let simplifiedLocations = simplified.map { coordinate in
            CLLocation(
                coordinate: CLLocationCoordinate2D(latitude: coordinate.latitude, longitude: coordinate.longitude),
                altitude: 0,
                horizontalAccuracy: 5,
                verticalAccuracy: -1,
                course: -1,
                speed: coordinate.speed ?? -1,
                timestamp: coordinate.timestamp
            )
        }
        
        let simplifiedSegment = RouteSegment(coordinates: simplifiedLocations, isPaused: segment.isPaused)
        
        simplifiedSegmentCache[cacheKey] = simplifiedSegment
        
        return simplifiedSegment
    }
    
    /// Pre-generate simplified routes for common contexts
    func preGenerateSimplifiedRoutes() {
        Task.detached(priority: .utility) {
            // We only need to pre-generate for historical contexts, as real-time will use raw data
            let contexts: [SimplificationContext] = [.activityDetail, .activityList]
            
            await MainActor.run {
                for context in contexts {
                    // Pre-generate for completed segments
                    for index in 0..<self.completedSegments.count {
                        _ = self.getCompletedSegmentForDisplay(index: index, context: context)
                    }
                    
                    // The 'currentSegment' for real-time display will no longer be simplified via this path,
                    // so no need to pre-generate it here.
                    // If there are other contexts (e.g., .backgroundProcessing) that need current segment simplification,
                    // they would be added here explicitly.
                    if self.currentSegment != nil {
                         // If you had other contexts, you would add logic here
                         // For example:
                         // if context == .backgroundProcessing {
                         //     _ = self.getCurrentSegmentForDisplay(context: .backgroundProcessing)
                         // }
                    }
                }
                print("RouteManager: Pre-generated simplified routes for \(contexts.count) historical contexts")
            }
        }
    }
    
    /// Async version of pre-generation for background processing
    private func preGenerateSimplifiedRoutesAsync() async {
        // Background simplification without blocking UI
        let contexts: [SimplificationContext] = [.activityDetail, .activityList]
        
        await MainActor.run {
            for context in contexts {
                for index in 0..<self.completedSegments.count {
                    _ = self.getCompletedSegmentForDisplay(index: index, context: context)
                }
            }
        }
    }
    
    /// Clear caches when workout ends or resets
    func clearSimplificationCaches() {
        simplifiedSegmentCache.removeAll()
        print("RouteManager: Cleared simplification caches")
    }
}
