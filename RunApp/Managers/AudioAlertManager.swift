import Foundation
import AVFoundation
import SwiftUI
import SwiftData
import UIKit

@MainActor
class AudioAlertManager: NSObject, ObservableObject {
    static let shared = AudioAlertManager()
    
    private let speechSynthesizer = AVSpeechSynthesizer()
    private var userProfile: UserProfile?
    
    // Alert state tracking
    private var lastDistanceAlert: Double = 0
    private var lastTimeAlert: TimeInterval = 0
    private var lastCalorieAlert: Double = 0.0 // Changed to Double
    private var lastPaceAlert: TimeInterval = 0
    
    // Volume control
    private var currentVolume: Float = AppConstants.defaultAudioPromptVolume
    
    // Background optimization
    private var isInBackground = false
    private var isHibernating = false
    
    // Audio session management
    private var audioSessionConfigured = false
    
    private override init() {
        super.init()
        setupAudioSession()
        setupSpeechSynthesizer()
        setupBackgroundObservers()
    }
    
    // MARK: - Setup
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            
            // Configure audio session for background playback with mixing (no ducking by default)
            try audioSession.setCategory(
                .playback,
                mode: .default,
                options: [.mixWithOthers, .allowBluetoothA2DP]
            )
            
            // Activate the audio session
            try audioSession.setActive(true)
            audioSessionConfigured = true
            
            print("Audio session configured successfully for background alerts (with mixing)")
            
        } catch {
            print("Failed to configure audio session: \(error.localizedDescription)")
            audioSessionConfigured = false
        }
    }
    
    private func setupSpeechSynthesizer() {
        speechSynthesizer.delegate = self
    }
    
    private func setupBackgroundObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
        
        // Handle audio session interruptions
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAudioSessionInterruption),
            name: AVAudioSession.interruptionNotification,
            object: nil
        )
        
        // Add hibernation observers
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleHibernation),
            name: .appShouldHibernate,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWakeUp),
            name: .appShouldWakeUp,
            object: nil
        )
        
        // Note: Voice change notifications are not available in iOS
        // Voice changes are rare and will be handled by manual refresh when needed
    }
    
    @objc private func handleAppBackground() {
        isInBackground = true
        // Ensure audio session remains active for background speech
        ensureAudioSessionActive()
    }
    
    @objc private func handleAppForeground() {
        isInBackground = false
        ensureAudioSessionActive()
    }
    
    @objc private func handleAudioSessionInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            // Audio session was interrupted
            break
        case .ended:
            // Audio session interruption ended, reactivate if needed
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    ensureAudioSessionActive()
                }
            }
        @unknown default:
            break
        }
    }
    
    private func ensureAudioSessionActive() {
        guard audioSessionConfigured else {
            setupAudioSession()
            return
        }
        
        do {
            let audioSession = AVAudioSession.sharedInstance()
            if !audioSession.isOtherAudioPlaying {
                try audioSession.setActive(true)
            }
        } catch {
            print("Failed to reactivate audio session: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Hibernation Handlers
    
    @objc private func handleHibernation() {
        // AudioAlertManager can hibernate when not in workout mode
        // It should stop any ongoing speech and clean up resources
        isHibernating = true
        print("AudioAlertManager: Entering hibernation mode")
        
        // Stop any ongoing speech synthesis
        if speechSynthesizer.isSpeaking {
            speechSynthesizer.stopSpeaking(at: .immediate)
        }
        
        // Remove audio session observers to reduce overhead
        NotificationCenter.default.removeObserver(
            self,
            name: AVAudioSession.interruptionNotification,
            object: AVAudioSession.sharedInstance()
        )
        
        // Deactivate audio session
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
            audioSessionConfigured = false
            print("AudioAlertManager: Audio session deactivated for hibernation")
        } catch {
            print("AudioAlertManager: Failed to deactivate audio session during hibernation: \(error)")
        }
    }
    
    @objc private func handleWakeUp() {
        guard isHibernating else { return }
        
        isHibernating = false
        print("AudioAlertManager: Waking up from hibernation")
        
        // Restore audio session and observers
        setupAudioSession()
        
        // Re-add audio session interruption observer
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAudioSessionInterruption),
            name: AVAudioSession.interruptionNotification,
            object: nil
        )
        
        print("AudioAlertManager: Hibernation wake-up complete")
    }
    
    // MARK: - Audio Ducking Management
    
    /// Temporarily enable audio ducking for speech synthesis
    private func enableAudioDucking() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(
                .playback,
                mode: .default,
                options: [.mixWithOthers, .duckOthers, .allowBluetoothA2DP]
            )
            print("AudioAlertManager: Audio ducking enabled for speech")
        } catch {
            print("Failed to enable audio ducking: \(error.localizedDescription)")
        }
    }
    
    /// Restore normal audio mixing after speech synthesis
    private func disableAudioDucking() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(
                .playback,
                mode: .default,
                options: [.mixWithOthers, .allowBluetoothA2DP]
            )
            print("AudioAlertManager: Audio ducking disabled, normal mixing restored")
        } catch {
            print("Failed to disable audio ducking: \(error.localizedDescription)")
        }
    }

    // MARK: - Volume Control
    
    /// Sets the audio alert volume (0.0 to 1.0)
    func setVolume(_ volume: Float) {
        currentVolume = max(0.0, min(1.0, volume))
        print("AudioAlertManager: Volume set to \(currentVolume)")
    }
    
    /// Gets the current audio alert volume
    func getVolume() -> Float {
        return currentVolume
    }

    // MARK: - Public Interface
    
    /// Update user profile for alert preferences and unit system
    func updateUserProfile(_ profile: UserProfile) {
        self.userProfile = profile
        // Set volume from profile
        setVolume(profile.audioAlertVolume)
    }
    
    /// Reset alert counters (call when starting new activity)
    func resetAlertCounters() {
        lastDistanceAlert = 0
        lastTimeAlert = 0
        lastCalorieAlert = 0
        lastPaceAlert = 0
        
        // Reset voice variation tracking for new session
        VoicePromptVariationManager.shared.resetSession()
    }
    
    /// Test audio alerts functionality (useful for debugging background audio)
    func testAudioAlert() {
        guard let profile = userProfile else {
            speak("Audio alerts are working correctly")
            return
        }
        
        let messageKey = profile.smartVoiceEnabled ? "audio.test.smart" : "audio.test.standard"
        speak(messageKey.localized)
    }
    
    /// Check and trigger appropriate alerts based on current activity data
    func checkAndTriggerAlerts(distance: Double, duration: TimeInterval, calories: Double, pace: Double) {
        let logManager = LogManager.shared
        
        // Log entry point with all input parameters
        logManager.log("checkAndTriggerAlerts called - distance: \(String(format: "%.2f", distance))m, duration: \(String(format: "%.1f", duration))s, calories: \(String(format: "%.1f", calories)), pace: \(String(format: "%.2f", pace))", category: .audio)
        
        guard let profile = userProfile, profile.audioAlertsEnabled else { 
            logManager.logAudioAlert(type: "CheckAlerts", triggered: false, pace: pace, reason: "Audio alerts disabled or no profile")
            return 
        }
        
        // Log user profile alert settings for debugging
        logManager.log("Profile settings - paceAlertEnabled: \(profile.paceAlertEnabled), paceAlertInterval: \(profile.paceAlertInterval)s, lastPaceAlert: \(String(format: "%.1f", lastPaceAlert))s", category: .audio)
        
        // Check distance alerts - using simple distance-since-last-alert logic
        if profile.distanceAlertEnabled {
            let currentDistanceInUserUnit = profile.units.convertDistance(distance)
            let interval = profile.distanceAlertInterval
            let distanceSinceLastAlert = currentDistanceInUserUnit - lastDistanceAlert
            
            if distanceSinceLastAlert >= interval {
                // Calculate the alert distance (rounded to nearest interval)
                let alertDistance = lastDistanceAlert + interval
                logManager.logAudioAlert(type: "Distance", triggered: true, pace: pace, reason: "Distance interval reached: \(alertDistance) \(profile.units.rawValue)")
                announceDistance(alertDistance, unit: profile.units)
                lastDistanceAlert = alertDistance
            }
        }
        
        // Check time alerts - using simple time-since-last-alert logic
        if profile.timeAlertEnabled {
            let interval = profile.timeAlertInterval
            let timeSinceLastAlert = duration - lastTimeAlert
            
            if timeSinceLastAlert >= interval {
                let alertTime = lastTimeAlert + interval
                logManager.logAudioAlert(type: "Time", triggered: true, pace: pace, reason: "Time interval reached: \(Int(alertTime/60))min")
                announceTime(alertTime)
                lastTimeAlert = alertTime
            }
        }
        
        // Check calorie alerts - using simple calorie-since-last-alert logic
        if profile.calorieAlertEnabled {
            let interval = profile.calorieAlertInterval
            if interval > 0 { // Prevent division by zero
                let caloriesSinceLastAlert = calories - lastCalorieAlert
                
                if caloriesSinceLastAlert >= interval {
                    let alertCalories = lastCalorieAlert + interval
                    logManager.logAudioAlert(type: "Calories", triggered: true, pace: pace, reason: "Calorie interval reached: \(Int(alertCalories)) cal")
                    announceCalories(alertCalories)
                    lastCalorieAlert = alertCalories
                }
            }
        }
        
        // Check pace alerts - using simple time-since-last-alert logic
        if profile.paceAlertEnabled {
            let interval = profile.paceAlertInterval
            let timeSinceLastAlert = duration - lastPaceAlert
            
            if timeSinceLastAlert >= interval {
                // Validate pace before announcing
                if pace >= 0.3 && pace <= 30.0 {
                    logManager.logAudioAlert(type: "Pace", triggered: true, pace: pace, reason: "Pace interval reached with valid pace: \(String(format: "%.1f", pace)) min/km")
                    announcePace(pace, unit: profile.units)
                    lastPaceAlert = duration
                } else {
                    logManager.logAudioAlert(type: "Pace", triggered: false, pace: pace, reason: "Pace interval reached but pace out of valid range (0.3-30 min/km): \(String(format: "%.1f", pace))")
                }
            } else {
                // Only log this occasionally to avoid spam
                if Int(duration) % 10 == 0 { // Log every 10 seconds
                    logManager.logAudioAlert(type: "Pace", triggered: false, pace: pace, reason: "Pace interval not reached (need \(String(format: "%.1f", interval - timeSinceLastAlert))s more)")
                }
            }
        }
    }
    
    // MARK: - Alert Announcements
    
    private func announceDistance(_ distance: Double, unit: UnitSystem) {
        let message = formatDistanceMessage(distance, unit: unit)
        speak(message)
    }
    
    private func announceTime(_ duration: TimeInterval) {
        let message = formatTimeMessage(duration)
        speak(message)
    }
    
    private func announceCalories(_ calories: Double) {
        let message = formatCaloriesMessage(calories)
        speak(message)
    }
    
    private func announcePace(_ pace: Double, unit: UnitSystem) {
        let logManager = LogManager.shared
        logManager.log("announcePace called with pace: \(String(format: "%.2f", pace)), unit: \(unit.rawValue)", category: .audio)
        
        // Convert pace based on unit system (input pace is always in min/km)
        let adjustedPace = unit == .imperial ? pace * 1.60934 : pace
        logManager.log("announcePace pace conversion - original: \(String(format: "%.2f", pace)) min/km, adjusted: \(String(format: "%.2f", adjustedPace)) \(unit.paceUnit)", category: .audio)
        
        // Skip announcement if pace is too slow based on unit system
        let threshold: Double
        switch unit {
        case .metric:
            threshold = 30.0  // 30 minutes per km
        case .imperial:
            threshold = 45.0  // 45 minutes per mile
        }
        
        if adjustedPace >= threshold {
            logManager.log("announcePace skipped - pace \(String(format: "%.2f", adjustedPace)) exceeds threshold \(threshold)", category: .audio)
            return // Skip announcement entirely for very slow paces
        }
        
        let message = formatPaceMessage(adjustedPace, unit: unit)
        logManager.log("announcePace calling speak() with message: '\(message)'", category: .audio)
        speak(message)
    }
    
    // MARK: - Achievement Detection
    
    /// Check if distance qualifies for special achievement celebration
    private func shouldCelebrateDistanceAchievement(_ distance: Double, unit: UnitSystem) -> Bool {
        let currentLanguage = LanguageManager.shared.currentLanguage
        
        switch unit {
        case .metric:
            if currentLanguage.hasPrefix("zh") {
                // Chinese users - celebrate round numbers that are culturally significant
                // 3km, 5km, 10km are common running distances in China
                return (distance.truncatingRemainder(dividingBy: 3.0) == 0 && distance >= 3.0) ||
                       (distance.truncatingRemainder(dividingBy: 5.0) == 0 && distance >= 5.0)
            } else {
                // Western users - celebrate every 5km
                return distance.truncatingRemainder(dividingBy: 5.0) == 0 && distance >= 5.0
            }
        case .imperial:
            // Celebrate every 3 miles
            return distance.truncatingRemainder(dividingBy: 3.0) == 0 && distance >= 3.0
        }
    }
    
    /// Check if calories qualify for special achievement celebration
    private func shouldCelebrateCalorieAchievement(_ calories: Double) -> Bool {
        let currentLanguage = LanguageManager.shared.currentLanguage
        
        if currentLanguage.hasPrefix("zh") {
            // Chinese users prefer round numbers - celebrate every 50 and 100 calories
            return (calories.truncatingRemainder(dividingBy: 50) == 0 && calories >= 50) ||
                   (calories.truncatingRemainder(dividingBy: 100) == 0 && calories >= 100)
        } else {
            // Western users - celebrate every 100 calories
            return calories.truncatingRemainder(dividingBy: 100) == 0 && calories >= 100
        }
    }
    
    // MARK: - Message Formatting
    
    /// Formats a number for speech, showing integer when fractional part is 0
    private func formatNumberForSpeech(_ value: Double) -> String {
        // Check if the fractional part is effectively zero (considering floating-point precision)
        if abs(value - round(value)) < 0.01 {
            return String(format: "%.0f", value)  // Show as integer
        } else {
            return String(format: "%.1f", value)  // Show one decimal place
        }
    }
    
    /// Get localized unit string for distance
    private func getLocalizedUnitString(for distance: Double, unit: UnitSystem) -> String {
        switch unit {
        case .metric:
            return distance == 1.0 ? "audio.unit.km.singular".localized : "audio.unit.km.plural".localized
        case .imperial:
            return distance == 1.0 ? "audio.unit.mile.singular".localized : "audio.unit.mile.plural".localized
        }
    }
    
    private func formatDistanceMessage(_ distance: Double, unit: UnitSystem) -> String {
        guard let profile = userProfile else { return "Distance update" }
        
        let distanceString = formatNumberForSpeech(distance)
        let unitString = getLocalizedUnitString(for: distance, unit: unit)
        
        let messageKey: String
        if profile.smartVoiceEnabled {
            if shouldCelebrateDistanceAchievement(distance, unit: unit) {
                messageKey = "audio.distance.achievement.smart"
            } else {
                // Use random variation for smart voice
                let variation = VoicePromptVariationManager.shared.getRandomVariation(for: "distance")
                messageKey = "audio.distance.smart.\(variation)"
            }
        } else {
            messageKey = "audio.distance.standard"
        }
        
        return String(format: messageKey.localized, distanceString, unitString)
    }
    
    private func formatTimeMessage(_ duration: TimeInterval) -> String {
        guard let profile = userProfile else { return "Time update" }
        
        let timeString = formatTimeForSpeech(duration)
        
        let messageKey: String
        if profile.smartVoiceEnabled {
            // Use random variation for smart voice
            let variation = VoicePromptVariationManager.shared.getRandomVariation(for: "time")
            messageKey = "audio.time.smart.\(variation)"
        } else {
            messageKey = "audio.time.standard"
        }
        
        return String(format: messageKey.localized, timeString)
    }
    
    private func formatCaloriesMessage(_ calories: Double) -> String {
        guard let profile = userProfile else { return "Calorie update" }
        
        let caloriesString = formatNumberForSpeech(calories)
        
        let messageKey: String
        if profile.smartVoiceEnabled {
            if shouldCelebrateCalorieAchievement(calories) {
                messageKey = "audio.calories.achievement.smart"
            } else {
                // Use random variation for smart voice
                let variation = VoicePromptVariationManager.shared.getRandomVariation(for: "calories")
                messageKey = "audio.calories.smart.\(variation)"
            }
        } else {
            messageKey = "audio.calories.standard"
        }
        
        return String(format: messageKey.localized, caloriesString)
    }
    
    private func formatPaceMessage(_ pace: Double, unit: UnitSystem) -> String {
        guard let profile = userProfile else { return "Pace update" }
        
        let paceString = formatPaceForSpeech(pace, unit: unit)
        
        let messageKey: String
        if profile.smartVoiceEnabled {
            // Use random variation for smart voice
            let variation = VoicePromptVariationManager.shared.getRandomVariation(for: "pace")
            messageKey = "audio.pace.smart.\(variation)"
        } else {
            messageKey = "audio.pace.standard"
        }
        
        return String(format: messageKey.localized, paceString)
    }
    
    /// Format time duration for speech
    private func formatTimeForSpeech(_ duration: TimeInterval) -> String {
        let minutes = Int(duration / 60)
        if minutes < 60 {
            return minutes == 1 ? "audio.time.minute.singular".localized : 
                   String(format: "audio.time.minutes".localized, minutes)
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return hours == 1 ? "audio.time.hour.singular".localized :
                       String(format: "audio.time.hours".localized, hours)
            } else {
                return String(format: "audio.time.hours.minutes".localized, hours, remainingMinutes)
            }
        }
    }
    
    /// Format pace for speech
    private func formatPaceForSpeech(_ pace: Double, unit: UnitSystem) -> String {
        let totalMinutes = pace
        let minutes = Int(totalMinutes)
        let seconds = Int((totalMinutes - Double(minutes)) * 60)
        
        let unitKey = unit == .metric ? "audio.pace.per.km".localized : "audio.pace.per.mile".localized
        
        if minutes == 0 {
            return String(format: "audio.pace.seconds".localized, seconds, unitKey)
        } else if seconds == 0 {
            return String(format: "audio.pace.minutes".localized, minutes, unitKey)
        } else {
            return String(format: "audio.pace.minutes.seconds".localized, minutes, seconds, unitKey)
        }
    }
    
    // MARK: - Speech Synthesis
    
    private func speak(_ message: String) {
        let logManager = LogManager.shared
        logManager.log("speak() called with message: '\(message)'", category: .audio)
        
        // Don't speak during hibernation
        guard !isHibernating else {
            logManager.log("speak() blocked - hibernating: \(isHibernating)", category: .audio)
            print("AudioAlertManager: Speech blocked during hibernation")
            return
        }
        
        // Don't interrupt existing speech, queue it
        guard !speechSynthesizer.isSpeaking else {
            logManager.log("speak() blocked - speechSynthesizer.isSpeaking: \(speechSynthesizer.isSpeaking)", category: .audio)
            return
        }
        
        // Log audio session state
        let audioSession = AVAudioSession.sharedInstance()
        logManager.log("Audio session state - configured: \(audioSessionConfigured), isActive: \(audioSession.isOtherAudioPlaying ? "inactive (other audio)" : "active"), category: \(audioSession.category.rawValue)", category: .audio)
        
        // Ensure audio session is active before speaking
        ensureAudioSessionActive()
        
        // Enable audio ducking temporarily for speech
        enableAudioDucking()
        
        let utterance = AVSpeechUtterance(string: message)
        
        // Configure speech parameters for running
        let speechRateMultiplier = userProfile?.speechRate ?? AppConstants.defaultAudioPromptRate
        utterance.rate = AVSpeechUtteranceDefaultSpeechRate * speechRateMultiplier
        utterance.volume = currentVolume // Use current volume setting
        
        // Use current language from LanguageManager for voice selection
        let currentLanguage = LanguageManager.shared.currentLanguage
        utterance.voice = getVoiceForLanguage(currentLanguage)
        let voiceInfo = currentLanguage
        
        logManager.log("Speech synthesis starting - volume: \(currentVolume), voice: \(voiceInfo), rate: \(utterance.rate)", category: .audio)
        speechSynthesizer.speak(utterance)
        logManager.log("speechSynthesizer.speak() called successfully", category: .audio)
    }
    
    private func getVoiceForLanguage(_ languageCode: String) -> AVSpeechSynthesisVoice? {
        let logManager = LogManager.shared
        logManager.log("getVoiceForLanguage called with language: \(languageCode)", category: .audio)
        
        // Try to get user's preferred voice first
        if let profile = userProfile,
           let preferredVoiceId = profile.getVoiceForLanguage(languageCode) {
            
            // Check if the preferred voice is available via VoiceManager
            if let voiceInfo = VoiceManager.shared.getVoice(by: preferredVoiceId),
               VoiceManager.shared.isVoiceAvailable(preferredVoiceId) {
                logManager.log("Using user's preferred voice: \(voiceInfo.name) (\(voiceInfo.quality.rawValue))", category: .audio)
                return voiceInfo.voice
            } else {
                logManager.log("User's preferred voice '\(preferredVoiceId)' is no longer available, falling back", category: .audio)
            }
        }
        
        // Fall back to best available voice for the language via VoiceManager
        if let bestVoice = VoiceManager.shared.getBestVoiceForLanguage(languageCode) {
            logManager.log("Using best available voice for \(languageCode): \(bestVoice.name) (\(bestVoice.quality.rawValue))", category: .audio)
            return bestVoice.voice
        }
        
        // Final fallback: use original language mapping for basic system voice
        let voiceLanguageCode: String
        switch languageCode {
        case "ar": voiceLanguageCode = "ar-SA"
        case "zh-Hans": voiceLanguageCode = "zh-CN"
        case "zh-Hant": voiceLanguageCode = "zh-TW"
        case "de": voiceLanguageCode = "de-DE"
        case "en": voiceLanguageCode = "en-US"
        case "fr": voiceLanguageCode = "fr-FR"
        case "id": voiceLanguageCode = "id-ID"
        case "it": voiceLanguageCode = "it-IT"
        case "ja": voiceLanguageCode = "ja-JP"
        case "ko": voiceLanguageCode = "ko-KR"
        case "pt": voiceLanguageCode = "pt-BR"
        case "ru": voiceLanguageCode = "ru-RU"
        case "es": voiceLanguageCode = "es-ES"
        default: voiceLanguageCode = "en-US"
        }
        
        let fallbackVoice = AVSpeechSynthesisVoice(language: voiceLanguageCode) ?? AVSpeechSynthesisVoice(language: "en-US")
        logManager.log("Using fallback system voice for \(voiceLanguageCode)", category: .audio)
        return fallbackVoice
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - AVSpeechSynthesizerDelegate

extension AudioAlertManager: AVSpeechSynthesizerDelegate {
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        // Speech started successfully
        Task { @MainActor in
            LogManager.shared.log("Speech synthesis STARTED: '\(utterance.speechString)'", category: .audio)
        }
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        // Speech finished, restore normal audio mixing
        Task { @MainActor in
            LogManager.shared.log("Speech synthesis FINISHED: '\(utterance.speechString)'", category: .audio)
            self.disableAudioDucking()
        }
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        // Speech was cancelled, restore normal audio mixing
        Task { @MainActor in
            LogManager.shared.log("Speech synthesis CANCELLED: '\(utterance.speechString)'", category: .audio)
            self.disableAudioDucking()
        }
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didPause utterance: AVSpeechUtterance) {
        Task { @MainActor in
            LogManager.shared.log("Speech synthesis PAUSED: '\(utterance.speechString)'", category: .audio)
        }
    }
    
    nonisolated func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didContinue utterance: AVSpeechUtterance) {
        Task { @MainActor in
            LogManager.shared.log("Speech synthesis CONTINUED: '\(utterance.speechString)'", category: .audio)
        }
    }
}
