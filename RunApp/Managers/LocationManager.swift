@preconcurrency import CoreLocation
import HealthKit
import SwiftUI
import UIKit

@MainActor
class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
  // Create a shared singleton instance
  static let shared = LocationManager()

  let manager = CLLocationManager()
  @Published var location: CLLocation?
  @Published var heading: CLHeading?
  @Published var course: Double?
  @Published var orientation: Double?
  @Published var isTracking = false
  @Published private(set) var isInWorkoutMode = false

  // Properties for tracking permission state
  @Published var locationPermissionStatus: CLAuthorizationStatus = .notDetermined
  @Published var needsAlwaysPermission = false

  // MARK: - Location Buffer System
  private var locationBuffer: [CLLocation] = []
  private var isProcessingBuffer = false

  // Buffer safety limits to prevent overflow
  private let maxBufferSize = 150  // Increased to handle 5-second saves
  private let maxPreloadedSize = 25  // Slightly increased for safety

  // MARK: - Background Accumulation Buffer (Performance Optimization)
  // BACKGROUND ACCUMULATION: Filtered locations waiting for foreground processing
  private var backgroundLocationBuffer: [CLLocation] = []
  private let maxBackgroundBufferSize = 20
  private var isProcessingBackgroundBuffer = false
  private var emergencyProcessingOccurred = false  // Track if emergency processing happened in background

  // Processing thresholds for hybrid approach
  private let BULK_PROCESSING_THRESHOLD = 10  // Switch to bulk for 10+ locations
  private let REAL_TIME_PROCESSING_MAX = 9  // Individual processing for ≤9 locations

  // Location validation and filtering
  private var lastProcessedLocation: CLLocation?
  private var lastLocationProcessTime: Date?
  // PHASE 1 OPTIMIZATION: Removed locationUpdateQueue - processing directly to locationBuffer
  // private var locationUpdateQueue: [CLLocation] = []
  // private var isProcessingLocationQueue = false
  // private let maxQueueSize = 20
  private let minProcessingInterval: TimeInterval = 0.5  // Minimum time between processing batches

  // MARK: - Smart Adaptive Location Filtering System

  // Speed Level Classification
  enum SpeedLevel {
    case stationary  // 0.0 - 0.5 m/s  (0 - 1.8 km/h)   - Stopped/Minimal movement
    case slowest  // 0.5 - 1.5 m/s  (1.8 - 5.4 km/h) - Walking/Hiking
    case slow  // 1.5 - 2.5 m/s  (5.4 - 9 km/h)   - Fast walking/Light jogging
    case medium  // 2.5 - 4.0 m/s  (9 - 14.4 km/h)  - Running/Slow biking
    case fast  // 4.0 - 6.5 m/s  (14.4 - 23.4 km/h) - Fast running/Moderate biking
    case fastest  // 6.5+ m/s       (23.4+ km/h)     - Fast biking/Racing
  }

  // Filtering state variables
  private var locationFilterCounter = 0
  private var lastFilteredTime: Date?

  // Dynamic filtering parameters (adaptive based on speed)
  private var conservativeFilterInterval: TimeInterval = 5.0  // Dynamic
  private var conservativeFilterCount = 4  // Dynamic

  // Speed tracking system (RAW GPS BATCH ANALYSIS)
  private var rawGpsLocationsForSpeedAnalysis: [CLLocation] = []  // MAX 5 elements
  private let rawGpsSpeedBatchSize = 5
  private var currentSpeedLevel: SpeedLevel = .medium
  private var lastSpeedLevelUpdate: Date?
  private let speedLevelHysteresis: TimeInterval = 5.0  // Prevent rapid switching

  // Statistics for monitoring filtering effectiveness
  private var totalLocationsReceived = 0
  private var totalLocationsKept = 0

  private var lastCourseUpdate: Date?
  private var isInBackground = false
  private var applicationStateObserver: Any?
  private let courseUpdateInterval: TimeInterval = 5.0  // Update course every 5 seconds
  private let minimumSpeed: Double = 0.5  // Minimum speed in m/s for course to be valid

  // Phase 3: Background task management removed - HealthKit provides background priority
  private var isProcessingLocation = false

  // Simplified monitoring for location health
  private var lastLocationTimestamp: Date?

  // MARK: - Location Preloading for Quick Start
  private var preloadedLocations: [CLLocation] = []
  private var isPreloading = false

  override init() {
    super.init()
    checkLocationPermission()
    configureLocationManager()

    // Enable battery monitoring for adaptive behavior
    // Use Task to ensure main actor context
    Task { @MainActor in
      UIDevice.current.isBatteryMonitoringEnabled = true
    }
  }

  // Check if we have the necessary location permissions
  func checkLocationPermission() {
    let status = manager.authorizationStatus
    locationPermissionStatus = status

    // Check if we need to request or remind about "Always" permission
    if status != .authorizedAlways {
      print(
        "LocationManager: 'Always Allow' permission not granted, current status: \(status.rawValue)"
      )
      needsAlwaysPermission = true
    } else {
      needsAlwaysPermission = false
    }
  }

  private func configureLocationManager() {
    manager.delegate = self
    manager.desiredAccuracy = kCLLocationAccuracyBest
    manager.activityType = .fitness
    manager.distanceFilter = 2  // Update more frequently (every 2 meters)

    // Critical settings for background location updates
    manager.allowsBackgroundLocationUpdates = true
    manager.pausesLocationUpdatesAutomatically = false
    manager.showsBackgroundLocationIndicator = true

    if CLLocationManager.headingAvailable() {
      manager.headingFilter = 5  // Update every 5 degrees
    }

    // Get initial authorization status
    locationPermissionStatus = manager.authorizationStatus

    // Observe app state changes
    setupAppStateObserver()
  }

  deinit {
    print("LocationManager: deinit called - cleaning up resources")

    // Remove notification observers - this is safe in deinit
    if let observer = applicationStateObserver {
      NotificationCenter.default.removeObserver(observer)
      applicationStateObserver = nil
    }

    // Clean up location data arrays
    locationBuffer.removeAll()
    // PHASE 1 OPTIMIZATION: locationUpdateQueue removed
    preloadedLocations.removeAll()

    // Disable battery monitoring - use Task to ensure main actor context
    Task { @MainActor in
      UIDevice.current.isBatteryMonitoringEnabled = false
    }

    print("LocationManager: deinit completed - all resources cleaned up")
  }

  private func setupAppStateObserver() {
    applicationStateObserver = NotificationCenter.default.addObserver(
      forName: UIApplication.didEnterBackgroundNotification,
      object: nil,
      queue: .main
    ) { [weak self] _ in
      Task { @MainActor in
        self?.handleAppStateChange(isBackground: true)
      }
    }

    NotificationCenter.default.addObserver(
      forName: UIApplication.willEnterForegroundNotification,
      object: nil,
      queue: .main
    ) { [weak self] _ in
      Task { @MainActor in
        self?.handleAppStateChange(isBackground: false)
      }
    }

    // Add observer for app termination to clean up properly
    NotificationCenter.default.addObserver(
      forName: UIApplication.willTerminateNotification,
      object: nil,
      queue: .main
    ) { [weak self] _ in
      Task { @MainActor in
        self?.stopTracking()
      }
    }
  }

  func startTracking(isWorkout: Bool = false) {
    isInWorkoutMode = isWorkout

    if isWorkout {
      // Initialize RouteManager for workout tracking
      RouteManager.shared.startTracking()

      // Set up location manager for workout/always mode
      manager.allowsBackgroundLocationUpdates = true
      manager.pausesLocationUpdatesAutomatically = false
      manager.showsBackgroundLocationIndicator = true

      // Configure for always-on location tracking
      manager.desiredAccuracy = kCLLocationAccuracyBest

      // Request "Always" authorization explicitly
      manager.requestAlwaysAuthorization()

      print(
        "LocationManager: Starting workout mode with ALWAYS authorization (HealthKit provides background priority)"
      )
    } else {
      manager.allowsBackgroundLocationUpdates = false
      manager.pausesLocationUpdatesAutomatically = true
      manager.requestWhenInUseAuthorization()
    }

    updateLocationTracking()
    isTracking = true

    print("LocationManager: Started tracking in \(isWorkout ? "workout" : "standard") mode")
  }

  func pauseTracking() {
    if !isInWorkoutMode {
      manager.stopUpdatingLocation()
      if CLLocationManager.headingAvailable() {
        manager.stopUpdatingHeading()
      }
      isTracking = false
    } else {
      // In workout mode, maintain location tracking but mark as paused in RouteManager
      RouteManager.shared.pauseTracking()
      // Keep isTracking true to continue receiving location updates
      // This allows drawing light blue line for paused segments
      isTracking = true
      print("LocationManager: Activity paused but continuing location updates for pause tracking")
    }
    print("LocationManager: Paused tracking")
  }

  func stopTracking() {
    if isInWorkoutMode {
      RouteManager.shared.stopTracking()
    }

    isInWorkoutMode = false
    manager.allowsBackgroundLocationUpdates = false
    manager.stopUpdatingLocation()
    if CLLocationManager.headingAvailable() {
      manager.stopUpdatingHeading()
    }
    isTracking = false
    clearLocationBuffer()
    print("LocationManager: Stopped tracking")
  }

  func endWorkoutMode() {
    // End workout mode but keep tracking in standard mode
    if isInWorkoutMode {
      RouteManager.shared.stopTracking()
    }

    isInWorkoutMode = false
    // Configure for standard tracking
    manager.allowsBackgroundLocationUpdates = false
    manager.pausesLocationUpdatesAutomatically = true

    // Keep tracking active but in standard mode
    updateLocationTracking()

    print("LocationManager: Ended workout mode but continuing standard tracking")
  }

  func resumeTracking() {
    // Don't call RouteManager.startTracking() here, it will be handled by the ContentView

    if isInWorkoutMode {
      // Set up location manager for workout/always mode
      manager.allowsBackgroundLocationUpdates = true
      manager.pausesLocationUpdatesAutomatically = false
      manager.showsBackgroundLocationIndicator = true

      // Configure for always-on location tracking
      manager.desiredAccuracy = kCLLocationAccuracyBest

      // Request "Always" authorization explicitly
      manager.requestAlwaysAuthorization()

      print(
        "LocationManager: Resuming workout mode with ALWAYS authorization (HealthKit provides background priority)"
      )
    } else {
      manager.allowsBackgroundLocationUpdates = false
      manager.pausesLocationUpdatesAutomatically = true
      manager.requestWhenInUseAuthorization()
    }

    updateLocationTracking()
    isTracking = true

    print("LocationManager: Resumed tracking in \(isInWorkoutMode ? "workout" : "standard") mode")
  }

  private func handleAppStateChange(isBackground: Bool) {
    self.isInBackground = isBackground

    print("LocationManager: App state changed to \(isBackground ? "background" : "foreground")")

    if isBackground {
      if isInWorkoutMode && isTracking {
        // Critical: Force location updates to continue in background (HealthKit provides priority)
        print("LocationManager: Forcing background updates for workout")

        // Stop updates before reconfiguring
        manager.stopUpdatingLocation()
        if CLLocationManager.headingAvailable() {
          manager.stopUpdatingHeading()
        }

        // Set up location manager with critical settings for background
        manager.desiredAccuracy = kCLLocationAccuracyBest
        manager.distanceFilter = 2  // meters
        manager.allowsBackgroundLocationUpdates = true
        manager.pausesLocationUpdatesAutomatically = false
        manager.showsBackgroundLocationIndicator = true

        // Add a slight delay to ensure proper setup
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
          guard let self = self else { return }
          // Ensure buffer is ready to receive background updates
          self.clearLocationBuffer()
          self.manager.startUpdatingLocation()
          if CLLocationManager.headingAvailable() {
            self.manager.startUpdatingHeading()
          }
        }
      } else {
        // When NOT in workout mode and app goes to background, immediately stop location services
        print("LocationManager: Stopping location services to save battery (not in workout mode)")
        Task.detached { @MainActor in
          self.manager.stopUpdatingLocation()
          if CLLocationManager.headingAvailable() {
            self.manager.stopUpdatingHeading()
          }
          // No timer cleanup needed - using efficient distance-based updates
        }
      }
    } else {
      // App came to foreground
      // No timer cleanup needed - using efficient distance-based updates

      // Check if emergency processing occurred in background and force UI refresh
      if emergencyProcessingOccurred {
        print("LocationManager: Emergency processing occurred in background - forcing UI refresh")
        // Force trigger @Published property update by reassigning current location
        if let currentLocation = location {
          location = currentLocation
        }
        emergencyProcessingOccurred = false  // Reset flag
      }

      // Process any pending locations in the buffer
      if isInWorkoutMode && isTracking && !locationBuffer.isEmpty {
        print(
          "LocationManager: Processing \(locationBuffer.count) buffered locations after returning to foreground"
        )
        processLocationBuffer()
      }

      // NEW: Process background accumulation buffer
      if isInWorkoutMode && isTracking && !backgroundLocationBuffer.isEmpty {
        print(
          "LocationManager: Processing \(backgroundLocationBuffer.count) background accumulated locations"
        )
        processBackgroundAccumulationBuffer()
      }

      // Resume tracking if we were tracking before
      if isTracking {
        print("LocationManager: Resuming tracking in foreground")

        // Re-apply correct settings based on mode
        if isInWorkoutMode {
          manager.desiredAccuracy = kCLLocationAccuracyBest
          manager.distanceFilter = 2  // meters
        } else {
          manager.desiredAccuracy = kCLLocationAccuracyHundredMeters
          manager.distanceFilter = kCLDistanceFilterNone
        }

        updateLocationTracking()
      }
    }
  }

  private func updateLocationTracking() {
    if isInWorkoutMode {
      // Ensure we have proper configuration for background tracking
      manager.desiredAccuracy = kCLLocationAccuracyBest
      manager.distanceFilter = 2  // meters
      manager.allowsBackgroundLocationUpdates = true
      manager.pausesLocationUpdatesAutomatically = false
      manager.showsBackgroundLocationIndicator = true

      // Always track in workout mode, regardless of background state
      print(
        "LocationManager: Starting location updates in workout mode (background: \(isInBackground))"
      )
      manager.startUpdatingLocation()
      if CLLocationManager.headingAvailable() {
        manager.startUpdatingHeading()
      }

      // HealthKit provides background priority automatically
    } else {
      // For non-workout mode: Use efficient distance-based updates
      if !isInBackground {
        // Only track when app is in foreground
        print(
          "LocationManager: Starting efficient location updates in standard mode (foreground only)")

        // Configure for efficient standard mode - MAJOR CPU SAVINGS
        manager.desiredAccuracy = kCLLocationAccuracyHundredMeters  // Lower accuracy to save battery
        manager.distanceFilter = 50.0  // Update every 50 meters instead of timer-based updates
        manager.allowsBackgroundLocationUpdates = false
        manager.pausesLocationUpdatesAutomatically = true  // Let iOS optimize

        // Start efficient location updates - no timer needed!
        manager.startUpdatingLocation()

        if CLLocationManager.headingAvailable() {
          manager.startUpdatingHeading()
        }

        print("LocationManager: Using distance-based updates (50m) - no timer overhead")
      } else {
        // Explicitly stop tracking when app is in background and not in workout mode
        print("LocationManager: No location updates in standard mode while in background")
        manager.stopUpdatingLocation()
        if CLLocationManager.headingAvailable() {
          manager.stopUpdatingHeading()
        }
      }
    }
  }

  // MARK: - Phase 3: Background task management removed - HealthKit provides system-level priority

  private func updateHybridOrientation() {
    // If course is available and speed is sufficient, prefer course
    if let course = self.course,
      let location = self.location,
      location.speed >= minimumSpeed
    {
      self.orientation = course
    }
    // Otherwise fallback to magnetic heading
    else if let heading = self.heading {
      self.orientation = heading.magneticHeading
    }
  }

  // Old timer-based system removed - now using efficient distance-based updates for non-workout mode
  // This eliminates constant CPU usage from timers in standard tracking mode

  // MARK: - Location Validation

  /// PHASE 3: Enhanced validation specifically for slow movement scenarios
  /// Only applies stricter validation for speeds near the 18 min/km threshold
  private func enhanceSlowMovementValidation(_ location: CLLocation) -> Bool {
    // Only apply enhanced validation for slow speeds (near threshold)
    guard location.speed < 1 else { return true }  // Normal validation for faster speeds

    // Stricter accuracy requirements for slow movement
    guard location.horizontalAccuracy < 10.0 else {
      print(
        "LocationManager: FILTERED slow movement - poor accuracy (\(location.horizontalAccuracy)m)")
      return false
    }

    // Additional movement consistency check
    guard let lastLocation = lastProcessedLocation else { return true }

    let distance = location.distance(from: lastLocation)
    let timeDiff = location.timestamp.timeIntervalSince(lastLocation.timestamp)

    // For slow movement, be more permissive with distance but validate direction consistency
    if timeDiff > 0 {
      let calculatedSpeed = distance / timeDiff

      // Reject impossible slow movement (teleportation at slow speeds)
      if calculatedSpeed > 5.0 && location.speed < 1.0 {
        print(
          "LocationManager: FILTERED slow movement - inconsistent speed (GPS: \(location.speed), calc: \(calculatedSpeed))"
        )
        return false
      }
    }

    return true
  }

  /// Validates if a location update should be processed based on quality criteria
  /// PHASE 3: Enhanced with slow-movement specific validation
  private func isValidLocation(_ location: CLLocation) -> Bool {
    // Basic accuracy check
    guard location.horizontalAccuracy <= 20 else {
      return false
    }

    // Check if location is too old (more than 10 seconds old)
    let now = Date()
    let locationAge = now.timeIntervalSince(location.timestamp)
    guard locationAge < 10 else {
      return false
    }

    // PHASE 3: Apply enhanced validation for slow movement
    guard enhanceSlowMovementValidation(location) else {
      return false
    }

    // If this is the first location, accept it
    guard let lastLocation = lastProcessedLocation else {
      return true
    }

    // Calculate distance and time difference
    let distance = location.distance(from: lastLocation)
    let timeDiff = location.timestamp.timeIntervalSince(lastLocation.timestamp)

    // Skip if time difference is negative or zero (out of order updates)
    guard timeDiff > 0 else {
      return false
    }

    // Dynamic distance filter based on speed
    let speed = max(location.speed, 0.5)  // Use at least 0.5 m/s to avoid division by zero

    // Calculate minimum distance based on speed and activity state
    let minDistance: CLLocationDistance
    if isInWorkoutMode {
      // For workout mode: more precise filtering
      minDistance = speed < 1.0 ? 2.0 : min(speed * 1.0, 10.0)
    } else {
      // For non-workout mode: more aggressive filtering
      minDistance = speed < 1.0 ? 5.0 : min(speed * 2.0, 20.0)
    }

    // Maximum reasonable speed (about 216 km/h)
    let maxSpeed: CLLocationSpeed = 60.0  // m/s

    // Check if the location represents reasonable movement
    let calculatedSpeed = distance / timeDiff
    let isReasonableSpeed = location.speed <= maxSpeed && calculatedSpeed <= maxSpeed

    // Accept the location if it's far enough from the previous one and has reasonable speed
    return distance >= minDistance && isReasonableSpeed
  }

  // MARK: - Location Buffer System

  // PHASE 1 OPTIMIZATION: bufferLocation() method deprecated - using processValidatedLocation() directly
  private func bufferLocation(_ location: CLLocation) {
    // PHASE 1: Replaced addToLocationQueue with direct processing
    processValidatedLocation(location)

    // Capture the state for log messages
    let currentBufferCount = locationBuffer.count
    let currentlyInBackground = isInBackground

    // Log buffer size occasionally (every 10th location or when size exceeds 20)
    if currentBufferCount % 10 == 0 || currentBufferCount > 20 {
      print(
        "LocationManager: Buffer size is now \(currentBufferCount) locations"
          + (currentlyInBackground ? " (in background)" : " (in foreground)"))
    }

    // Note: processLocationBuffer() is now called directly from processValidatedLocation()
  }

  /// PHASE 1 OPTIMIZATION: Unified validation and filtering function
  /// Consolidates isValidLocation() checks and smart adaptive filtering logic
  private func validateAndFilterLocation(_ location: CLLocation) -> CLLocation? {
    // --- Part 1: Content from existing isValidLocation() ---
    // Basic accuracy check
    guard location.horizontalAccuracy <= 20 else { return nil }

    // Check if location is too old (more than 10 seconds old)
    let now = Date()
    let locationAge = now.timeIntervalSince(location.timestamp)
    guard locationAge < 10 else { return nil }

    // PHASE 3: Apply enhanced validation for slow movement
    guard enhanceSlowMovementValidation(location) else { return nil }

    // If this is the first location, continue to adaptive filtering
    if let lastLocation = lastProcessedLocation {
      // Calculate distance and time difference
      let distance = location.distance(from: lastLocation)
      let timeDiff = location.timestamp.timeIntervalSince(lastLocation.timestamp)

      // Skip if time difference is negative or zero (out of order updates)
      guard timeDiff > 0 else { return nil }

      // Dynamic distance filter based on speed
      let speed = max(location.speed, 0.5)  // Use at least 0.5 m/s to avoid division by zero

      // Calculate minimum distance based on speed and activity state
      let minDistance: CLLocationDistance
      if isInWorkoutMode {
        // For workout mode: more precise filtering
        minDistance = speed < 1.0 ? 2.0 : min(speed * 1.0, 10.0)
      } else {
        // For non-workout mode: more aggressive filtering
        minDistance = speed < 1.0 ? 5.0 : min(speed * 2.0, 20.0)
      }

      // Maximum reasonable speed (about 216 km/h)
      let maxSpeed: CLLocationSpeed = 60.0  // m/s

      // Check if the location represents reasonable movement
      let calculatedSpeed = distance / timeDiff
      let isReasonableSpeed = location.speed <= maxSpeed && calculatedSpeed <= maxSpeed

      // Continue to adaptive filtering if distance and speed are reasonable
      guard distance >= minDistance && isReasonableSpeed else { return nil }
    }

    // --- Part 2: Content from existing adaptive filtering logic ---
    totalLocationsReceived += 1

    // RAW GPS SPEED ANALYSIS: Add EVERY valid location to batch for speed analysis
    addRawLocationForSpeedAnalysis(location)

    // SMART ADAPTIVE FILTERING: Dynamic parameters based on speed level (whichever comes first)
    locationFilterCounter += 1

    // Check conditions after incrementing
    let shouldKeepByCount = (locationFilterCounter % conservativeFilterCount == 0)
    let shouldKeepByTime =
      lastFilteredTime == nil
      || now.timeIntervalSince(lastFilteredTime!) >= conservativeFilterInterval

    // Keep location if either condition is met
    if shouldKeepByCount || shouldKeepByTime {
      // Track locations kept for statistics
      totalLocationsKept += 1

      // Reset counter to 0 and timer when we keep a location
      locationFilterCounter = 0
      lastFilteredTime = now

      print(
        "LocationManager: KEPT location - count: \(shouldKeepByCount), time: \(shouldKeepByTime), accuracy: \(location.horizontalAccuracy)m, speed: \(String(format: "%.2f", location.speed))m/s, level: \(currentSpeedLevel), stats: \(totalLocationsKept)/\(totalLocationsReceived)"
      )

      return location
    } else {
      // Filtered out by adaptive filter
      return nil
    }
  }

  /// PHASE 1 OPTIMIZATION: Direct location processing using unified validation
  /// Replaces addToLocationQueue() with direct processing to locationBuffer
  private func processValidatedLocation(_ location: CLLocation) {
    // Use the new unified validation and filtering function
    guard let validatedLocation = validateAndFilterLocation(location) else {
      return  // Location was filtered out
    }

    // Record processing time for rate limiting
    lastLocationProcessTime = Date()

    // Add directly to locationBuffer (skipping the queue)
    locationBuffer.append(validatedLocation)

    // Implement circular buffer to prevent overflow
    if locationBuffer.count > maxBufferSize {
      locationBuffer.removeFirst()  // Remove only oldest element
      print("LocationManager: Buffer reached limit (\(maxBufferSize)), removing oldest location")
    }

    // Update last processed location for subsequent validation checks
    lastProcessedLocation = validatedLocation

    // Process the buffer immediately (since we already filtered conservatively)
    processLocationBuffer()
  }
  // PHASE 1 OPTIMIZATION: processLocationQueue() removed - processing directly to locationBuffer

  private func processLocationBuffer() {
    guard !isProcessingBuffer, !locationBuffer.isEmpty else { return }

    isProcessingBuffer = true

    // Get the most recent location for immediate UI updates
    let mostRecentLocation = locationBuffer.last

    // Log buffer status for monitoring
    let bufferCount = locationBuffer.count
    if bufferCount > maxBufferSize * 3 / 4 {
      print("LocationManager: Buffer approaching limit (\(bufferCount)/\(maxBufferSize))")
    }

    // Process locations through RouteManager if in workout mode
    if isInWorkoutMode {
      // Process locations in chronological order
      let sortedLocations = locationBuffer.sorted { $0.timestamp < $1.timestamp }

      // Send filtered locations to HealthKit (unified high-quality data pipeline)
      Task { @MainActor in
        for location in sortedLocations {
          HealthKitManager.shared.addLocationToWorkout(location)
        }
      }

      // HYBRID APPROACH: Smart decision between bulk and individual processing
      if sortedLocations.count > BULK_PROCESSING_THRESHOLD {
        // BULK PROCESSING for large batches (pause-resume scenarios)
        Task.detached(priority: .utility) {
          await RouteManager.shared.processBulkLocationUpdate(sortedLocations)

          await MainActor.run {
            self.clearProcessedBufferLocations()
            print(
              "LocationManager: Processed \(sortedLocations.count) locations using BULK processing")
          }
        }
      } else {
        // INDIVIDUAL PROCESSING for small batches (real-time scenarios)
        Task.detached(priority: .utility) {
          var processedCount = 0
          for location in sortedLocations {
            // Process route updates on background thread
            await RouteManager.shared.processLocationUpdateAsync(location)
            processedCount += 1
          }

          // Only update UI state on main thread after processing completes
          await MainActor.run {
            if processedCount == sortedLocations.count {
              self.clearProcessedBufferLocations()
              print(
                "LocationManager: Processed \(sortedLocations.count) locations using INDIVIDUAL processing"
              )
            } else {
              print("LocationManager: Warning - Not all locations processed successfully")
            }
          }
        }
      }
    } else {
      // For non-workout mode, safe to clear immediately
      clearProcessedBufferLocations()
    }

    // Update the published location property with the most recent location (immediate)
    if let location = mostRecentLocation {
      self.location = location
    }

    isProcessingBuffer = false
  }

  /// Safely clear processed buffer locations
  private func clearProcessedBufferLocations() {
    locationBuffer.removeAll()
  }

  // Clear all location data when stopping tracking
  private func clearLocationBuffer() {
    locationBuffer.removeAll()
    // PHASE 1 OPTIMIZATION: locationUpdateQueue removed
    backgroundLocationBuffer.removeAll()
    isProcessingBuffer = false
    // PHASE 1 OPTIMIZATION: isProcessingLocationQueue removed
    isProcessingBackgroundBuffer = false
    emergencyProcessingOccurred = false  // Reset emergency processing flag
    lastProcessedLocation = nil
    lastLocationProcessTime = nil

    // IMPORTANT: Reset filter state to avoid stale filtering decisions
    locationFilterCounter = 0
    lastFilteredTime = nil

    // Reset adaptive filtering state
    resetAdaptiveFilteringState()

    // Reset filtering statistics for next workout
    totalLocationsReceived = 0
    totalLocationsKept = 0

    print(
      "LocationManager: Cleared all buffers including background buffer, reset filter state and statistics"
    )
  }

  // MARK: - Smart Adaptive Filtering System

  /// Update filtering parameters based on speed level
  private func updateFilteringParameters(for speedLevel: SpeedLevel) {
    switch speedLevel {
    case .stationary:
      conservativeFilterInterval = 16.0
      conservativeFilterCount = 10
    case .slowest:
      conservativeFilterInterval = 12.0
      conservativeFilterCount = 8
    case .slow:
      conservativeFilterInterval = 8.0
      conservativeFilterCount = 6
    case .medium:
      conservativeFilterInterval = 5.0
      conservativeFilterCount = 4
    case .fast:
      conservativeFilterInterval = 3.0
      conservativeFilterCount = 3
    case .fastest:
      conservativeFilterInterval = 2.0
      conservativeFilterCount = 2
    }

    print(
      "LocationManager: Updated filter parameters for \(speedLevel) - interval: \(conservativeFilterInterval)s, count: \(conservativeFilterCount)"
    )
  }

  /// Calculate mean speed from raw GPS batch (5 locations)
  private func calculateMeanSpeedFromRawBatch() -> Double {
    guard rawGpsLocationsForSpeedAnalysis.count == rawGpsSpeedBatchSize else { return 0.0 }

    var validSpeeds: [Double] = []

    // Extract valid speeds from raw GPS batch only
    for location in rawGpsLocationsForSpeedAnalysis {
      let speed = location.speed
      if speed >= 0 {  // Valid speed from GPS
        validSpeeds.append(speed)
      }
    }

    // Must have at least 3 valid speeds out of 5 to calculate reliable mean
    guard validSpeeds.count >= 3 else { return 0.0 }

    // Calculate arithmetic mean
    let sum = validSpeeds.reduce(0, +)
    return sum / Double(validSpeeds.count)
  }

  /// Determine speed level from mean speed
  private func determineSpeedLevel(from meanSpeed: Double) -> SpeedLevel {
    switch meanSpeed {
    case 0.0..<0.5: return .stationary  // Stopped/Minimal movement
    case 0.5..<1.5: return .slowest  // Walking/Hiking
    case 1.5..<2.5: return .slow  // Fast walking/Light jogging
    case 2.5..<4.0: return .medium  // Running/Slow biking
    case 4.0..<6.5: return .fast  // Fast running/Moderate biking
    default: return .fastest  // Fast biking/Racing
    }
  }

  /// Add raw GPS location to batch for speed analysis (ALL received locations)
  private func addRawLocationForSpeedAnalysis(_ location: CLLocation) {
    // Add to raw GPS batch
    rawGpsLocationsForSpeedAnalysis.append(location)

    // When we have a full batch of 5, calculate mean and update level
    if rawGpsLocationsForSpeedAnalysis.count == rawGpsSpeedBatchSize {
      let meanSpeed = calculateMeanSpeedFromRawBatch()
      let newSpeedLevel = determineSpeedLevel(from: meanSpeed)
      updateSpeedLevelIfNeeded(to: newSpeedLevel, meanSpeed: meanSpeed)

      // Clear batch and start fresh for next 5 locations
      rawGpsLocationsForSpeedAnalysis.removeAll()

      print(
        "LocationManager: Processed raw GPS batch, mean speed: \(String(format: "%.2f", meanSpeed)) m/s"
      )
    }
  }

  /// Update speed level with hysteresis to prevent rapid switching
  private func updateSpeedLevelIfNeeded(to newSpeedLevel: SpeedLevel, meanSpeed: Double) {
    let now = Date()

    // Apply hysteresis to prevent rapid switching
    if let lastUpdate = lastSpeedLevelUpdate,
      now.timeIntervalSince(lastUpdate) < speedLevelHysteresis
    {
      return
    }

    if newSpeedLevel != currentSpeedLevel {
      let oldLevel = currentSpeedLevel
      currentSpeedLevel = newSpeedLevel
      updateFilteringParameters(for: newSpeedLevel)
      lastSpeedLevelUpdate = now

      print(
        "LocationManager: Speed level changed from \(oldLevel) to \(newSpeedLevel) (mean: \(String(format: "%.2f", meanSpeed)) m/s)"
      )
    }
  }

  /// Reset adaptive filtering state
  private func resetAdaptiveFilteringState() {
    rawGpsLocationsForSpeedAnalysis.removeAll()
    currentSpeedLevel = .medium
    lastSpeedLevelUpdate = nil

    // Reset to medium defaults
    updateFilteringParameters(for: .medium)

    print("LocationManager: Reset adaptive filtering state to medium defaults")
  }

  // MARK: - Background Accumulation Buffer Methods (Performance Optimization)

  /// Adds location to background accumulation buffer, deferring RouteManager processing
  /// Uses EXISTING filtering logic - reuse isValidLocation() and adaptive filtering
  private func addToBackgroundAccumulationBuffer(_ location: CLLocation) {
    // Use EXISTING filtering logic
    guard isValidLocation(location) else { return }

    // Apply existing smart adaptive filtering
    totalLocationsReceived += 1
    addRawLocationForSpeedAnalysis(location)  // Keep speed analysis running

    // Apply same conservative filtering as addToLocationQueue()
    let now = Date()
    locationFilterCounter += 1

    let shouldKeepByCount = (locationFilterCounter % conservativeFilterCount == 0)
    let shouldKeepByTime =
      lastFilteredTime == nil
      || now.timeIntervalSince(lastFilteredTime!) >= conservativeFilterInterval

    if shouldKeepByCount || shouldKeepByTime {
      totalLocationsKept += 1
      locationFilterCounter = 0
      lastFilteredTime = now

      // Add to background buffer instead of processing queue
      backgroundLocationBuffer.append(location)

      // Update last processed location for filtering continuity
      lastProcessedLocation = location

      print(
        "LocationManager: Accumulated filtered location in background buffer (UI processing deferred) - accuracy: \(location.horizontalAccuracy)m, speed: \(String(format: "%.2f", location.speed))m/s, buffer: \(backgroundLocationBuffer.count)"
      )

      // Overflow protection - emergency processing if buffer gets too large
      if backgroundLocationBuffer.count >= maxBackgroundBufferSize {
        print(
          "LocationManager: Background buffer overflow (\(backgroundLocationBuffer.count)) - emergency processing"
        )
        processBackgroundBufferEmergency()
      }
    } else {
      print(
        "LocationManager: FILTERED OUT location in background - counter: \(locationFilterCounter), accuracy: \(location.horizontalAccuracy)m"
      )
    }
  }

  /// Emergency processing when background buffer reaches limit
  private func processBackgroundBufferEmergency() {
    guard !backgroundLocationBuffer.isEmpty else { return }

    let emergencyLocations = backgroundLocationBuffer
    backgroundLocationBuffer.removeAll()

    print(
      "LocationManager: Emergency batch processing \(emergencyLocations.count) locations with COMPLETE UI update"
    )

    // BATCH UI UPDATE: Process all locations and update UI at once
    Task.detached(priority: .utility) {
      // 1. Process all route data in background thread
      await RouteManager.shared.processBulkLocationUpdate(emergencyLocations)

      // 2. Update UI on main thread with final state only
      await MainActor.run {
        // Update location to most recent position
        if let mostRecentLocation = emergencyLocations.last {
          self.location = mostRecentLocation
          self.course = mostRecentLocation.course >= 0 ? mostRecentLocation.course : nil
          self.lastCourseUpdate = Date()  // Prevent immediate recalculation
          self.updateHybridOrientation()
        }

        // Mark that emergency processing occurred in background
        self.emergencyProcessingOccurred = true
        print(
          "LocationManager: Emergency batch UI update completed - location updated to final position"
        )
      }
    }
  }

  /// Process accumulated background locations when returning to foreground
  private func processBackgroundAccumulationBuffer() {
    guard !isProcessingBackgroundBuffer, !backgroundLocationBuffer.isEmpty else { return }

    isProcessingBackgroundBuffer = true
    let locationsToProcess = backgroundLocationBuffer
    backgroundLocationBuffer.removeAll()

    print(
      "LocationManager: Processing \(locationsToProcess.count) background accumulated locations with COMPLETE UI update"
    )

    // BATCH UI UPDATE: Process all locations and update UI at once
    Task.detached(priority: .utility) {
      // 1. Process all route data in background thread
      await RouteManager.shared.processBulkLocationUpdate(locationsToProcess)

      // 2. Update UI on main thread with final state only
      await MainActor.run {
        // Update location to most recent position
        if let mostRecentLocation = locationsToProcess.last {
          self.location = mostRecentLocation
          self.course = mostRecentLocation.course >= 0 ? mostRecentLocation.course : nil
          self.lastCourseUpdate = Date()  // Prevent immediate recalculation
          self.updateHybridOrientation()
        }

        self.isProcessingBackgroundBuffer = false
        print(
          "LocationManager: Background accumulation UI update completed - location updated to final position"
        )
      }
    }
  }

  // MARK: - Manual Location Request for UI Button

  /// Request immediate location update for "Center on Location" button
  func requestImmediateLocation() {
    print("LocationManager: Immediate location requested by user")
    manager.requestLocation()
  }

  // MARK: - Location Preloading for Quick Start

  func startLocationPreloading() {
    // Clear any old preloaded locations
    preloadedLocations.removeAll()
    isPreloading = true

    // Start location updates with high accuracy
    manager.desiredAccuracy = kCLLocationAccuracyBest
    manager.distanceFilter = 2
    manager.startUpdatingLocation()

    print("LocationManager: Started preloading high-accuracy locations")
  }

  func stopLocationPreloading() {
    isPreloading = false

    // Don't stop location updates here as they might be needed for other purposes
    // Just stop collecting preloaded locations

    print(
      "LocationManager: Stopped preloading locations, cached \(preloadedLocations.count) locations")
  }

  // Get the best preloaded location (most accurate within the last few seconds)
  func getBestPreloadedLocation() -> CLLocation? {
    let recentTimeWindow: TimeInterval = 5.0  // Consider locations from last 5 seconds
    let now = Date()

    // Filter locations by recency and minimum accuracy
    let recentAccurateLocations = preloadedLocations.filter {
      now.timeIntervalSince($0.timestamp) < recentTimeWindow && $0.horizontalAccuracy <= 20
    }

    // Return the most accurate recent location
    return recentAccurateLocations.min(by: { $0.horizontalAccuracy < $1.horizontalAccuracy })
  }

  // MARK: - Location Manager Delegate Methods

  nonisolated func locationManager(
    _ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]
  ) {
    // Skip empty updates
    guard !locations.isEmpty else { return }

    Task { @MainActor in
      // Update the timestamp indicating we got a location update
      self.lastLocationTimestamp = Date()

      // Process each location through our optimized pipeline
      for location in locations {
        // Log location update for debugging
        let logManager = LogManager.shared
        logManager.logLocation(
          accuracy: location.horizontalAccuracy,
          speed: location.speed,
          isValid: self.isValidLocation(location),
          additionalInfo: "LocationManager update - workout: \(self.isInWorkoutMode)"
        )

        // If preloading is active, cache this location for quick start
        if self.isPreloading {
          // Only store high-accuracy locations for preloading
          if location.horizontalAccuracy <= 20 {
            self.preloadedLocations.append(location)

            // Keep only the most recent locations to prevent memory buildup
            if self.preloadedLocations.count > self.maxPreloadedSize {
              self.preloadedLocations.removeFirst()  // Efficient single-element removal
            }
          }
        }

        // Update course if needed (using the most recent location)
        if location == locations.last {
          let now = Date()
          if location.speed >= self.minimumSpeed,
            self.lastCourseUpdate == nil
              || now.timeIntervalSince(self.lastCourseUpdate!) >= self.courseUpdateInterval
          {
            self.course = location.course >= 0 ? location.course : nil
            self.lastCourseUpdate = now
          }

          // Update the published location for UI updates
          // This happens immediately for the most recent location
          if location.horizontalAccuracy <= 20 {
            self.location = location
            self.updateHybridOrientation()
          }
        }

        // In workout mode, add to queue for batch processing
        if self.isInWorkoutMode && self.isTracking {
          if !self.isInBackground {
            // FOREGROUND: PHASE 1 OPTIMIZATION - Direct processing with unified validation
            self.processValidatedLocation(location)
          } else {
            // BACKGROUND: Accumulate for foreground return, skip UI processing
            // This is the KEY PERFORMANCE OPTIMIZATION - defer RouteManager processing in background
            self.addToBackgroundAccumulationBuffer(location)
          }
        }
        // In non-workout mode, just update the current location
        else if !self.isInWorkoutMode && self.isTracking {
          // PHASE 1 OPTIMIZATION: Use unified validation for consistency
          if let validatedLocation = self.validateAndFilterLocation(location) {
            self.location = validatedLocation
            self.updateHybridOrientation()
          }
        }
      }
    }
  }

  nonisolated func locationManager(
    _ manager: CLLocationManager, didUpdateHeading newHeading: CLHeading
  ) {
    Task { @MainActor in
      self.heading = newHeading
      self.updateHybridOrientation()
    }
  }

  nonisolated func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
    Task { @MainActor in
      // Log the error but don't stop tracking
      print("LocationManager: Location update failed with error: \(error.localizedDescription)")

      // If we're using requestLocation() and it fails, we need to try again
      if !self.isInWorkoutMode && !self.isInBackground && self.isTracking {
        // Wait a second before trying again to avoid excessive requests
        try? await Task.sleep(for: .seconds(1))

        // Try again if still in foreground and tracking
        if !self.isInBackground && self.isTracking {
          print("LocationManager: Retrying location request after error")
          self.manager.requestLocation()
        }
      }
    }
  }

  nonisolated func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
    Task { @MainActor in
      let status = manager.authorizationStatus
      self.locationPermissionStatus = status

      switch status {
      case .notDetermined:
        print("LocationManager: Location authorization not determined")
        self.needsAlwaysPermission = true
      case .restricted:
        print("LocationManager: Location access restricted")
        self.needsAlwaysPermission = true
      case .denied:
        print("LocationManager: Location access denied")
        self.needsAlwaysPermission = true
      case .authorizedAlways:
        print("LocationManager: Location access authorized always")
        self.needsAlwaysPermission = false
        if self.isInWorkoutMode {
          self.updateLocationTracking()
        }
      case .authorizedWhenInUse:
        print("LocationManager: Location access authorized when in use")
        self.needsAlwaysPermission = true
        // If we need 'Always' permission but only have 'WhenInUse', ask for it again
        if self.isInWorkoutMode {
          print("LocationManager: Requesting 'Always' authorization for workout mode")
          manager.requestAlwaysAuthorization()
          self.updateLocationTracking()
        }
      @unknown default:
        print("LocationManager: Unknown authorization status")
        self.needsAlwaysPermission = true
      }
    }
  }

  // MARK: - Filtering Statistics (for debugging)
  func getFilteringStatistics() -> (received: Int, kept: Int, filterRatio: Double) {
    let ratio =
      totalLocationsReceived > 0 ? Double(totalLocationsKept) / Double(totalLocationsReceived) : 0.0
    return (totalLocationsReceived, totalLocationsKept, ratio)
  }

  func resetFilteringStatistics() {
    totalLocationsReceived = 0
    totalLocationsKept = 0
    locationFilterCounter = 0
    lastFilteredTime = nil
    print("LocationManager: Reset filtering statistics")
  }
}
