import Foundation
import HealthKit
import CoreLocation
import SwiftUI

/// HealthKit Manager for iPhone 16 iOS 17+ workout tracking
/// Uses HKWorkoutBuilder for iPhone-compatible background priority
@MainActor
class HealthKitManager: NSObject, ObservableObject {
    // Singleton instance
    static let shared = HealthKitManager()
    
    // HealthKit components
    private let healthStore = HKHealthStore()
    private var workoutBuilder: HKWorkoutBuilder?
    private var routeBuilder: HKWorkoutRouteBuilder?
    
    // Published state for UI
    @Published private(set) var isWorkoutActive = false
    @Published private(set) var isHealthKitAuthorized = false
    @Published private(set) var authorizationError: String?
    
    // Workout data (calculated by our existing systems)
    @Published private(set) var totalDistance: Double = 0
    @Published private(set) var averagePace: Double = 0
    @Published private(set) var currentSportType: HKWorkoutActivityType = .running
    
    // Workout metadata
    private var workoutStartDate: Date?
    private var workoutEndDate: Date?
    
    override init() {
        super.init()
        print("HealthKitManager: Initializing for iPhone 16 iOS 17+")
    }
    
    // MARK: - Authorization
    
    /// Request HealthKit permissions for iPhone workout tracking
    func requestAuthorization() async throws {
        guard HKHealthStore.isHealthDataAvailable() else {
            throw HealthKitError.healthDataNotAvailable
        }
        
        // Define the health data types we need
        let typesToWrite: Set<HKSampleType> = [
            HKObjectType.workoutType(),
            HKSeriesType.workoutRoute(),
            HKObjectType.quantityType(forIdentifier: .activeEnergyBurned)!,
            HKObjectType.quantityType(forIdentifier: .distanceWalkingRunning)!
        ]
        
        let typesToRead: Set<HKObjectType> = [
            HKObjectType.workoutType(),
            HKSeriesType.workoutRoute(),
            HKObjectType.quantityType(forIdentifier: .activeEnergyBurned)!,
            HKObjectType.quantityType(forIdentifier: .distanceWalkingRunning)!
        ]
        
        do {
            try await healthStore.requestAuthorization(toShare: typesToWrite, read: typesToRead)
            
            // Check authorization status
            let workoutAuthStatus = healthStore.authorizationStatus(for: HKObjectType.workoutType())
            let routeAuthStatus = healthStore.authorizationStatus(for: HKSeriesType.workoutRoute())
            
            if workoutAuthStatus == .sharingAuthorized && routeAuthStatus == .sharingAuthorized {
                isHealthKitAuthorized = true
                authorizationError = nil
                print("HealthKitManager: Authorization successful for iPhone 16")
            } else {
                isHealthKitAuthorized = false
                authorizationError = "HealthKit authorization denied. Please enable in Settings > Health > Data Access & Devices."
                print("HealthKitManager: Authorization denied - Workout: \(workoutAuthStatus.rawValue), Route: \(routeAuthStatus.rawValue)")
            }
        } catch {
            isHealthKitAuthorized = false
            authorizationError = "Failed to request HealthKit authorization: \(error.localizedDescription)"
            print("HealthKitManager: Authorization error: \(error)")
            throw HealthKitError.authorizationFailed(error)
        }
    }
    
    // MARK: - Workout Management
    
    /// Start HealthKit workout for iPhone 16 background priority
    func startWorkout(activityType: SportType) async throws {
        guard isHealthKitAuthorized else {
            throw HealthKitError.notAuthorized
        }
        
        guard !isWorkoutActive else {
            print("HealthKitManager: Workout already active")
            return
        }
        
        do {
            // Create workout configuration
            let configuration = HKWorkoutConfiguration()
            configuration.activityType = activityType.healthKitActivityType
            configuration.locationType = .outdoor // iPhone GPS-based workouts
            
            // Create workout builder for iPhone
            workoutBuilder = HKWorkoutBuilder(healthStore: healthStore, configuration: configuration, device: .local())
            
            // Create route builder for GPS tracking
            routeBuilder = HKWorkoutRouteBuilder(healthStore: healthStore, device: .local())
            
            // Start the workout
            let startDate = Date()
            workoutStartDate = startDate
            
            workoutBuilder?.beginCollection(withStart: startDate, completion: { success, error in
                if let error = error {
                    print("HealthKitManager: Failed to begin collection: \(error)")
                } else {
                    print("HealthKitManager: Successfully began collection")
                }
            })
            
            // Update state
            isWorkoutActive = true
            currentSportType = activityType.healthKitActivityType
            totalDistance = 0
            averagePace = 0
            
            print("HealthKitManager: Workout started successfully for \(activityType.rawValue) on iPhone 16")
            
        } catch {
            print("HealthKitManager: Failed to start workout: \(error)")
            throw HealthKitError.workoutStartFailed(error)
        }
    }
    
    /// End HealthKit workout and save data with enhanced post-workout processing
    func endWorkout(distance: Double, averagePace: Double, route: [Coordinate]) async throws -> HealthKitWorkoutData {
        guard let builder = workoutBuilder,
              let routeBuilder = self.routeBuilder,
              isWorkoutActive else {
            throw HealthKitError.noActiveWorkout
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            // Update final metrics
            self.totalDistance = distance
            self.averagePace = averagePace
            
            let endDate = Date()
            workoutEndDate = endDate
            
            // Add distance sample if we have distance data
            if distance > 0 {
                let distanceQuantity = HKQuantity(unit: .meter(), doubleValue: distance)
                let distanceSample = HKQuantitySample(
                    type: HKQuantityType.quantityType(forIdentifier: .distanceWalkingRunning)!,
                    quantity: distanceQuantity,
                    start: workoutStartDate ?? endDate,
                    end: endDate
                )
                builder.add([distanceSample]) { success, error in
                    if let error = error {
                        print("HealthKitManager: Failed to add distance sample: \(error)")
                    }
                }
            }
            
            // Add route data if available
            if !route.isEmpty {
                let locations = route.map { coordinate in
                    CLLocation(
                        coordinate: CLLocationCoordinate2D(latitude: coordinate.latitude, longitude: coordinate.longitude),
                        altitude: coordinate.altitude ?? 0.0,
                        horizontalAccuracy: 5.0,
                        verticalAccuracy: 5.0,
                        timestamp: coordinate.timestamp
                    )
                }
                
                routeBuilder.insertRouteData(locations) { success, error in
                    if let error = error {
                        print("HealthKitManager: Failed to insert route data: \(error)")
                    }
                }
            }
            
            // End collection and finalize workout
            builder.endCollection(withEnd: endDate, completion: { success, error in
                if let error = error {
                    print("HealthKitManager: Failed to end collection: \(error)")
                    continuation.resume(throwing: HealthKitError.workoutEndFailed(error))
                    return
                }
                print("HealthKitManager: Successfully ended collection")
                
                // Finalize the workout
                builder.finishWorkout { workout, error in
                    if let error = error {
                        print("HealthKitManager: Failed to finish workout: \(error)")
                        continuation.resume(throwing: HealthKitError.workoutEndFailed(error))
                        return
                    }
                    
                    guard let workout = workout else {
                        print("HealthKitManager: No workout returned from finishWorkout")
                        continuation.resume(throwing: HealthKitError.workoutEndFailed(NSError(domain: "HealthKitManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "No workout returned"])))
                        return
                    }
                    
                    print("HealthKitManager: Workout finished successfully")
                    
                    // Finalize the route and then process the complete workout data
                    if !route.isEmpty {
                        routeBuilder.finishRoute(with: workout, metadata: nil) { route, error in
                            if let error = error {
                                print("HealthKitManager: Failed to finish route: \(error)")
                                // Continue without route data rather than failing completely
                                self.processCompletedWorkout(workout: workout, continuation: continuation)
                            } else {
                                print("HealthKitManager: Route finished successfully")
                                self.processCompletedWorkout(workout: workout, continuation: continuation)
                            }
                        }
                    } else {
                        // No route data, process workout directly
                        self.processCompletedWorkout(workout: workout, continuation: continuation)
                    }
                }
            })
            
            // Reset state
            isWorkoutActive = false
            workoutBuilder = nil
            self.routeBuilder = nil
            workoutStartDate = nil
            workoutEndDate = nil
        }
    }
    
    /// Process completed workout and retrieve HealthKit data (Phase 2 implementation)
    private func processCompletedWorkout(workout: HKWorkout, continuation: CheckedContinuation<HealthKitWorkoutData, Error>) {
        print("HealthKitManager: Starting post-workout processing pipeline")
        
        // Create workout data with HealthKit's precise calculations
        let workoutData = HealthKitWorkoutData(
            workout: workout,
            startDate: workout.startDate,
            endDate: workout.endDate,
            duration: workout.duration,
            totalEnergyBurned: workout.totalEnergyBurned?.doubleValue(for: .kilocalorie()) ?? 0,
            totalDistance: workout.totalDistance?.doubleValue(for: .meter()) ?? self.totalDistance,
            averageSpeed: self.averagePace > 0 ? (1000.0 / (self.averagePace * 60.0)) : 0, // Convert min/km to m/s
            activityType: workout.workoutActivityType
        )
        
        print("HealthKitManager: Workout data processed - Duration: \(workoutData.duration)s, Calories: \(workoutData.totalEnergyBurned), Distance: \(workoutData.totalDistance)m")
        
        // Query for route data if available
        self.retrieveWorkoutRoute(for: workout) { routeCoordinates in
            var finalWorkoutData = workoutData
            finalWorkoutData.routeCoordinates = routeCoordinates
            
            print("HealthKitManager: Post-workout processing complete with \(routeCoordinates?.count ?? 0) route points")
            continuation.resume(returning: finalWorkoutData)
        }
    }
    
    /// Retrieve workout route from HealthKit (Phase 2 implementation)
    private func retrieveWorkoutRoute(for workout: HKWorkout, completion: @escaping ([Coordinate]?) -> Void) {
        let routeType = HKSeriesType.workoutRoute()
        let predicate = HKQuery.predicateForObjects(from: workout)
        
        let routeQuery = HKSampleQuery(sampleType: routeType, predicate: predicate, limit: HKObjectQueryNoLimit, sortDescriptors: nil) { query, samples, error in
            
            guard let routeSamples = samples as? [HKWorkoutRoute], !routeSamples.isEmpty else {
                print("HealthKitManager: No route data found for workout")
                completion(nil)
                return
            }
            
            print("HealthKitManager: Found \(routeSamples.count) route sample(s)")
            
            // Get the first (and typically only) route
            let route = routeSamples[0]
            
            // Query for location data
            let locationQuery = HKWorkoutRouteQuery(route: route) { locationQuery, locations, done, error in
                
                if let error = error {
                    print("HealthKitManager: Error retrieving route locations: \(error)")
                    completion(nil)
                    return
                }
                
                guard let locations = locations else {
                    if done {
                        print("HealthKitManager: No location data in route")
                        completion(nil)
                    }
                    return
                }
                
                if done {
                    // Convert CLLocations to Coordinates
                    let coordinates = locations.map { location in
                        Coordinate(
                            latitude: location.coordinate.latitude,
                            longitude: location.coordinate.longitude,
                            altitude: location.altitude,
                            timestamp: location.timestamp,
                            isPaused: false, // HealthKit doesn't track pause state
                            speed: location.speed >= 0 ? location.speed : nil
                        )
                    }
                    
                    print("HealthKitManager: Retrieved \(coordinates.count) location points from HealthKit")
                    completion(coordinates)
                }
            }
            
            self.healthStore.execute(locationQuery)
        }
        
        healthStore.execute(routeQuery)
    }
    
    /// Pause HealthKit workout
    func pauseWorkout() async throws {
        guard isWorkoutActive else {
            throw HealthKitError.noActiveWorkout
        }
        
        // HealthKit doesn't have explicit pause/resume for HKWorkoutBuilder
        // The pause/resume is handled by the app's logic
        print("HealthKitManager: Workout paused (handled by app logic)")
    }
    
    /// Resume HealthKit workout
    func resumeWorkout() async throws {
        guard isWorkoutActive else {
            throw HealthKitError.noActiveWorkout
        }
        
        // HealthKit doesn't have explicit pause/resume for HKWorkoutBuilder
        // The pause/resume is handled by the app's logic
        print("HealthKitManager: Workout resumed (handled by app logic)")
    }
    
    /// Get current workout data
    func getWorkoutData() async throws -> (totalEnergyBurned: Double?, duration: TimeInterval?) {
        guard isWorkoutActive else {
            throw HealthKitError.noActiveWorkout
        }
        
        // For now, return nil values as HealthKit will calculate these automatically
        // The actual values will be available after the workout is finished
        return (totalEnergyBurned: nil, duration: nil)
    }
    
    /// Add location data to the current workout route
    func addLocationToWorkout(_ location: CLLocation) {
        guard let routeBuilder = self.routeBuilder, isWorkoutActive else {
            return
        }
        
        routeBuilder.insertRouteData([location]) { success, error in
            if let error = error {
                print("HealthKitManager: Failed to add location to route: \(error)")
            }
        }
    }
    
    /// Add workout samples during the workout (for real-time data)
    func addWorkoutSamples(_ samples: [HKSample]) async throws {
        guard let builder = workoutBuilder, isWorkoutActive else {
            throw HealthKitError.noActiveWorkout
        }
        
        builder.add(samples) { success, error in
            if let error = error {
                print("HealthKitManager: Failed to add samples: \(error)")
            }
        }
    }
}

// MARK: - Data Types

/// Enhanced workout data from HealthKit with precise calculations
struct HealthKitWorkoutData {
    let workout: HKWorkout
    let startDate: Date
    let endDate: Date
    let duration: TimeInterval
    let totalEnergyBurned: Double // in kilocalories
    let totalDistance: Double // in meters
    let averageSpeed: Double // in m/s
    let activityType: HKWorkoutActivityType
    var routeCoordinates: [Coordinate]? // Retrieved from HealthKit route query
}

// MARK: - Error Types

enum HealthKitError: LocalizedError {
    case healthDataNotAvailable
    case notAuthorized
    case authorizationFailed(Error)
    case workoutStartFailed(Error)
    case workoutEndFailed(Error)
    case noActiveWorkout
    
    var errorDescription: String? {
        switch self {
        case .healthDataNotAvailable:
            return "Health data is not available on this device"
        case .notAuthorized:
            return "HealthKit access not authorized"
        case .authorizationFailed(let error):
            return "HealthKit authorization failed: \(error.localizedDescription)"
        case .workoutStartFailed(let error):
            return "Failed to start workout: \(error.localizedDescription)"
        case .workoutEndFailed(let error):
            return "Failed to end workout: \(error.localizedDescription)"
        case .noActiveWorkout:
            return "No active workout session"
        }
    }
} 