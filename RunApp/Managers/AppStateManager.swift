//
//  AppStateManager.swift
//  RunApp
//
//  Created by <PERSON><PERSON> on 1/24/25.
//

import SwiftUI
import UIKit
import Foundation

@MainActor
class AppStateManager: ObservableObject {
    static let shared = AppStateManager()
    
    @Published private(set) var isInBackground = false
    @Published private(set) var isInWorkoutMode = false
    @Published private(set) var isHibernating = false
    
    private var hibernationTimer: Timer?
    private let hibernationDelay: TimeInterval = 2.0 // 2 second delay to handle quick app switches
    private let minimumHibernationDuration: TimeInterval = 5.0 // Prevent hibernation thrashing
    private var lastHibernationTime: Date?
    
    // State management
    private var isTransitioning = false
    
    private init() {
        setupAppStateObservers()
    }
    
    // MARK: - Public Interface
    
    /// Call when workout starts
    func enterWorkoutMode() {
        isInWorkoutMode = true
        exitHibernation() // Immediately exit hibernation if in workout
        print("AppStateManager: Entered workout mode")
    }
    
    /// Call when workout ends
    func exitWorkoutMode() {
        isInWorkoutMode = false
        // Check if we should hibernate (if we're in background)
        if isInBackground {
            scheduleHibernation()
        }
        print("AppStateManager: Exited workout mode")
    }
    
    // MARK: - Private Implementation
    
    private func setupAppStateObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppTermination),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
    }
    
    @objc private func handleAppBackground() {
        isInBackground = true
        
        // Only schedule hibernation if NOT in workout mode
        if !isInWorkoutMode {
            scheduleHibernation()
        }
        
        print("AppStateManager: App entered background. Workout mode: \(isInWorkoutMode)")
    }
    
    @objc private func handleAppForeground() {
        Task { @MainActor in
            isInBackground = false
            cancelHibernationTimer()
            
            // Always exit hibernation when returning to foreground
            if isHibernating {
                exitHibernation()
            }
            
            print("AppStateManager: App entered foreground")
        }
    }
    
    @objc private func handleAppTermination() {
        // Force hibernation to clean up resources before termination
        if !isInWorkoutMode {
            forceHibernation()
        }
        print("AppStateManager: App terminating")
    }
    
    private func scheduleHibernation() {
        // Cancel any existing timer
        cancelHibernationTimer()
        
        // Don't hibernate if in workout mode
        guard !isInWorkoutMode else {
            print("AppStateManager: Hibernation cancelled - in workout mode")
            return
        }
        
        print("AppStateManager: Scheduling hibernation in \(hibernationDelay) seconds")
        
        hibernationTimer = Timer.scheduledTimer(withTimeInterval: hibernationDelay, repeats: false) { [weak self] _ in
            guard let self = self else { return }
            
            Task { @MainActor in
                // Double-check conditions before hibernating
                if self.isInBackground && !self.isInWorkoutMode && !self.isHibernating {
                    self.enterHibernation()
                }
            }
        }
    }
    
    private func cancelHibernationTimer() {
        hibernationTimer?.invalidate()
        hibernationTimer = nil
    }
    
    private func enterHibernation() {
        // Prevent race conditions and invalid state transitions
        guard !isHibernating && !isInWorkoutMode && !isTransitioning else { 
            print("AppStateManager: Hibernation blocked - invalid state")
            return 
        }
        
        // Check minimum hibernation interval to prevent thrashing
        if let lastTime = lastHibernationTime,
           Date().timeIntervalSince(lastTime) < minimumHibernationDuration {
            print("AppStateManager: Hibernation blocked - too soon since last hibernation")
            return
        }
        
        isTransitioning = true
        defer { isTransitioning = false }
        
        isHibernating = true
        lastHibernationTime = Date()
        print("AppStateManager: Entering hibernation mode")
        
        // Notify all managers to hibernate
        NotificationCenter.default.post(name: .appShouldHibernate, object: nil)
        
        // Additional system-level optimizations
        optimizeSystemResources(hibernate: true)
    }
    
    private func exitHibernation() {
        guard isHibernating && !isTransitioning else { 
            print("AppStateManager: Wake-up blocked - invalid state")
            return 
        }
        
        isTransitioning = true
        defer { isTransitioning = false }
        
        isHibernating = false
        cancelHibernationTimer()
        print("AppStateManager: Exiting hibernation mode")
        
        // Notify all managers to wake up
        NotificationCenter.default.post(name: .appShouldWakeUp, object: nil)
        
        // Restore system-level optimizations
        optimizeSystemResources(hibernate: false)
    }
    
    private func forceHibernation() {
        if !isHibernating && !isInWorkoutMode {
            isHibernating = true
            NotificationCenter.default.post(name: .appShouldHibernate, object: nil)
            optimizeSystemResources(hibernate: true)
            print("AppStateManager: Force hibernation for app termination")
        }
    }
    
    private func optimizeSystemResources(hibernate: Bool) {
        if hibernate {
            // Disable idle timer management when hibernating (preserve battery)
            // Note: Don't change isIdleTimerDisabled directly as it may be controlled by user settings
            
            // Lower process priority for non-essential tasks
            // This is handled by individual managers
            
            print("AppStateManager: Applied hibernation system optimizations")
        } else {
            // Restore normal system behavior
            print("AppStateManager: Restored normal system operations")
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        hibernationTimer?.invalidate()
        hibernationTimer = nil
    }
}
