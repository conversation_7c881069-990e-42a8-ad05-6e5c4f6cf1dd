// ActivityControls
"activities" = "Trainingseinheiten";
"metronome" = "Metronom";

// StatsView
"distance" = "Distanz";
"active" = "Aktiv";
"time" = "Zeit";
"current" = "Aktuell";
"pace" = "Tempo";
"km" = "km";
"mi" = "mi";

// ActivityView
"activities" = "Trainingseinheiten";
"no_activities" = "Keine Trainingseinheiten";
"complete_first_run" = "Schließe dein erstes Training ab, um es hier zu sehen";

// ActivityRowView
"distance" = "Distanz";
"duration" = "Zeit";
"avg_pace" = "Durchschn. Tempo";
"calories" = "Kalorien";
"start" = "Start";
"end" = "Ziel";

// ActivityDetailView
"delete_activity" = "Trainingseinheit löschen";
"are_you_sure_deletion" = "Bist du sicher? Das Löschen kann nicht rückgängig gemacht werden.";

// CountdownView
"go" = "SPRINT!";

// LanguageSettingView
"language" = "Sprache";
"done" = "Fertig";

// AuthView
"runapp" = "RunApp";
"email" = "E-Mail";
"sign_in_with_email" = "Mit E-Mail anmelden";
"check_inbox_login_link" = "Prüfe deinen Posteingang für den Anmeldelink.";

// AnalysisView
"improve_with_personalized_data" = "Verbessere dich wissenschaftlich mit persönlichen Daten";
"run" = "Laufen";
"walk" = "Gehen";
"hike" = "Wandern";
"bike" = "Radfahren";
"distance" = "Distanz";
"duration" = "Zeit";
"calories" = "Kalorien";
"current_week" = "Aktuelle Woche";
"current_month" = "Aktueller Monat";
"current_year" = "Aktuelles Jahr";
"per_day" = "pro Tag";
"per_month" = "pro Monat";
"generate_test_data" = "Testdaten generieren";

// MetronomeSettingsView
"metronome_settings" = "Metronom";
"tempo" = "Tempo";
"tempo_footer" = "Tempo zwischen 40-240 Schläge pro Minute einstellen";
"enable_metronome" = "Metronom aktivieren";
"sound" = "Ton";
"sound_type" = "Tonart";
"vibration" = "Vibration";
"vibration_strength" = "Vibrationsstärke";
"alert_frequency" = "Warnungsfrequenz";
"largo" = "Largo";
"adagio" = "Adagio";
"andante" = "Andante";
"moderato" = "Moderato";
"allegro" = "Allegro";
"presto" = "Presto";
"prestissimo" = "Prestissimo";
"default_beat" = "Standard-Schlag";
"beat_2" = "Schlag 2";
"beat_3" = "Schlag 3";
"beat_1" = "Schlag 1";
"beat_4" = "Schlag 4";
"beat_5" = "Schlag 5";
"beat_6" = "Schlag 6";
"feedback" = "Ton und Vibration";
"feedback_footer" = "Wähle verschiedene Tonarten, wenn der Ton aktiviert ist";

// NameSettingView
"name" = "Name";
"enter_your_name" = "Gib deinen Namen ein";
"cancel" = "Abbrechen";
"save" = "Speichern";
"invalid_name" = "Ungültiger Name";
"please_enter_valid_name" = "Bitte gib einen gültigen Namen ein";

// WeightSettingView
"weight" = "Gewicht";
"unit" = "Einheit";
"calorie_calculations_required" = "Erforderlich für genaue Kalorienberechnungen";
"invalid_weight" = "Ungültiges Gewicht";
"please_enter_valid_weight" = "Bitte gib ein gültiges Gewicht zwischen %d und %d %@ ein";

// GenderSettingView
"gender" = "Geschlecht";
"gender_footer" = "Optional - Wird für genauere Kalorienberechnungen verwendet";

// AgeSettingView
"age" = "Alter";
"birth_date" = "Geburtsdatum";
"years_old" = "Jahre alt";
"invalid_age" = "Ungültiges Alter";
"age_requirement" = "Du musst mindestens 13 Jahre alt sein, um diese App zu nutzen";

// HeightSettingView
"height" = "Größe";
"height_cm" = "Größe (cm)";
"feet" = "Fuß";
"inches" = "Zoll";
"bmi_calculations" = "Optional - Wird für BMI-Berechnungen verwendet";
"invalid_height" = "Ungültige Größe";
"please_enter_valid_height" = "Bitte gib eine gültige Größe ein.";

// SettingsView
"settings" = "Einstellungen";
"profile" = "Profil";
"not_set" = "Nicht festgelegt";
"age" = "Alter";
"years" = "Jahre";
"audio" = "Audio";
"preferences" = "Einstellungen";
"metronome" = "Metronom";
"bpm" = "BPM";
"units" = "Einheiten";
"metric" = "Metrisch";
"imperial" = "Imperial";
"theme" = "Design";
"language" = "Sprache";
"data" = "Daten";
"all_activities" = "Alle Trainingseinheiten";
"export_activities" = "Trainingsdaten exportieren";
"exporting" = "Exportiere...";
"error" = "Fehler";
"ok" = "OK";
"unknown_error" = "Ein unbekannter Fehler ist aufgetreten";
"system_default" = "Systemstandard";
"light" = "Hell";
"dark" = "Dunkel";
"male" = "Männlich";
"female" = "Weiblich";
"other" = "Andere";
"prefer_not_to_say" = "Keine Angabe";

// UnitsSettingView
"unit_system" = "Einheitensystem";

// SportTypeSelector
"run" = "Laufen";
"walk" = "Gehen";
"hike" = "Wandern";
"bike" = "Radfahren";

// ProfileHeaderView
"your_name" = "Dein Name";
"email_example" = "<EMAIL>";
"change" = "Ändern";
"delete_photo" = "Foto löschen?";
"delete" = "Löschen";

// ActivitySummaryComponents
"total" = "Gesamt";
"avg" = "Durchschnitt";
"per_day" = "pro Tag";
"per_month" = "pro Monat";

// PeriodIndicatorView
"current_week" = "Aktuelle Woche";
"week_ago_format" = "Vor %d Woche (%d)";
"weeks_ago_format" = "Vor %d Wochen (%d)";
"current_month" = "Aktueller Monat";
"month_ago_format" = "Vor %d Monat (%d)";
"months_ago_format" = "Vor %d Monaten (%d)";
"current_year" = "Aktuelles Jahr";
"year_ago_format" = "Vor %d Jahr (%d)";
"years_ago_format" = "Vor %d Jahren (%d)";

// TimePeriodPicker
"time_period" = "Zeitraum";
"seven_days" = "1 Woche";
"one_month" = "1 Monat";
"one_year" = "1 Jahr";
"all_time" = "Alle Zeit";

// For timeOfDay values
"morning" = "Morgentraining";
"afternoon" = "Nachmittagstraining";
"evening" = "Abendlauf";
"night" = "Nachttraining";

"every_beat" = "Jeder Schlag";
"every_other_beat" = "Jeder zweite Schlag";
"every_4th_beat" = "Jeder 4. Schlag";
"every_6th_beat" = "Jeder 6. Schlag";

// AudioAlertSettingsView
"audio_prompts" = "Sprachansagen";
"enable_audio_prompts" = "Sprachansagen aktivieren";
"audio_prompts_footer" = "Erhalte Sprachansagen während deines Trainings";
"distance_alerts" = "Distanz-Benachrichtigungen";
"distance_alerts_footer" = "Ansage jeder Distanz-Meilenstein";
"time_alerts" = "Zeit-Benachrichtigungen";
"time_alerts_footer" = "Ansage jedes Zeitintervalls";
"calorie_alerts" = "Kalorien-Benachrichtigungen";
"calorie_alerts_footer" = "Ansage der verbrannten Kalorien-Meilensteine";
"pace_alerts" = "Tempo-Benachrichtigungen";
"pace_alerts_footer" = "Regelmäßige Ansage des aktuellen Tempos";
"custom" = "Benutzerdefiniert";
"value" = "Wert";
"min" = "Min";
"cal" = "Kal";
"enabled" = "Aktiviert";
"disabled" = "Deaktiviert";
"hour" = "Stunde";
"h" = "h";
"m" = "m";
"distance_short" = "Distanz";
"time_short" = "Zeit";
"calorie_short" = "Kalorien";
"pace_short" = "Tempo";
"no_alerts_configured" = "Nicht konfiguriert";
"all_alerts" = "Alle aktiviert";
"enter_custom_value" = "Benutzerdefinierten Wert eingeben";
"integers_only" = "Nur ganze Zahlen";
"invalid_input" = "Ungültige Eingabe";
"please_enter_positive_number" = "Bitte gib eine positive Zahl ein";
"please_enter_whole_number" = "Bitte gib eine ganze Zahl ein (keine Dezimalstellen)";
"please_enter_value_between" = "Bitte gib einen Wert zwischen %@ und %@ ein";
"invalid" = "Ungültig";

"per_day" = "pro Tag";
"per_month" = "pro Monat";

// MARK: - Welcome View
"welcome.title" = "Willkommen bei RunApp";
"welcome.subtitle" = "Ausrüstung bereit, jeder Schritt zählt!";
"welcome.button.start" = "Training starten";

// MARK: - Basic Info View
"basicInfo.header.title" = "Personalisiere dein Erlebnis";
"basicInfo.header.subtitle" = "für präzise Kalorienberechnung";

// MARK: - Gender Selection
"basicInfo.gender.title" = "Was ist dein Geschlecht?";
"basicInfo.gender.male" = "Männlich";
"basicInfo.gender.female" = "Weiblich";
"basicInfo.gender.preferNotToSay" = "Keine Angabe";
"basicInfo.button.skip" = "Überspringen";

// MARK: - Weight Input
"basicInfo.weight.title" = "Wie viel wiegst du?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lbs";

// MARK: - Height Input
"basicInfo.height.title" = "Wie groß bist du?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "FT";
"basicInfo.height.placeholder.in" = "IN";

"basicInfo.button.continue" = "Weiter";

// MARK: - All Set View
"allSet.title" = "Alles bereit!";
"allSet.subtitle.first" = "Zeit zum Schwitzen";
"allSet.subtitle.second" = "Keine Ausreden, nur Action!";
"allSet.button.go" = "Sprint!";

// MARK: - ContentView Alerts
"alert.location.title" = "Standortberechtigung erforderlich";
"alert.location.message" = "Aktiviere 'Immer erlauben' für die Standortfreigabe, um deine Routen perfekt zu verfolgen und deine Trainingsdaten genau zu halten. Bitte aktualisiere deine Einstellungen.";
"alert.location.settings" = "Einstellungen";
"alert.location.later" = "Später";

"alert.complete.title" = "Training beenden?";
"alert.complete.message" = "Dies beendet dein aktuelles Training.\nDu kannst nach dem Beenden nicht fortsetzen.";
"alert.complete.confirm" = "Beenden";
"alert.complete.cancel" = "Abbrechen";

"alert.shortWorkout.title" = "Kurzes Training bestätigen";
"alert.shortWorkout.message" = "Dein Training ist weniger als 30 Sekunden lang. Bist du sicher, dass du es speichern möchtest?";
"alert.shortWorkout.save" = "Speichern";
"alert.shortWorkout.discard" = "Verwerfen";

// MARK: - Map Markers
"map.marker.start" = "Startlinie";
"map.marker.end" = "Ziellinie";
"map.location.unknown" = "Unbekannter Ort";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "Metronom an";
"metronome.status.off" = "Metronom aus";

"activity.summary.title" = "Trainingsübersicht";

// GPS Status
"gps.searching" = "GPS-Signal wird gesucht...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "Du hast unbegrenztes Training freigeschaltet, schwitz und brenne deine Leidenschaft!";
"settings.subscription.status.exceeded" = "Kostenlose Testversion beendet! Jetzt abonnieren und deine Sportleidenschaft weiter entfachen!";
"settings.subscription.status.oneRemaining" = "Du hast noch 1 kostenloses Training — jetzt abonnieren und den Rhythmus beibehalten!";
"settings.subscription.status.remaining" = "Du hast noch %d kostenlose Trainings — jetzt abonnieren und weiter Kalorien verbrennen!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Abonnement";
"settings.subscription.unlimitedAccess" = "Unbegrenztes Training";
"settings.subscription.checkingStatus" = "Überprüfung...";
"settings.subscription.alert.alreadySubscribed.title" = "Bereits abonniert";
"settings.subscription.alert.alreadySubscribed.message" = "Du hast bereits unbegrenzten Trainingszugang.";
"settings.subscription.alert.checkFailed.format" = "Überprüfung des Abonnement-Status fehlgeschlagen: %@";

// MARK: - Custom Value Interface
"custom_interval" = "Benutzerdefiniertes Intervall";
"interval_value" = "Intervallwert";
"enter_value_placeholder" = "Wert eingeben";
"whole_numbers_only" = "Nur ganze Zahlen";
"valid_range" = "Gültiger Bereich: %@ - %@";
"preset_options" = "Schnelloptionen:";
"custom_value" = "Benutzerdefinierter Wert";

// MARK: - Audio Volume Settings
"audio_volume" = "Audio-Lautstärke";
"audio_volume_footer" = "Metronom-Lautstärke anpassen";
"volume" = "Lautstärke";
"voice_volume" = "Stimmlautstärke";
"audio_settings" = "Audio-Einstellungen";
"audio_settings_footer" = "Lautstärke und Geschwindigkeit der Sprachansagen anpassen";
"test_voice" = "Stimme testen";

// MARK: - Smart Voice Messages
"smart_voice" = "Intelligente Stimme";
"smart_voice_footer" = "Aktiviere natürliche und motivierende Sprachansagen anstelle von Standard-Ansagen";

// Test messages
"audio.test.standard" = "Audio-Ansagen funktionieren ordnungsgemäß";
"audio.test.smart" = "Ausgezeichnet! Dein Audio ist perfekt, bereit dich zu motivieren!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "Sehr gut! Du hast %@ %@ geschafft";
"audio.distance.achievement.smart" = "Hervorragend! Du hast die %@ %@ Marke erreicht! Du bist unaufhaltbar!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "Du bist bereits %@ in Bewegung. Halte dieses großartige Tempo bei!";

// Calorie messages
"audio.calories.standard" = "%@ Kalorien";
"audio.calories.smart" = "Du hast %@ Kalorien verbrannt! Deine Anstrengung zahlt sich aus!";
"audio.calories.achievement.smart" = "Beeindruckend! %@ Kalorien verbrannt! Du bist eine Kalorienverbrennungsmaschine!";

// Pace messages
"audio.pace.standard" = "Aktuelles Tempo: %@";
"audio.pace.smart" = "Du hältst ein Tempo von %@. Du siehst sehr stark aus!";

// Unit strings for audio
"audio.unit.km.singular" = "Kilometer";
"audio.unit.km.plural" = "Kilometer";
"audio.unit.mile.singular" = "Meile";
"audio.unit.mile.plural" = "Meilen";

// Time formatting for audio
"audio.time.minute.singular" = "1 Minute";
"audio.time.minutes" = "%d Minuten";
"audio.time.hour.singular" = "1 Stunde";
"audio.time.hours" = "%d Stunden";
"audio.time.hours.minutes" = "%d Stunden und %d Minuten";

// Pace formatting for audio
"audio.pace.per.km" = "pro Kilometer";
"audio.pace.per.mile" = "pro Meile";
"audio.pace.seconds" = "%d Sekunden %@";
"audio.pace.minutes" = "%d Minuten %@";
"audio.pace.minutes.seconds" = "%d Minuten und %d Sekunden %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Standard";
"voice.quality.enhanced" = "Verbessert";
"voice.quality.premium" = "Premium";
"voice.manager.test.text" = "Dies ist ein Test der ausgewählten Stimme für Ihre Lauf-Benachrichtigungen";
"voice.test.sample" = "Dies ist ein Test der ausgewählten Stimme für Ihre Lauf-Benachrichtigungen";
"voice.selection.title" = "Stimmenauswahl";
"voice.current" = "Aktuelle Stimme";
"voice.preview" = "Vorschau";
"voice.none.available" = "Keine Stimmen für diese Sprache verfügbar";
"voice.loading" = "Stimmen werden geladen...";

// MARK: - Voice Selection UI
"voice.download.required" = "Download Erforderlich";
"voice.loading.voices" = "Stimmen werden geladen...";
"voice.empty.title" = "Keine Stimmen Verfügbar";
"voice.empty.message" = "Keine verbesserten oder Premium-Stimmen sind für diese Sprache verfügbar. Laden Sie hochwertige Stimmen aus den Einstellungen herunter, um Ihre Erfahrung zu verbessern.";
"voice.refresh.voices" = "Stimmen Aktualisieren";
"voice.refresh.footer" = "Tippen Sie, um die Liste der verfügbaren Stimmen zu aktualisieren";
"voice.current.language" = "Aktuelle Sprache";
"voice.selection.description" = "Wählen Sie Ihre bevorzugte Stimme für Audio-Benachrichtigungen.\nVerbesserte und Premium-Optionen bieten die höchste Qualität.";
"voice.preview.error.title" = "Vorschau Fehlgeschlagen";
"voice.preview.error.message" = "Die Stimme '%@' kann nicht in der Vorschau angezeigt werden. Bitte versuchen Sie es erneut.";
"voice.add.new" = "Neue Stimme Hinzufügen";
"voice.manage.description" = "Gehen Sie zu Einstellungen > Bedienungshilfen > Gesprochene Inhalte > Stimmen, um Stimmen herunterzuladen und zu verwalten";
"voice.system.default" = "Systemstandard";

// MARK: - Voice Instructions
"voice.instructions.title" = "Neue Stimmen Herunterladen";
"voice.instructions.message" = "Befolgen Sie diese Schritte, um hochwertige Stimmen herunterzuladen:\n\n1. Tippen Sie unten auf 'Einstellungen Öffnen'\n2. Navigieren Sie zu: Bedienungshilfen\n3. Wählen Sie: Gesprochene Inhalte\n4. Wählen Sie: Stimmen\n5. Laden Sie Ihre bevorzugten Stimmen herunter\n6. Kehren Sie zu RunApp zurück und aktualisieren Sie";
"voice.instructions.open.settings" = "Einstellungen Öffnen";
"voice.instructions.steps.title" = "So fügen Sie Stimmen hinzu:";
"voice.instructions.steps.detail" = "Einstellungen → Bedienungshilfen → Gesprochene Inhalte → Stimmen";
"voice.instructions.footer" = "Tippen Sie auf die Schaltfläche oben, um schrittweise Anweisungen zum Herunterladen neuer Stimmen zu sehen";
"voice.add.new.subtitle" = "Schrittweise Anweisungen erhalten";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Stimmenauswahl";
"voice.selection.footer" = "Wählen Sie Ihre bevorzugte Stimme für Audio-Ansagen";

// Common buttons
"common.cancel" = "Abbrechen";
"common.close" = "Schließen";
"common.done" = "Fertig";
"common.ok" = "OK";

// MARK: - Voice Management
"voice.delete.success" = "Stimme erfolgreich gelöscht";
"voice.delete.failed" = "Stimme konnte nicht gelöscht werden";
"voice.delete.confirm.title" = "Stimme löschen?";
"voice.delete.confirm.message" = "Dies entfernt '%@' von Ihrem Gerät. Sie können sie später aus den Einstellungen erneut herunterladen.";
"voice.delete.confirm.delete" = "Löschen";
"voice.delete.unavailable" = "Diese Stimme kann nicht gelöscht werden, da es sich um eine Systemstandardstimme handelt.";
"voice.manage.voices" = "Stimmen verwalten";
"voice.download.status" = "Heruntergeladen";
"voice.builtin.status" = "Integriert";

// MARK: - Smart Voice Prompt Variations (German)
// Distance Smart Variations - Deutsch motivierend
"audio.distance.smart.1" = "Wahnsinn! Du bist schon %@ %@ gelaufen! Du rockst!";
"audio.distance.smart.2" = "Hammer! %@ %@ geschafft! Weiter so, Champion!";
"audio.distance.smart.3" = "Krass! %@ %@ im Sack! Was für eine Maschine!";
"audio.distance.smart.4" = "Genial! %@ %@ vollendet! Du bist der Wahnsinn!";
"audio.distance.smart.5" = "Mega! %@ %@ erreicht! Du bist ein Ass!";
"audio.distance.smart.6" = "Brutal! %@ %@ dominiert! Mach weiter!";
"audio.distance.smart.7" = "Stark! %@ %@ überwunden! Was für Power!";
"audio.distance.smart.8" = "Top! %@ %@ freigeschaltet! Du bist Feuer!";
"audio.distance.smart.9" = "Klasse! %@ %@ weggeputzt! Pure Kraft!";
"audio.distance.smart.10" = "Legende! %@ %@ zerstört! Du bist der Boss!";

// Time Smart Variations - Deutsch motivierend
"audio.time.smart.1" = "Schon %@ am Laufen! Was für Ausdauer!";
"audio.time.smart.2" = "Wow! %@ pure Power! Weiter durchziehen!";
"audio.time.smart.3" = "Krass! %@ ohne Stopp! Du bist eine Maschine!";
"audio.time.smart.4" = "Wahnsinn! %@ voller Entschlossenheit! Gib Gas!";
"audio.time.smart.5" = "Mega! %@ konzentriert! Du schaffst das!";
"audio.time.smart.6" = "Brutal! %@ Einsatz! Was für ein Kämpfer!";
"audio.time.smart.7" = "Stark! %@ unaufhaltbar! Weiter so!";
"audio.time.smart.8" = "Top! %@ am Kämpfen! Du bist Feuer!";
"audio.time.smart.9" = "Klasse! %@ Leidenschaft! Du bist der Beste!";
"audio.time.smart.10" = "Legende! %@ am Zerstören! Pure Herrlichkeit!";

// Calories Smart Variations - Deutsch motivierend
"audio.calories.smart.1" = "Wow! Du hast %@ Kalorien verbrannt! Du bist Feuer!";
"audio.calories.smart.2" = "Krass! %@ Kalorien eliminiert! Weiter verbrennen!";
"audio.calories.smart.3" = "Wahnsinn! %@ Kalorien dominiert! Was für eine Maschine!";
"audio.calories.smart.4" = "Mega! %@ Kalorien erobert! Mach weiter!";
"audio.calories.smart.5" = "Brutal! %@ Kalorien demoliert! Nicht stoppen!";
"audio.calories.smart.6" = "Stark! %@ Kalorien zerlegt! Du bist ein Ass!";
"audio.calories.smart.7" = "Top! %@ Kalorien zerstört! Weiter so!";
"audio.calories.smart.8" = "Klasse! %@ Kalorien verdampft! Unaufhaltbar!";
"audio.calories.smart.9" = "Genial! %@ Kalorien vernichtet! Beast-Modus!";
"audio.calories.smart.10" = "Legende! %@ Kalorien pulverisiert! Du bist der Boss!";

// Pace Smart Variations - Deutsch motivierend
"audio.pace.smart.1" = "Perfekt! Tempo %@! Was für Kontrolle!";
"audio.pace.smart.2" = "Wow! %@ Geschwindigkeit! Pure Power!";
"audio.pace.smart.3" = "Krass! %@ beherrscht! Du bist eine Maschine!";
"audio.pace.smart.4" = "Wahnsinn! %@ dominiert! Was für Technik!";
"audio.pace.smart.5" = "Mega! %@ perfekt! Weiter so!";
"audio.pace.smart.6" = "Brutal! %@ flüssig! Du bist ein Ass!";
"audio.pace.smart.7" = "Stark! %@ professionell! Was für ein Level!";
"audio.pace.smart.8" = "Top! %@ unaufhaltbar! Du bist Feuer!";
"audio.pace.smart.9" = "Klasse! %@ traumhaft! Beast-Modus!";
"audio.pace.smart.10" = "Legende! %@ zerstörerisch! Du bist der Boss!";

// MARK: - Workout Settings
"workout.settings.title" = "Trainingseinstellungen";
"workout.settings.notice" = "Training läuft";
"workout.settings.safe.only" = "Nur trainingssichere Einstellungen verfügbar";

// MARK: - Voice Premium Features
"voice.selection.simple.description" = "Wählen Sie Ihre bevorzugte Stimme für Audio-Ansagen während Ihrer Läufe.";
"voice.premium.benefits.recommendation" = "Wir empfehlen Premium-Stimmen für die beste Erfahrung";
"voice.premium.benefit.clarity" = "Überlegene Audio-Klarheit für Outdoor-Training";
"voice.premium.benefit.noise" = "Bessere Leistung in lauten Umgebungen";
"voice.premium.benefit.motivation" = "Natürlicherer, motivierender Stimmton";
"voice.management.footer" = "Laden Sie neue Stimmen aus den iOS-Einstellungen herunter und aktualisieren Sie, um neu installierte Stimmen zu sehen";
"voice.selection.workout.description" = "Wählen Sie Ihre Stimme für Trainings-Audio-Ansagen. Premium-Stimmen bieten die klarste, motivierendste Erfahrung beim Laufen.";

// MARK: - Speech Speed Settings
"speech_speed" = "Sprechgeschwindigkeit";
"speech_speed_footer" = "Steuern Sie, wie schnell Sprachansagen gesprochen werden. Langsamere Geschwindigkeiten helfen beim Verständnis während intensiver Trainings oder in lauten Umgebungen.";

// MARK: - Data Management
"data_management" = "Datenverwaltung";
"data_privacy" = "Datenschutz";
"export_data" = "Daten exportieren";
"import_data" = "Daten importieren";
"delete_data" = "Daten löschen";

// Privacy Messages
"privacy_ownership" = "Sie behalten das Eigentum an Ihren Daten";
"privacy_icloud_storage" = "Sicher in Ihrer iCloud gespeichert";
"privacy_no_access" = "Wir (als Entwickler) können nicht auf Ihre persönlichen Daten zugreifen";
"privacy_full_control" = "Sie behalten die volle Kontrolle über Ihre Daten";

// Export
"export_all_data" = "Alle Daten exportieren";
"export_profile_only" = "Nur Profil exportieren";
"export_activities_only" = "Nur Aktivitäten exportieren";
"export_all_description" = "Profil und Aktivitäten in 2 Dateien";
"export_profile_description" = "Persönliche Einstellungen und Präferenzen";
"export_activities_description" = "Alle Trainingsdaten und Statistiken";
"export_data_description" = "Exportieren Sie Ihre Daten als CSV-Dateien für Backup oder Transfer";

// Import
"import_data_files" = "Datendateien importieren";
"import_data_files_description" = "Wählen Sie zuvor exportierte CSV-Dateien aus";
"import_data_description" = "Aus zuvor exportierten Dateien wiederherstellen";

// Delete
"delete_all_data" = "Alle Daten löschen";
"delete_all_data_description" = "Alle Daten dauerhaft entfernen";
"delete_data_description" = "Diese Aktion kann nicht rückgängig gemacht werden";
"delete_confirmation" = "Löschung bestätigen";
"delete_confirmation_message" = "Dies wird dauerhaft alle Ihre Profildaten und Aktivitäten löschen. Diese Aktion kann nicht rückgängig gemacht werden.";
"delete_permanently" = "Dauerhaft löschen";

// Status Messages
"importing" = "Importiere...";
"deleting" = "Lösche...";
"export_failed" = "Export fehlgeschlagen";
"import_failed" = "Import fehlgeschlagen";
"delete_failed" = "Löschen fehlgeschlagen";
"import_success" = "Import erfolgreich";
"import_success_message" = "Daten wurden erfolgreich importiert";
"delete_success" = "Daten gelöscht";
"delete_success_message" = "Alle Ihre Daten wurden dauerhaft gelöscht. Sie kehren nun zum Hauptbildschirm zurück.";
"import_error" = "Import-Fehler";
"export_error" = "Export-Fehler";
"delete_error" = "Lösch-Fehler";

"select_gender_title" = "Geschlecht auswählen";

// MARK: - Activity Stats View
"act_dist" = "Trainingsdistanz";
"act_time" = "Trainingszeit";
"act_cal" = "Training Kal";
"performance" = "Leistung";
"best_pace" = "Besttempo";
"avg_speed" = "Durchschn. Speed";
"max_speed" = "Max Speed";
"time_analysis" = "Zeitanalyse";
"total_time" = "Gesamtzeit";
"paused_time" = "Pausenzeit";
"active_calories" = "Aktiv";
"resting_calories" = "Ruhe";
"rate" = "Rate";
"kcal" = "kcal";
"cal_per_min" = "kal/min";
