// ActivityControls
"activities" = "Allenamenti";
"metronome" = "Metronomo";

// StatsView
"distance" = "Distanza";
"active" = "Attivo";
"time" = "Tempo";
"current" = "Attuale";
"pace" = "Ritmo";
"km" = "km";
"mi" = "mi";

// ActivityView
"activities" = "Allenamenti";
"no_activities" = "Nessun Allenamento";
"complete_first_run" = "Completa la tua prima corsa per vederla qui";

// ActivityRowView
"distance" = "Distanza";
"duration" = "Durata";
"avg_pace" = "Ritmo Medio";
"calories" = "Calorie";
"start" = "Partenza";
"end" = "Traguardo";

// ActivityDetailView
"delete_activity" = "Elimina Allenamento";
"are_you_sure_deletion" = "Sei sicuro? L'eliminazione non può essere annullata.";

// CountdownView
"go" = "SCATTA!";

// LanguageSettingView
"language" = "Lingua";
"done" = "Fatto";

// AuthView
"runapp" = "RunApp";
"email" = "Email";
"sign_in_with_email" = "Accedi con Email";
"check_inbox_login_link" = "Controlla la tua casella di posta per il link di accesso.";

// AnalysisView
"improve_with_personalized_data" = "Migliora con Dati Personalizzati";
"run" = "Corsa";
"walk" = "Camminata Sportiva";
"hike" = "Trekking";
"bike" = "Ciclismo";
"distance" = "Distanza";
"duration" = "Durata";
"calories" = "Calorie";
"current_week" = "Settimana corrente";
"current_month" = "Mese corrente";
"current_year" = "Anno corrente";
"per_day" = "al giorno";
"per_month" = "al mese";
"generate_test_data" = "Genera Dati di Test";

// MetronomeSettingsView
"metronome_settings" = "Metronomo";
"tempo" = "Tempo battiti";
"tempo_footer" = "Regola il tempo tra 40-240 battiti al minuto";
"enable_metronome" = "Abilita Metronomo";
"sound" = "Suono";
"sound_type" = "Tipo di Suono";
"vibration" = "Vibrazione";
"vibration_strength" = "Intensità Vibrazione";
"alert_frequency" = "Frequenza Avvisi";
"largo" = "Largo";
"adagio" = "Adagio";
"andante" = "Andante";
"moderato" = "Moderato";
"allegro" = "Allegro";
"presto" = "Presto";
"prestissimo" = "Prestissimo";
"default_beat" = "Battito Predefinito";
"beat_2" = "Battito 2";
"beat_3" = "Battito 3";
"beat_1" = "Battito 1";
"beat_4" = "Battito 4";
"beat_5" = "Battito 5";
"beat_6" = "Battito 6";
"feedback" = "Suono e Vibrazione";
"feedback_footer" = "Scegli tra diversi tipi di suono del metronomo quando il suono è abilitato";

// NameSettingView
"name" = "Nome";
"enter_your_name" = "Inserisci il tuo nome";
"cancel" = "Annulla";
"save" = "Salva";
"invalid_name" = "Nome non valido";
"please_enter_valid_name" = "Inserisci un nome valido";

// WeightSettingView
"weight" = "Peso";
"unit" = "Unità";
"calorie_calculations_required" = "Necessario per calcoli accurati delle calorie";
"invalid_weight" = "Peso non valido";
"please_enter_valid_weight" = "Inserisci un peso valido tra %d e %d %@";

// GenderSettingView
"gender" = "Genere";
"gender_footer" = "Opzionale - Usato per calcoli più accurati delle calorie";

// AgeSettingView
"age" = "Età";
"birth_date" = "Data di nascita";
"years_old" = "anni";
"invalid_age" = "Età non valida";
"age_requirement" = "Devi avere almeno 13 anni per utilizzare questa app";

// HeightSettingView
"height" = "Altezza";
"height_cm" = "Altezza (cm)";
"feet" = "Piedi";
"inches" = "Pollici";
"bmi_calculations" = "Opzionale - Usato per calcoli BMI";
"invalid_height" = "Altezza non valida";
"please_enter_valid_height" = "Inserisci un'altezza valida.";

// SettingsView
"settings" = "Impostazioni";
"profile" = "Profilo";
"not_set" = "Non impostato";
"age" = "Età";
"years" = "anni";
"audio" = "Audio";
"preferences" = "Preferenze";
"metronome" = "Metronomo";
"bpm" = "bpm";
"units" = "Unità";
"metric" = "Metrico";
"imperial" = "Imperiale";
"theme" = "Tema";
"language" = "Lingua";
"data" = "Dati";
"all_activities" = "Tutti gli Allenamenti";
"export_activities" = "Esporta Allenamenti";
"exporting" = "Esportazione in corso...";
"error" = "Errore";
"ok" = "OK";
"unknown_error" = "Si è verificato un errore sconosciuto";
"system_default" = "Predefinito Sistema";
"light" = "Chiaro";
"dark" = "Scuro";
"male" = "Maschio";
"female" = "Femmina";
"other" = "Altro";
"prefer_not_to_say" = "Preferisco non dire";

// UnitsSettingView
"unit_system" = "Sistema Unità";

// SportTypeSelector
"run" = "Corsa";
"walk" = "Camminata Sportiva";
"hike" = "Trekking";
"bike" = "Ciclismo";

// ProfileHeaderView
"your_name" = "Il tuo nome";
"email_example" = "<EMAIL>";
"change" = "Modifica";
"delete_photo" = "Eliminare la foto?";
"delete" = "Elimina";

// ActivitySummaryComponents
"total" = "Totale";
"avg" = "Media";
"per_day" = "al giorno";
"per_month" = "al mese";

// PeriodIndicatorView
"current_week" = "Settimana corrente";
"week_ago_format" = "%d settimana fa (%d)";
"weeks_ago_format" = "%d settimane fa (%d)";
"current_month" = "Mese corrente";
"month_ago_format" = "%d mese fa (%d)";
"months_ago_format" = "%d mesi fa (%d)";
"current_year" = "Anno corrente";
"year_ago_format" = "%d anno fa (%d)";
"years_ago_format" = "%d anni fa (%d)";

// TimePeriodPicker
"time_period" = "Periodo di Tempo";
"seven_days" = "1 settimana";
"one_month" = "1 Mese";
"one_year" = "1 Anno";
"all_time" = "Tutto il Tempo";

// For timeOfDay values
"morning" = "Allenamento Mattutino";
"afternoon" = "Allenamento Pomeridiano";
"evening" = "Corsa Serale";
"night" = "Allenamento Notturno";

"every_beat" = "Ogni Battito";
"every_other_beat" = "Ogni Due Battiti";
"every_4th_beat" = "Ogni 4 Battiti";
"every_6th_beat" = "Ogni 6 Battiti";

"per_day" = "al giorno";
"per_month" = "al mese";

// MARK: - Welcome View
"welcome.title" = "Benvenuto su RunApp";
"welcome.subtitle" = "Preparati, ogni passo conta!";
"welcome.button.start" = "Inizia";

// MARK: - Basic Info View
"basicInfo.header.title" = "Personalizza l'esperienza";
"basicInfo.header.subtitle" = "per la stima delle Calorie";

// MARK: - Gender Selection
"basicInfo.gender.title" = "Qual è il tuo genere?";
"basicInfo.gender.male" = "Maschio";
"basicInfo.gender.female" = "Femmina";
"basicInfo.gender.preferNotToSay" = "Preferisco Non Dire";
"basicInfo.button.skip" = "Salta";

// MARK: - Weight Input
"basicInfo.weight.title" = "Qual è il tuo peso?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lbs";

// MARK: - Height Input
"basicInfo.height.title" = "Qual è la tua altezza?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "FT";
"basicInfo.height.placeholder.in" = "IN";

"basicInfo.button.continue" = "Continua";

// MARK: - All Set View
"allSet.title" = "Sei Pronto!";
"allSet.subtitle.first" = "È ora di scatenare la potenza";
"allSet.subtitle.second" = "Niente scuse, solo pura energia!";
"allSet.button.go" = "Scatta!";

// MARK: - ContentView Alerts
"alert.location.title" = "Permesso di Localizzazione Richiesto";
"alert.location.message" = "Abilita 'Consenti Sempre' per la posizione per tracciare perfettamente i percorsi e mantenere i tuoi progressi accurati. Aggiorna le tue impostazioni.";
"alert.location.settings" = "Impostazioni";
"alert.location.later" = "Più tardi";

"alert.complete.title" = "Completare l'Allenamento?";
"alert.complete.message" = "Questo terminerà la tua attività corrente.\nNon potrai riprendere dopo il completamento.";
"alert.complete.confirm" = "Completa";
"alert.complete.cancel" = "Annulla";

"alert.shortWorkout.title" = "Conferma Allenamento Breve";
"alert.shortWorkout.message" = "Il tuo allenamento è inferiore a 30 secondi. Sei sicuro di volerlo salvare?";
"alert.shortWorkout.save" = "Salva";
"alert.shortWorkout.discard" = "Scarta";

// MARK: - Map Markers
"map.marker.start" = "Inizio";
"map.marker.end" = "Fine";
"map.location.unknown" = "Posizione Sconosciuta";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "Metronomo Attivo";
"metronome.status.off" = "Metronomo Disattivo";

"activity.summary.title" = "Riepilogo attività";

// GPS Status
"gps.searching" = "Ricerca GPS...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "Accesso illimitato attivo — scatena la tua potenza!";
"settings.subscription.status.exceeded" = "Hai raggiunto il limite della prova gratuita. Abbonati ora e continua a bruciare la strada!";
"settings.subscription.status.oneRemaining" = "Ti resta 1 allenamento gratuito — abbonati subito per non fermare la tua corsa!";
"settings.subscription.status.remaining" = "Ti restano %d allenamenti gratuiti — abbonati ora e continua a spingere al massimo!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Abbonamento";
"settings.subscription.unlimitedAccess" = "Accesso Illimitato";
"settings.subscription.checkingStatus" = "Verifica in corso...";
"settings.subscription.alert.alreadySubscribed.title" = "Già Abbonato";
"settings.subscription.alert.alreadySubscribed.message" = "Hai già accesso illimitato.";
"settings.subscription.alert.checkFailed.format" = "Verifica dello stato dell'abbonamento fallita: %@";

// AudioAlertSettingsView
"audio_prompts" = "Avvisi Audio";
"enable_audio_prompts" = "Abilita Avvisi Audio";
"audio_prompts_footer" = "Ricevi annunci vocali durante le tue corse";
"distance_alerts" = "Avvisi di Distanza";
"distance_alerts_footer" = "Annuncia ogni traguardo di distanza";
"time_alerts" = "Avvisi di Tempo";
"time_alerts_footer" = "Annuncia ogni intervallo di tempo";
"calorie_alerts" = "Avvisi di Calorie";
"calorie_alerts_footer" = "Annuncia traguardi di calorie";
"pace_alerts" = "Avvisi di Ritmo";
"pace_alerts_footer" = "Annuncia il ritmo attuale a intervalli";
"custom" = "Personalizzato";
"value" = "Valore";
"min" = "min";
"cal" = "cal";
"enabled" = "Abilitato";
"disabled" = "Disabilitato";
"hour" = "ora";
"h" = "h";
"m" = "m";
"distance_short" = "Distanza";
"time_short" = "Tempo";
"calorie_short" = "Calorie";
"pace_short" = "Ritmo";
"no_alerts_configured" = "Nessuno impostato";
"all_alerts" = "Tutti abilitati";
"enter_custom_value" = "Inserisci valore personalizzato";
"integers_only" = "Solo numeri interi";
"invalid_input" = "Input Non Valido";
"please_enter_positive_number" = "Inserisci un numero positivo";
"please_enter_whole_number" = "Inserisci un numero intero (senza decimali)";
"please_enter_value_between" = "Inserisci un valore tra %@ e %@";
"invalid" = "Non Valido";

"per_day" = "al giorno";

// MARK: - Custom Value Interface
"custom_interval" = "Intervallo Personalizzato";
"interval_value" = "Valore Intervallo";
"enter_value_placeholder" = "Inserisci valore";
"whole_numbers_only" = "Solo numeri interi";
"valid_range" = "Intervallo valido: %@ - %@";
"preset_options" = "Opzioni rapide:";
"custom_value" = "Valore Personalizzato";

// MARK: - Audio Volume Settings
"audio_volume" = "Volume Audio";
"audio_volume_footer" = "Regola il volume del metronomo";
"volume" = "Volume";
"voice_volume" = "Volume Voce";
"audio_settings" = "Impostazioni Audio";
"audio_settings_footer" = "Regola il volume e la velocità degli annunci vocali";
"test_voice" = "Testa Voce";

// MARK: - Smart Voice Messages
"smart_voice" = "Voce Intelligente";
"smart_voice_footer" = "Attiva annunci vocali naturali e motivanti invece degli annunci standard";

// Test messages
"audio.test.standard" = "Gli annunci audio funzionano correttamente";
"audio.test.smart" = "Perfetto! Il tuo audio è eccellente, pronto a motivarti!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "Bravissimo! Hai completato %@ %@";
"audio.distance.achievement.smart" = "Eccezionale! Hai raggiunto il traguardo di %@ %@! Sei inarrestabile!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "Sei in movimento da %@. Continua con questo ritmo fantastico!";

// Calorie messages
"audio.calories.standard" = "%@ calorie";
"audio.calories.smart" = "Hai bruciato %@ calorie! I tuoi sforzi stanno dando frutti!";
"audio.calories.achievement.smart" = "Spettacolare! %@ calorie bruciate! Sei una macchina brucia-calorie!";

// Pace messages
"audio.pace.standard" = "Ritmo attuale: %@";
"audio.pace.smart" = "Stai mantenendo un ritmo di %@. Sembri molto forte!";

// Unit strings for audio
"audio.unit.km.singular" = "chilometro";
"audio.unit.km.plural" = "chilometri";
"audio.unit.mile.singular" = "miglio";
"audio.unit.mile.plural" = "miglia";

// Time formatting for audio
"audio.time.minute.singular" = "1 minuto";
"audio.time.minutes" = "%d minuti";
"audio.time.hour.singular" = "1 ora";
"audio.time.hours" = "%d ore";
"audio.time.hours.minutes" = "%d ore e %d minuti";

// Pace formatting for audio
"audio.pace.per.km" = "per chilometro";
"audio.pace.per.mile" = "per miglio";
"audio.pace.seconds" = "%d secondi %@";
"audio.pace.minutes" = "%d minuti %@";
"audio.pace.minutes.seconds" = "%d minuti e %d secondi %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Standard";
"voice.quality.enhanced" = "Migliorata";
"voice.quality.premium" = "Premium";
"voice.manager.test.text" = "Questo è un test della voce selezionata per i tuoi avvisi di corsa";
"voice.test.sample" = "Questo è un test della voce selezionata per i tuoi avvisi di corsa";
"voice.selection.title" = "Selezione Voce";
"voice.current" = "Voce Attuale";
"voice.preview" = "Anteprima";
"voice.none.available" = "Nessuna voce disponibile per questa lingua";
"voice.loading" = "Caricamento voci...";

// MARK: - Voice Selection UI
"voice.download.required" = "Download Richiesto";
"voice.loading.voices" = "Caricamento voci...";
"voice.empty.title" = "Nessuna Voce Disponibile";
"voice.empty.message" = "Nessuna voce migliorata o premium è disponibile per questa lingua. Scarica voci di alta qualità dalle Impostazioni per migliorare la tua esperienza.";
"voice.refresh.voices" = "Aggiorna Voci";
"voice.refresh.footer" = "Tocca per aggiornare l'elenco delle voci disponibili";
"voice.current.language" = "Lingua Attuale";
"voice.selection.description" = "Seleziona la tua voce preferita per gli avvisi audio.\nLe opzioni migliorate e premium offrono la massima qualità.";
"voice.preview.error.title" = "Anteprima Fallita";
"voice.preview.error.message" = "Impossibile visualizzare l'anteprima della voce '%@'. Riprova.";
"voice.add.new" = "Aggiungi Nuova Voce";
"voice.manage.description" = "Vai su Impostazioni > Accessibilità > Contenuti Parlati > Voci per scaricare e gestire le voci";
"voice.system.default" = "Predefinita del Sistema";

// MARK: - Voice Instructions
"voice.instructions.title" = "Scarica Nuove Voci";
"voice.instructions.message" = "Segui questi passaggi per scaricare voci di alta qualità:\n\n1. Tocca 'Apri Impostazioni' qui sotto\n2. Naviga su: Accessibilità\n3. Seleziona: Contenuti Parlati\n4. Scegli: Voci\n5. Scarica le tue voci preferite\n6. Torna a RunApp e aggiorna";
"voice.instructions.open.settings" = "Apri Impostazioni";
"voice.instructions.steps.title" = "Come aggiungere voci:";
"voice.instructions.steps.detail" = "Impostazioni → Accessibilità → Contenuti Parlati → Voci";
"voice.instructions.footer" = "Tocca il pulsante sopra per vedere le istruzioni passo-passo per scaricare nuove voci";
"voice.add.new.subtitle" = "Ottieni istruzioni passo-passo";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Selezione Voce";
"voice.selection.footer" = "Scegli la tua voce preferita per gli annunci audio";

// Common buttons
"common.cancel" = "Annulla";
"common.close" = "Chiudi";
"common.done" = "Fatto";
"common.ok" = "OK";

// MARK: - Voice Management
"voice.delete.success" = "Voce eliminata con successo";
"voice.delete.failed" = "Eliminazione della voce fallita";
"voice.delete.confirm.title" = "Eliminare la voce?";
"voice.delete.confirm.message" = "Questo rimuoverà '%@' dal tuo dispositivo. Potrai scaricarla di nuovo più tardi dalle Impostazioni.";
"voice.delete.confirm.delete" = "Elimina";
"voice.delete.unavailable" = "Questa voce non può essere eliminata perché è una voce predefinita del sistema.";
"voice.manage.voices" = "Gestisci Voci";
"voice.download.status" = "Scaricata";
"voice.builtin.status" = "Integrata";

// MARK: - Smart Voice Prompt Variations (Italian)
// Distance Smart Variations - Italiano motivazionale
"audio.distance.smart.1" = "Incredibile! Hai già corso %@ %@! Sei fortissimo!";
"audio.distance.smart.2" = "Grande! %@ %@ conquistati! Continua così, campione!";
"audio.distance.smart.3" = "Fantastico! %@ %@ in tasca! Che macchina!";
"audio.distance.smart.4" = "Spettacolare! %@ %@ completati! Dai che ce la fai!";
"audio.distance.smart.5" = "Fenomenale! %@ %@ raggiunti! Sei un asso!";
"audio.distance.smart.6" = "Bestiale! %@ %@ dominati! Non fermarti!";
"audio.distance.smart.7" = "Geniale! %@ %@ superati! Che potenza!";
"audio.distance.smart.8" = "Magnifico! %@ %@ sbloccati! Sei fuoco!";
"audio.distance.smart.9" = "Straordinario! %@ %@ spazzati via! Pura forza!";
"audio.distance.smart.10" = "Leggendario! %@ %@ demoliti! Sei il re!";

// Time Smart Variations - Italiano motivazionale
"audio.time.smart.1" = "Già %@ di corsa! Che resistenza!";
"audio.time.smart.2" = "Wow! %@ di pura energia! Continua a spingere!";
"audio.time.smart.3" = "Fantastico! %@ senza sosta! Sei una macchina!";
"audio.time.smart.4" = "Incredibile! %@ di determinazione! Dai forte!";
"audio.time.smart.5" = "Fenomenale! %@ concentrato! Vai che vai!";
"audio.time.smart.6" = "Bestiale! %@ di sforzo! Che guerriero!";
"audio.time.smart.7" = "Geniale! %@ inarrestabile! Continua!";
"audio.time.smart.8" = "Magnifico! %@ a lottare! Sei fuoco!";
"audio.time.smart.9" = "Straordinario! %@ di passione! Sei il migliore!";
"audio.time.smart.10" = "Leggendario! %@ a distruggere! Pura gloria!";

// Calories Smart Variations - Italiano motivazionale
"audio.calories.smart.1" = "Wow! Hai bruciato %@ calorie! Sei fuoco!";
"audio.calories.smart.2" = "Fantastico! %@ calorie eliminate! Continua a bruciare!";
"audio.calories.smart.3" = "Incredibile! %@ calorie dominate! Che macchina!";
"audio.calories.smart.4" = "Fenomenale! %@ calorie conquistate! Vai!";
"audio.calories.smart.5" = "Bestiale! %@ calorie demolite! Non fermarti!";
"audio.calories.smart.6" = "Geniale! %@ calorie spazzate! Sei un asso!";
"audio.calories.smart.7" = "Magnifico! %@ calorie distrutte! Continua!";
"audio.calories.smart.8" = "Straordinario! %@ calorie vaporizzate! Inarrestabile!";
"audio.calories.smart.9" = "Spettacolare! %@ calorie annientate! Modalità bestia!";
"audio.calories.smart.10" = "Leggendario! %@ calorie polverizzate! Sei il re!";

// Pace Smart Variations - Italiano motivazionale
"audio.pace.smart.1" = "Perfetto! Ritmo di %@! Che controllo!";
"audio.pace.smart.2" = "Wow! %@ di velocità! Pura potenza!";
"audio.pace.smart.3" = "Fantastico! %@ controllato! Sei una macchina!";
"audio.pace.smart.4" = "Incredibile! %@ dominato! Che tecnica!";
"audio.pace.smart.5" = "Fenomenale! %@ perfetto! Continua così!";
"audio.pace.smart.6" = "Bestiale! %@ fluido! Sei un asso!";
"audio.pace.smart.7" = "Geniale! %@ professionale! Che livello!";
"audio.pace.smart.8" = "Magnifico! %@ inarrestabile! Sei fuoco!";
"audio.pace.smart.9" = "Straordinario! %@ da sogno! Modalità bestia!";
"audio.pace.smart.10" = "Leggendario! %@ distruttivo! Sei il re!";

// MARK: - Workout Settings
"workout.settings.title" = "Impostazioni Allenamento";
"workout.settings.notice" = "Allenamento in Corso";
"workout.settings.safe.only" = "Solo impostazioni sicure per l'allenamento disponibili";

// MARK: - Voice Premium Features
"voice.selection.simple.description" = "Scegli la tua voce preferita per gli avvisi audio durante le tue corse.";
"voice.premium.benefits.recommendation" = "Consigliamo di aggiungere voci PREMIUM per la migliore esperienza";
"voice.premium.benefit.clarity" = "Chiarezza audio superiore per gli allenamenti all'aperto";
"voice.premium.benefit.noise" = "Prestazioni migliori in ambienti rumorosi";
"voice.premium.benefit.motivation" = "Tono di voce più naturale e motivante";
"voice.management.footer" = "Scarica nuove voci dalle Impostazioni iOS e aggiorna per vedere le voci appena installate";
"voice.selection.workout.description" = "Scegli la tua voce per gli avvisi audio dell'allenamento. Le voci premium offrono l'esperienza più chiara e motivante durante le corse.";

// MARK: - Speech Speed Settings
"speech_speed" = "Velocità di Pronuncia";
"speech_speed_footer" = "Controlla la velocità di pronuncia degli avvisi vocali. Velocità più lente aiutano la comprensione durante allenamenti intensi o in ambienti rumorosi.";

// MARK: - Data Management
"data_management" = "Gestione Dati";
"data_privacy" = "Privacy dei Dati";
"export_data" = "Esporta Dati";
"import_data" = "Importa Dati";
"delete_data" = "Elimina Dati";

// Privacy Messages
"privacy_ownership" = "Mantieni la proprietà dei tuoi dati";
"privacy_icloud_storage" = "Archiviati in sicurezza nel tuo iCloud";
"privacy_no_access" = "Noi (come sviluppatori) non possiamo accedere ai tuoi dati personali";
"privacy_full_control" = "Mantieni il controllo completo dei tuoi dati";

// Export
"export_all_data" = "Esporta Tutti i Dati";
"export_profile_only" = "Esporta Solo Profilo";
"export_activities_only" = "Esporta Solo Attività";
"export_all_description" = "Profilo e attività in 2 file";
"export_profile_description" = "Impostazioni personali e preferenze";
"export_activities_description" = "Tutti i dati di allenamento e statistiche";
"export_data_description" = "Esporta i tuoi dati come file CSV per backup o trasferimento";

// Import
"import_data_files" = "Importa File Dati";
"import_data_files_description" = "Seleziona file CSV esportati in precedenza";
"import_data_description" = "Ripristina da file esportati in precedenza";

// Delete
"delete_all_data" = "Elimina Tutti i Dati";
"delete_all_data_description" = "Rimuovi permanentemente tutti i dati";
"delete_data_description" = "Questa azione non può essere annullata";
"delete_confirmation" = "Conferma Eliminazione";
"delete_confirmation_message" = "Questo eliminerà permanentemente tutti i tuoi dati del profilo e attività. Questa azione non può essere annullata.";
"delete_permanently" = "Elimina Permanentemente";

// Status Messages
"importing" = "Importazione...";
"deleting" = "Eliminazione...";
"export_failed" = "Esportazione fallita";
"import_failed" = "Importazione fallita";
"delete_failed" = "Eliminazione fallita";
"import_success" = "Importazione Riuscita";
"import_success_message" = "I dati sono stati importati con successo";
"delete_success" = "Dati Eliminati";
"delete_success_message" = "Tutti i tuoi dati sono stati eliminati permanentemente. Ora tornerai alla schermata principale.";
"import_error" = "Errore di Importazione";
"export_error" = "Errore di Esportazione";
"delete_error" = "Errore di Eliminazione";

"select_gender_title" = "Seleziona Genere";

// MARK: - Activity Stats View
"act_dist" = "Dist Allenamento";
"act_time" = "Tempo Allenamento";
"act_cal" = "Cal Allenamento";
"performance" = "Performance";
"best_pace" = "Miglior Ritmo";
"avg_speed" = "Velocità Media";
"max_speed" = "Velocità Max";
"time_analysis" = "Analisi Tempo";
"total_time" = "Tempo Totale";
"paused_time" = "Tempo Pausa";
"active_calories" = "Attivo";
"resting_calories" = "Riposo";
"rate" = "Tasso";
"kcal" = "kcal";
"cal_per_min" = "cal/min";
