// ActivityControls
"activities" = "運動紀錄";
"metronome" = "節拍器";

// StatsView
"distance" = "距離";
"active" = "活躍";
"time" = "時間";
"current" = "目前";
"pace" = "配速";
"km" = "公里";
"mi" = "英里";

// ActivityView
"activities" = "運動紀錄";
"no_activities" = "尚無運動紀錄";
"complete_first_run" = "完成首次訓練後會顯示在這裡";

// ActivityRowView
"distance" = "距離";
"duration" = "用時";
"avg_pace" = "平均配速";
"calories" = "卡路里";
"start" = "起跑";
"end" = "衝線";

// ActivityDetailView
"delete_activity" = "刪除運動紀錄";
"are_you_sure_deletion" = "確定要刪除嗎？此動作無法復原。";

// CountdownView
"go" = "出發！";

// LanguageSettingView
"language" = "語言";
"done" = "完成";

// AuthView
"runapp" = "RunApp";
"email" = "電子信箱";
"sign_in_with_email" = "信箱登入";
"check_inbox_login_link" = "請到信箱查看登入連結。";

// AnalysisView
"improve_with_personalized_data" = "用個人化數據科學提升";
"run" = "跑步";
"walk" = "健走";
"hike" = "登山";
"bike" = "單車";
"distance" = "距離";
"duration" = "用時";
"calories" = "卡路里";
"current_week" = "本週";
"current_month" = "本月";
"current_year" = "今年";
"per_day" = "每日";
"per_month" = "每月";
"generate_test_data" = "產生測試資料";

// MetronomeSettingsView
"metronome_settings" = "節拍器";
"tempo" = "節奏速度";
"tempo_footer" = "調整每分鐘 40-240 拍";
"enable_metronome" = "啟用節拍器";
"sound" = "聲音";
"sound_type" = "聲音類型";
"vibration" = "震動";
"vibration_strength" = "震動強度";
"alert_frequency" = "提醒頻率";
"largo" = "慢板";
"adagio" = "柔板";
"andante" = "行板";
"moderato" = "中板";
"allegro" = "快板";
"presto" = "急板";
"prestissimo" = "極急板";
"default_beat" = "預設節拍";
"beat_2" = "節拍2";
"beat_3" = "節拍3";
"beat_1" = "節拍1";
"beat_4" = "節拍4";
"beat_5" = "節拍5";
"beat_6" = "節拍6";
"feedback" = "聲音與震動";
"feedback_footer" = "啟用聲音後可選擇節拍器的音效類型";

// NameSettingView
"name" = "姓名";
"enter_your_name" = "請輸入你的姓名";
"cancel" = "取消";
"save" = "儲存";
"invalid_name" = "姓名無效";
"please_enter_valid_name" = "請輸入有效的姓名";

// WeightSettingView
"weight" = "體重";
"unit" = "單位";
"calorie_calculations_required" = "精準計算卡路里消耗必需";
"invalid_weight" = "體重無效";
"please_enter_valid_weight" = "請輸入介於 %d 到 %d %@ 的有效體重";

// GenderSettingView
"gender" = "性別";
"gender_footer" = "選填 - 可協助更精準地計算卡路里";

// AgeSettingView
"age" = "年齡";
"birth_date" = "出生日期";
"years_old" = "歲";
"invalid_age" = "年齡無效";
"age_requirement" = "使用本 App 需年滿 13 歲";

// HeightSettingView
"height" = "身高";
"height_cm" = "身高（公分）";
"feet" = "英呎";
"inches" = "英吋";
"bmi_calculations" = "選填 - 用於 BMI 計算";
"invalid_height" = "身高無效";
"please_enter_valid_height" = "請輸入正確的身高";

// SettingsView
"settings" = "設定";
"profile" = "個人檔案";
"not_set" = "尚未設定";
"age" = "年齡";
"years" = "歲";
"audio" = "音訊";
"preferences" = "偏好";
"metronome" = "節拍器";
"bpm" = "BPM";
"units" = "單位";
"metric" = "公制";
"imperial" = "英制";
"theme" = "主題風格";
"language" = "語言";
"data" = "資料";
"all_activities" = "所有運動";
"export_activities" = "匯出運動資料";
"exporting" = "匯出中...";
"error" = "錯誤";
"ok" = "確定";
"unknown_error" = "發生未知錯誤";
"system_default" = "系統預設";
"light" = "淺色模式";
"dark" = "深色模式";
"male" = "男性";
"female" = "女性";
"other" = "其他";
"prefer_not_to_say" = "不想透露";

// UnitsSettingView
"unit_system" = "單位系統";

// SportTypeSelector
"run" = "跑步";
"walk" = "健走";
"hike" = "登山";
"bike" = "單車";

// ProfileHeaderView
"your_name" = "你的姓名";
"email_example" = "<EMAIL>";
"change" = "變更";
"delete_photo" = "刪除照片？";
"delete" = "刪除";

// ActivitySummaryComponents
"total" = "總計";
"avg" = "平均";
"per_day" = "每日";
"per_month" = "每月";

// PeriodIndicatorView
"current_week" = "本週";
"week_ago_format" = "%d 週前（%d）";
"weeks_ago_format" = "%d 週前（%d）";
"current_month" = "本月";
"month_ago_format" = "%d 個月前（%d）";
"months_ago_format" = "%d 個月前（%d）";
"current_year" = "今年";
"year_ago_format" = "%d 年前（%d）";
"years_ago_format" = "%d 年前（%d）";

// TimePeriodPicker
"time_period" = "時間範圍";
"seven_days" = "1 週";
"one_month" = "1 個月";
"one_year" = "1 年";
"all_time" = "全部時間";

// For timeOfDay values
"morning" = "晨練";
"afternoon" = "午練";
"evening" = "夜跑";
"night" = "夜練";

"every_beat" = "每一拍";
"every_other_beat" = "每兩拍";
"every_4th_beat" = "每 4 拍";
"every_6th_beat" = "每 6 拍";

// AudioAlertSettingsView
"audio_prompts" = "語音提醒";
"enable_audio_prompts" = "啟用語音提醒";
"audio_prompts_footer" = "運動過程中接收語音播報";
"distance_alerts" = "距離提醒";
"distance_alerts_footer" = "播報每個距離里程碑";
"time_alerts" = "時間提醒";
"time_alerts_footer" = "播報每個時間間隔";
"calorie_alerts" = "卡路里提醒";
"calorie_alerts_footer" = "播報卡路里燃燒里程碑";
"pace_alerts" = "配速提醒";
"pace_alerts_footer" = "定時播報當前配速";
"custom" = "自訂";
"value" = "數值";
"min" = "分鐘";
"cal" = "卡";
"enabled" = "已啟用";
"disabled" = "已停用";
"hour" = "小時";
"h" = "時";
"m" = "分";
"distance_short" = "距離";
"time_short" = "時間";
"calorie_short" = "卡路里";
"pace_short" = "配速";
"no_alerts_configured" = "未設定";
"all_alerts" = "全部啟用";
"enter_custom_value" = "輸入自訂值";
"integers_only" = "僅限整數";
"invalid_input" = "輸入無效";
"please_enter_positive_number" = "請輸入正數";
"please_enter_whole_number" = "請輸入整數（無小數）";
"please_enter_value_between" = "請輸入 %@ 到 %@ 之間的值";
"invalid" = "無效";

"per_day" = "每日";
"per_month" = "每月";

// MARK: - Welcome View
"welcome.title" = "歡迎使用 RunApp";
"welcome.subtitle" = "裝備就緒，每一步都算數！";
"welcome.button.start" = "開始訓練";

// MARK: - Basic Info View
"basicInfo.header.title" = "個人化設定";
"basicInfo.header.subtitle" = "精準計算卡路里消耗";

// MARK: - Gender Selection
"basicInfo.gender.title" = "你的性別？";
"basicInfo.gender.male" = "男性";
"basicInfo.gender.female" = "女性";
"basicInfo.gender.preferNotToSay" = "不想說";
"basicInfo.button.skip" = "跳過";

// MARK: - Weight Input
"basicInfo.weight.title" = "你的體重是？";
"basicInfo.weight.unit.kg" = "公斤";
"basicInfo.weight.unit.lbs" = "磅";

// MARK: - Height Input
"basicInfo.height.title" = "你的身高是？";
"basicInfo.height.unit.cm" = "公分";
"basicInfo.height.unit.ftIn" = "英呎";
"basicInfo.height.placeholder.cm" = "公分";
"basicInfo.height.placeholder.ft" = "英呎";
"basicInfo.height.placeholder.in" = "英吋";

"basicInfo.button.continue" = "繼續";

// MARK: - All Set View
"allSet.title" = "一切準備好了！";
"allSet.subtitle.first" = "該開始揮汗如雨了";
"allSet.subtitle.second" = "沒有藉口，只有行動！";
"allSet.button.go" = "出發！";

// MARK: - ContentView Alerts
"alert.location.title" = "需要定位權限";
"alert.location.message" = "請啟用「永遠允許」位置權限，以精準追蹤路線並保持運動數據正確。請到設定中開啟。";
"alert.location.settings" = "前往設定";
"alert.location.later" = "稍後提醒";

// MARK: - Completion Alerts
"alert.complete.title" = "要結束運動嗎？";
"alert.complete.message" = "這會結束目前的運動。\n結束後無法再繼續。";
"alert.complete.confirm" = "完成";
"alert.complete.cancel" = "取消";

"alert.shortWorkout.title" = "確認短時運動";
"alert.shortWorkout.message" = "這次訓練少於 30 秒，確定要儲存嗎？";
"alert.shortWorkout.save" = "儲存";
"alert.shortWorkout.discard" = "放棄";

// MARK: - Map Markers
"map.marker.start" = "起跑點";
"map.marker.end" = "終點";
"map.location.unknown" = "未知位置";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "節拍器已開啟";
"metronome.status.off" = "節拍器已關閉";

"activity.summary.title" = "運動摘要";

// GPS Status
"gps.searching" = "正在搜尋 GPS 訊號...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "你已解鎖無限訓練，盡情揮灑汗水，燃燒熱血！";
"settings.subscription.status.exceeded" = "免費試用已用完！立即訂閱，繼續爆發你的運動潛力！";
"settings.subscription.status.oneRemaining" = "你還有 1 次免費訓練 — 把握節奏，馬上訂閱！";
"settings.subscription.status.remaining" = "還有 %d 次免費訓練 — 趕緊訂閱，持續燃燒熱血！";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "訂閱方案";
"settings.subscription.unlimitedAccess" = "無限訓練";
"settings.subscription.checkingStatus" = "檢查中...";
"settings.subscription.alert.alreadySubscribed.title" = "已訂閱";
"settings.subscription.alert.alreadySubscribed.message" = "你已享有無限訓練權限。";
"settings.subscription.alert.checkFailed.format" = "無法檢查訂閱狀態：%@";

// MARK: - Custom Value Interface
"custom_interval" = "自訂間隔";
"interval_value" = "間隔值";
"enter_value_placeholder" = "輸入數值";
"whole_numbers_only" = "僅限整數";
"valid_range" = "有效範圍：%@ - %@";
"preset_options" = "快速選項：";
"custom_value" = "自訂值";

// MARK: - Audio Volume Settings
"audio_volume" = "音訊音量";
"audio_volume_footer" = "調節節拍器音量";
"volume" = "音量";
"voice_volume" = "語音音量";
"speech_speed" = "語音速度";
"speech_speed_footer" = "控制語音提醒的播放速度。在高強度訓練或嘈雜環境中，較慢的速度有助於理解。";
"audio_settings" = "音訊設定";
"audio_settings_footer" = "調節語音播報音量和速度";
"test_voice" = "測試語音";

// MARK: - Smart Voice Messages
"smart_voice" = "智慧語音";
"smart_voice_footer" = "啟用自然且富有激勵性的語音提醒，取代標準播報";

// Test messages
"audio.test.standard" = "語音提醒正常運作";
"audio.test.smart" = "加油！你的語音清晰完美，準備為你打氣加油！";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "太棒了！你已完成 %@ %@";
"audio.distance.achievement.smart" = "超強！%@ %@ 里程碑達成！你真的太厲害了！";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "你已經堅持了 %@，繼續保持這種絕佳狀態！";

// Calorie messages
"audio.calories.standard" = "%@ 卡路里";
"audio.calories.smart" = "你已燃燒 %@ 卡路里！努力正在發揮效果！";
"audio.calories.achievement.smart" = "驚人！%@ 卡路里燃燒完成！你就是燃脂達人！";

// Taiwan-specific calorie achievements
"audio.calories.achievement.50.smart" = "好棒！50卡路里入袋！繼續保持這股衝勁！";
"audio.calories.achievement.100.smart" = "超讚！100卡路里燃燒完畢！你的堅持正在創造驚喜！";

// Pace messages
"audio.pace.standard" = "目前配速：%@";
"audio.pace.smart" = "你正保持 %@ 的配速，節奏超棒！";

// Unit strings for audio
"audio.unit.km.singular" = "公里";
"audio.unit.km.plural" = "公里";
"audio.unit.mile.singular" = "英里";
"audio.unit.mile.plural" = "英里";

// Time formatting for audio
"audio.time.minute.singular" = "1分鐘";
"audio.time.minutes" = "%d分鐘";
"audio.time.hour.singular" = "1小時";
"audio.time.hours" = "%d小時";
"audio.time.hours.minutes" = "%d小時%d分鐘";

// Pace formatting for audio
"audio.pace.per.km" = "每公里";
"audio.pace.per.mile" = "每英里";
"audio.pace.seconds" = "%d秒%@";
"audio.pace.minutes" = "%d分鐘%@";
"audio.pace.minutes.seconds" = "%d分%d秒%@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "標準";
"voice.quality.enhanced" = "增強";
"voice.quality.premium" = "高級";
"voice.manager.test.text" = "這是為您的跑步提醒選擇的語音測試";
"voice.test.sample" = "這是為您的跑步提醒選擇的語音測試";
"voice.selection.title" = "語音選擇";
"voice.current" = "目前語音";
"voice.preview" = "預覽";
"voice.none.available" = "此語言沒有可用語音";
"voice.loading" = "正在載入語音...";

// MARK: - Voice Selection UI
"voice.download.required" = "需要下載";
"voice.loading.voices" = "正在載入語音...";
"voice.empty.title" = "沒有可用語音";
"voice.empty.message" = "此語言沒有增強或高級語音。從設定中下載高品質語音以改善體驗。";
"voice.refresh.voices" = "重新整理語音";
"voice.refresh.footer" = "點擊重新整理可用語音清單";
"voice.current.language" = "目前語言";
"voice.selection.description" = "為語音提醒選擇您偏好的語音。\n增強和高級選項提供最高品質。";
"voice.selection.workout.description" = "為運動語音提醒選擇您的語音。高級語音在跑步過程中提供最清晰、最激勵人心的體驗。";
"voice.selection.simple.description" = "為運動語音提醒選擇您偏好的語音。";
"voice.preview.error.title" = "預覽失敗";
"voice.preview.error.message" = "無法預覽語音「%@」。請重試。";
"voice.add.new" = "新增語音";
"voice.manage.description" = "前往 設定 > 輔助使用 > 朗讀內容 > 語音 下載和管理語音";
"voice.system.default" = "系統預設";

// MARK: - Voice Instructions
"voice.instructions.title" = "下載新語音";
"voice.instructions.message" = "按照以下步驟下載高品質語音：\n\n1. 點擊下方「開啟設定」\n2. 導航至：輔助使用\n3. 選擇：朗讀內容\n4. 選擇：語音\n5. 下載您偏好的語音\n6. 返回 RunApp 並重新整理";
"voice.instructions.open.settings" = "開啟設定";
"voice.instructions.steps.title" = "如何新增語音：";
"voice.instructions.steps.detail" = "設定 → 輔助使用 → 朗讀內容 → 語音";
"voice.instructions.footer" = "點擊上方按鈕查看下載新語音的分步說明";
"voice.add.new.subtitle" = "取得分步說明";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "語音選擇";
"voice.selection.footer" = "為語音播報選擇您偏好的語音";

// Common buttons
"common.cancel" = "取消";
"common.close" = "關閉";
"common.done" = "完成";
"common.ok" = "確定";

// MARK: - Voice Management
"voice.delete.success" = "語音刪除成功";
"voice.delete.failed" = "刪除語音失敗";
"voice.delete.confirm.title" = "刪除語音？";
"voice.delete.confirm.message" = "這將從您的裝置中移除 '%@'。您可以稍後從設定中重新下載。";
"voice.delete.confirm.delete" = "刪除";
"voice.delete.unavailable" = "此語音無法刪除，因為它是系統預設語音。";
"voice.manage.voices" = "管理語音";
"voice.download.status" = "已下載";
"voice.builtin.status" = "內建";

// MARK: - Smart Voice Prompt Variations (Traditional Chinese - Taiwan)
// Distance Smart Variations - 台灣口語化
"audio.distance.smart.1" = "已經跑了 %@ %@ 耶！超厲害的啦！";
"audio.distance.smart.2" = "讚啦！%@ %@ 到手！繼續衝衝衝！";
"audio.distance.smart.3" = "哇賽！%@ %@ 輕鬆get！你好棒喔！";
"audio.distance.smart.4" = "太強了！%@ %@ 完成了！加油加油！";
"audio.distance.smart.5" = "厲害耶！%@ %@ 達成！你真的很行！";
"audio.distance.smart.6" = "挖勒！%@ %@ 搞定！繼續拼啦！";
"audio.distance.smart.7" = "好猛喔！%@ %@ 又突破了！";
"audio.distance.smart.8" = "讚讚讚！%@ %@ 成功解鎖！";
"audio.distance.smart.9" = "超棒的！%@ %@ 輕鬆過關！";
"audio.distance.smart.10" = "爆強！%@ %@ 完美達成！你是王者！";

// Time Smart Variations - 台灣口語化
"audio.time.smart.1" = "已經堅持 %@ 耶！真的有毅力！";
"audio.time.smart.2" = " %@ 過去了！還這麼有精神！";
"audio.time.smart.3" = "厲害！%@ 了還在跑！太強了啦！";
"audio.time.smart.4" = "不錯喔！%@ 的堅持！繼續保持！";
"audio.time.smart.5" = "好棒！專注了 %@ ！加油喔！";
"audio.time.smart.6" = "哇賽！%@ 的努力！真的佩服！";
"audio.time.smart.7" = "超讚的！%@ 的堅持！繼續衝！";
"audio.time.smart.8" = "好樣的！%@ 還在拼！真厲害！";
"audio.time.smart.9" = "真棒！%@ 的付出！你最棒了！";
"audio.time.smart.10" = "爆強！%@ 的堅持！無敵啦！";

// Calories Smart Variations - 台灣口語化
"audio.calories.smart.1" = " 已經燒掉 %@ 卡啦！超厲害！";
"audio.calories.smart.2" = "讚啦！%@ 卡路里消滅！繼續燃燒！";
"audio.calories.smart.3" = "哇賽！%@ 卡輕鬆搞定！";
"audio.calories.smart.4" = "太強了！%@ 卡已經拿下！加油！";
"audio.calories.smart.5" = "厲害！%@ 卡路里燒光光！";
"audio.calories.smart.6" = "挖勒！%@ 卡成功消耗！真棒！";
"audio.calories.smart.7" = "好樣的！%@ 卡又突破了！";
"audio.calories.smart.8" = "超讚的！%@ 卡輕鬆過關！";
"audio.calories.smart.9" = "真厲害！%@ 卡完美燃燒！";
"audio.calories.smart.10" = "爆強！%@ 卡路里秒殺！無敵！";

// Pace Smart Variations - 台灣口語化
"audio.pace.smart.1" = "穩住！%@ 的配速！節奏很棒耶！";
"audio.pace.smart.2" = "不錯喔！%@ 配速控制得很好！";
"audio.pace.smart.3" = "厲害！%@ 的節奏！很穩定！";
"audio.pace.smart.4" = "好樣的！%@ 配速保持得真好！";
"audio.pace.smart.5" = "哇賽！%@ 的速度！太完美了啦！";
"audio.pace.smart.6" = "真棒！%@ 配速剛剛好！";
"audio.pace.smart.7" = "超讚的！%@ 的節奏！專業級別！";
"audio.pace.smart.8" = "好厲害！%@ 配速很流暢！";
"audio.pace.smart.9" = "爆強！%@ 的速度！太穩了！";
"audio.pace.smart.10" = "無敵！%@ 配速完美控制！";

// MARK: - Workout Settings
"workout.settings.title" = "運動設定";
"workout.settings.notice" = "運動進行中";
"workout.settings.safe.only" = "僅運動安全設定可用";

// MARK: - Premium Voice Benefits
"voice.premium.benefits.title" = "優質語音帶來最佳運動體驗";
"voice.premium.benefits.subtitle" = "獲得水晶般清晰的語音提醒，在環境噪音中清晰可聞，在高強度訓練中激勵您";
"voice.premium.benefits.recommendation" = "建議新增高級語音以獲得最佳體驗";
"voice.premium.benefit.clarity" = "戶外運動音訊清晰度更佳";
"voice.premium.benefit.noise" = "在嘈雜環境中表現更好";
"voice.premium.benefit.motivation" = "更自然、更激勵人心的語音音調";
"voice.premium.recommended" = "推薦";
"voice.premium.footer" = "高級語音在運動中提供最清晰的音訊，具有更好的抗噪性和自然的語音模式。";
"voice.enhanced.footer" = "增強語音相比標準系統語音提供更好的音質和清晰度。";

// MARK: - Voice Quality Descriptions
"voice.quality.standard.description" = "標準品質語音";
"voice.quality.enhanced.description" = "增強清晰度的音訊";
"voice.quality.premium.description" = "針對運動激勵最佳化";

"voice.management.footer" = "從iOS設定下載新語音，然後重新整理以查看新安裝的語音";

// MARK: - Data Management
"data_management" = "資料管理";
"data_privacy" = "資料隱私";
"export_data" = "匯出資料";
"import_data" = "匯入資料";
"delete_data" = "刪除資料";

// Privacy Messages
"privacy_ownership" = "您保留資料的所有權";
"privacy_icloud_storage" = "安全儲存在您的iCloud中";
"privacy_no_access" = "我們（作為開發者）無法存取您的個人資料";
"privacy_full_control" = "您保持對資料的完全控制";

// Export
"export_all_data" = "匯出所有資料";
"export_profile_only" = "僅匯出個人檔案";
"export_activities_only" = "僅匯出活動";
"export_all_description" = "個人檔案和活動在2個檔案中";
"export_profile_description" = "個人設定和偏好";
"export_activities_description" = "所有運動資料和統計資訊";
"export_data_description" = "將資料匯出為CSV檔案以備份或轉移";

// Import
"import_data_files" = "匯入資料檔案";
"import_data_files_description" = "選擇之前匯出的CSV檔案";
"import_data_description" = "從之前匯出的檔案復原";

// Delete
"delete_all_data" = "刪除所有資料";
"delete_all_data_description" = "永久刪除所有資料";
"delete_data_description" = "此操作無法復原";
"delete_confirmation" = "確認刪除";
"delete_confirmation_message" = "這將永久刪除您的所有個人檔案資料和活動。此操作無法復原。";
"delete_permanently" = "永久刪除";

// Status Messages
"importing" = "正在匯入...";
"deleting" = "正在刪除...";
"export_failed" = "匯出失敗";
"import_failed" = "匯入失敗";
"delete_failed" = "刪除失敗";
"import_success" = "匯入成功";
"import_success_message" = "資料已成功匯入";
"delete_success" = "資料已刪除";
"delete_success_message" = "您的所有資料已被永久刪除。現在將返回主畫面。";
"import_error" = "匯入錯誤";
"export_error" = "匯出錯誤";
"delete_error" = "刪除錯誤";

"select_gender_title" = "選擇性別";

// MARK: - Activity Stats View
"act_dist" = "訓練距離";
"act_time" = "訓練時間";
"act_cal" = "訓練卡";
"performance" = "運動表現";
"best_pace" = "最佳配速";
"avg_speed" = "平均速度";
"max_speed" = "最高速度";
"time_analysis" = "時間分析";
"total_time" = "總時間";
"paused_time" = "暫停時間";
"active_calories" = "運動";
"resting_calories" = "靜息";
"rate" = "燃燒率";
"kcal" = "千卡";
"cal_per_min" = "卡/分";
