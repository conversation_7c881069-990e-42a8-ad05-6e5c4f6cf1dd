// ActivityControls
"activities" = "Entrenamientos";
"metronome" = "Metrónomo";

// StatsView
"distance" = "Distancia";
"active" = "Activo";
"time" = "Tiempo";
"current" = "Actual";
"pace" = "Ritmo";
"km" = "km";
"mi" = "mi";

// ActivityView
"activities" = "Entrenamientos";
"no_activities" = "Sin Entrenamientos";
"complete_first_run" = "Completa tu primer entrenamiento para verlo aquí";

// ActivityRowView
"distance" = "Distancia";
"duration" = "Tiempo";
"avg_pace" = "Ritmo Promedio";
"calories" = "Calorías";
"start" = "Salida";
"end" = "Meta";

// ActivityDetailView
"delete_activity" = "Eliminar Entrenamiento";
"are_you_sure_deletion" = "¿Estás seguro? La eliminación no se puede deshacer.";

// CountdownView
"go" = "¡ACELERA!";

// LanguageSettingView
"language" = "Idioma";
"done" = "Listo";

// AuthView
"runapp" = "RunApp";
"email" = "Correo electrónico";
"sign_in_with_email" = "Iniciar sesión con correo electrónico";
"check_inbox_login_link" = "Revisa tu bandeja de entrada para el enlace de inicio de sesión.";

// AnalysisView
"improve_with_personalized_data" = "Mejora científicamente con datos personalizados";
"run" = "Correr";
"walk" = "Caminar";
"hike" = "Senderismo";
"bike" = "Bicicleta";
"distance" = "Distancia";
"duration" = "Tiempo";
"calories" = "Calorías";
"current_week" = "Semana actual";
"current_month" = "Mes actual";
"current_year" = "Año actual";
"per_day" = "por día";
"per_month" = "por mes";
"generate_test_data" = "Generar datos de prueba";

// MetronomeSettingsView
"metronome_settings" = "Metrónomo";
"tempo" = "Tempo";
"tempo_footer" = "Ajusta el tempo entre 40-240 pulsos por minuto";
"enable_metronome" = "Activar Metrónomo";
"sound" = "Sonido";
"sound_type" = "Tipo de Sonido";
"vibration" = "Vibración";
"vibration_strength" = "Intensidad de Vibración";
"alert_frequency" = "Frecuencia de Alerta";
"largo" = "Largo";
"adagio" = "Adagio";
"andante" = "Andante";
"moderato" = "Moderato";
"allegro" = "Allegro";
"presto" = "Presto";
"prestissimo" = "Prestissimo";
"default_beat" = "Pulso Predeterminado";
"beat_2" = "Pulso 2";
"beat_3" = "Pulso 3";
"beat_1" = "Pulso 1";
"beat_4" = "Pulso 4";
"beat_5" = "Pulso 5";
"beat_6" = "Pulso 6";
"feedback" = "Sonido y Vibración";
"feedback_footer" = "Elige entre diferentes tipos de sonido cuando el sonido está activado";

// NameSettingView
"name" = "Nombre";
"enter_your_name" = "Ingresa tu nombre";
"cancel" = "Cancelar";
"save" = "Guardar";
"invalid_name" = "Nombre Inválido";
"please_enter_valid_name" = "Por favor ingresa un nombre válido";

// WeightSettingView
"weight" = "Peso";
"unit" = "Unidad";
"calorie_calculations_required" = "Requerido para cálculos precisos de calorías";
"invalid_weight" = "Peso Inválido";
"please_enter_valid_weight" = "Por favor ingresa un peso válido entre %d y %d %@";

// GenderSettingView
"gender" = "Género";
"gender_footer" = "Opcional - Usado para cálculos más precisos de calorías";

// AgeSettingView
"age" = "Edad";
"birth_date" = "Fecha de nacimiento";
"years_old" = "años";
"invalid_age" = "Edad Inválida";
"age_requirement" = "Debes tener al menos 13 años para usar esta aplicación";

// HeightSettingView
"height" = "Altura";
"height_cm" = "Altura (cm)";
"feet" = "Pies";
"inches" = "Pulgadas";
"bmi_calculations" = "Opcional - Usado para cálculos de IMC";
"invalid_height" = "Altura Inválida";
"please_enter_valid_height" = "Por favor ingresa una altura válida.";

// SettingsView
"settings" = "Ajustes";
"profile" = "Perfil";
"not_set" = "Sin establecer";
"age" = "Edad";
"years" = "años";
"audio" = "Audio";
"preferences" = "Preferencias";
"metronome" = "Metrónomo";
"bpm" = "ppm";
"units" = "Unidades";
"metric" = "Métrico";
"imperial" = "Imperial";
"theme" = "Tema";
"language" = "Idioma";
"data" = "Datos";
"all_activities" = "Todos los Entrenamientos";
"export_activities" = "Exportar Datos de Entrenamiento";
"exporting" = "Exportando...";
"error" = "Error";
"ok" = "OK";
"unknown_error" = "Ocurrió un error desconocido";
"system_default" = "Predeterminado del Sistema";
"light" = "Claro";
"dark" = "Oscuro";
"male" = "Masculino";
"female" = "Femenino";
"other" = "Otro";
"prefer_not_to_say" = "Prefiero no decirlo";

// UnitsSettingView
"unit_system" = "Sistema de Unidades";

// SportTypeSelector
"run" = "Correr";
"walk" = "Caminar";
"hike" = "Senderismo";
"bike" = "Bicicleta";

// ProfileHeaderView
"your_name" = "Tu Nombre";
"email_example" = "<EMAIL>";
"change" = "Cambiar";
"delete_photo" = "¿Eliminar Foto?";
"delete" = "Eliminar";

// ActivitySummaryComponents
"total" = "Total";
"avg" = "Promedio";
"per_day" = "por día";
"per_month" = "por mes";

// PeriodIndicatorView
"current_week" = "Semana actual";
"week_ago_format" = "Hace %d semana (%d)";
"weeks_ago_format" = "Hace %d semanas (%d)";
"current_month" = "Mes actual";
"month_ago_format" = "Hace %d mes (%d)";
"months_ago_format" = "Hace %d meses (%d)";
"current_year" = "Año actual";
"year_ago_format" = "Hace %d año (%d)";
"years_ago_format" = "Hace %d años (%d)";

// TimePeriodPicker
"time_period" = "Período de Tiempo";
"seven_days" = "1 semana";
"one_month" = "1 Mes";
"one_year" = "1 Año";
"all_time" = "Todo el Tiempo";

// For timeOfDay values
"morning" = "Entrenamiento matutino";
"afternoon" = "Entrenamiento vespertino";
"evening" = "Carrera nocturna";
"night" = "Entrenamiento nocturno";

"every_beat" = "Cada Pulso";
"every_other_beat" = "Cada Dos Pulsos";
"every_4th_beat" = "Cada 4 Pulsos";
"every_6th_beat" = "Cada 6 Pulsos";

// AudioAlertSettingsView
"audio_prompts" = "Avisos de Voz";
"enable_audio_prompts" = "Activar Avisos de Voz";
"audio_prompts_footer" = "Recibe avisos de voz durante tus entrenamientos";
"distance_alerts" = "Alertas de Distancia";
"distance_alerts_footer" = "Anuncia cada hito de distancia";
"time_alerts" = "Alertas de Tiempo";
"time_alerts_footer" = "Anuncia cada intervalo de tiempo";
"calorie_alerts" = "Alertas de Calorías";
"calorie_alerts_footer" = "Anuncia los hitos de calorías quemadas";
"pace_alerts" = "Alertas de Ritmo";
"pace_alerts_footer" = "Anuncia tu ritmo actual a intervalos";
"custom" = "Personalizado";
"value" = "Valor";
"min" = "min";
"cal" = "cal";
"enabled" = "Activado";
"disabled" = "Desactivado";
"hour" = "hora";
"h" = "h";
"m" = "m";
"distance_short" = "Distancia";
"time_short" = "Tiempo";
"calorie_short" = "Calorías";
"pace_short" = "Ritmo";
"no_alerts_configured" = "Sin configurar";
"all_alerts" = "Todas activadas";
"enter_custom_value" = "Ingresa valor personalizado";
"integers_only" = "Solo números enteros";
"invalid_input" = "Entrada Inválida";
"please_enter_positive_number" = "Por favor ingresa un número positivo";
"please_enter_whole_number" = "Por favor ingresa un número entero (sin decimales)";
"please_enter_value_between" = "Por favor ingresa un valor entre %@ y %@";
"invalid" = "Inválido";

"per_day" = "por día";
"per_month" = "por mes";

// MARK: - Welcome View
"welcome.title" = "Bienvenido a RunApp";
"welcome.subtitle" = "¡Equipo listo, cada paso cuenta!";
"welcome.button.start" = "Comenzar Entrenamiento";

// MARK: - Basic Info View
"basicInfo.header.title" = "Personaliza tu experiencia";
"basicInfo.header.subtitle" = "para cálculo preciso de calorías";

// MARK: - Gender Selection
"basicInfo.gender.title" = "¿Cuál es tu género?";
"basicInfo.gender.male" = "Masculino";
"basicInfo.gender.female" = "Femenino";
"basicInfo.gender.preferNotToSay" = "Prefiero no decirlo";
"basicInfo.button.skip" = "Omitir";

// MARK: - Weight Input
"basicInfo.weight.title" = "¿Cuál es tu peso?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lbs";

// MARK: - Height Input
"basicInfo.height.title" = "¿Cuál es tu altura?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "FT";
"basicInfo.height.placeholder.in" = "IN";

"basicInfo.button.continue" = "Continuar";

// MARK: - All Set View
"allSet.title" = "¡Todo Listo!";
"allSet.subtitle.first" = "Es hora de sudar";
"allSet.subtitle.second" = "¡Sin excusas, solo acción!";
"allSet.button.go" = "¡Acelera!";

// MARK: - ContentView Alerts
"alert.location.title" = "Autorización de Ubicación Requerida";
"alert.location.message" = "Activa 'Permitir Siempre' la ubicación para rastrear perfectamente tus rutas y mantener tus datos de entrenamiento precisos. Por favor actualiza tu configuración.";
"alert.location.settings" = "Ajustes";
"alert.location.later" = "Más Tarde";

"alert.complete.title" = "¿Completar Entrenamiento?";
"alert.complete.message" = "Esto terminará tu entrenamiento actual.\nNo podrás reanudarlo después de completarlo.";
"alert.complete.confirm" = "Completar";
"alert.complete.cancel" = "Cancelar";

"alert.shortWorkout.title" = "Confirmar Entrenamiento Corto";
"alert.shortWorkout.message" = "Tu entrenamiento dura menos de 30 segundos. ¿Estás seguro de que quieres guardarlo?";
"alert.shortWorkout.save" = "Guardar";
"alert.shortWorkout.discard" = "Descartar";

// MARK: - Map Markers
"map.marker.start" = "Línea de Salida";
"map.marker.end" = "Línea de Meta";
"map.location.unknown" = "Ubicación Desconocida";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "Metrónomo Activado";
"metronome.status.off" = "Metrónomo Desactivado";

"activity.summary.title" = "Resumen de Entrenamiento";

// GPS Status
"gps.searching" = "Buscando señal GPS...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "¡Has desbloqueado entrenamiento ilimitado, suda y quema tu pasión!";
"settings.subscription.status.exceeded" = "¡Prueba gratuita terminada! ¡Suscríbete ahora para seguir quemando tu pasión deportiva!";
"settings.subscription.status.oneRemaining" = "Te queda 1 entrenamiento gratuito — ¡suscríbete ahora para mantener el ritmo!";
"settings.subscription.status.remaining" = "Te quedan %d entrenamientos gratuitos — ¡suscríbete ahora para seguir quemando calorías!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Suscripción";
"settings.subscription.unlimitedAccess" = "Entrenamiento Ilimitado";
"settings.subscription.checkingStatus" = "Verificando...";
"settings.subscription.alert.alreadySubscribed.title" = "Ya Suscrito";
"settings.subscription.alert.alreadySubscribed.message" = "Ya tienes acceso ilimitado al entrenamiento.";
"settings.subscription.alert.checkFailed.format" = "Error al verificar el estado de suscripción: %@";

// MARK: - Custom Value Interface
"custom_interval" = "Intervalo Personalizado";
"interval_value" = "Valor del Intervalo";
"enter_value_placeholder" = "Ingresa valor";
"whole_numbers_only" = "Solo números enteros";
"valid_range" = "Rango válido: %@ - %@";
"preset_options" = "Opciones rápidas:";
"custom_value" = "Valor Personalizado";

// MARK: - Audio Volume Settings
"audio_volume" = "Volumen de Audio";
"audio_volume_footer" = "Ajustar el volumen del metrónomo";
"volume" = "Volumen";
"voice_volume" = "Volumen de Voz";
"audio_settings" = "Ajustes de Audio";
"audio_settings_footer" = "Ajustar el volumen y velocidad de los anuncios de voz";
"settings.subscription.unlimitedAccess" = "Acceso Ilimitado";
"settings.subscription.checkingStatus" = "Comprobando...";
"settings.subscription.alert.alreadySubscribed.title" = "Ya estás suscrito";
"settings.subscription.alert.alreadySubscribed.message" = "Ya tienes acceso ilimitado.";
"settings.subscription.alert.checkFailed.format" = "Error al comprobar la suscripción: %@";

// AudioAlertSettingsView
"audio_prompts" = "Avisos de Audio";
"enable_audio_prompts" = "Activar Avisos de Audio";
"audio_prompts_footer" = "Recibir anuncios de voz durante tus carreras";
"distance_alerts" = "Alertas de Distancia";
"distance_alerts_footer" = "Anunciar cada hito de distancia";
"time_alerts" = "Alertas de Tiempo";
"time_alerts_footer" = "Anunciar cada intervalo de tiempo";
"calorie_alerts" = "Alertas de Calorías";
"calorie_alerts_footer" = "Anunciar hitos de calorías";
"pace_alerts" = "Alertas de Ritmo";
"pace_alerts_footer" = "Anunciar ritmo actual en intervalos";
"custom" = "Personalizado";
"value" = "Valor";
"min" = "min";
"cal" = "cal";
"enabled" = "Activado";
"disabled" = "Desactivado";
"hour" = "hora";
"h" = "h";
"m" = "m";
"distance_short" = "Distancia";
"time_short" = "Tiempo";
"calorie_short" = "Calorías";
"pace_short" = "Ritmo";
"no_alerts_configured" = "Ninguno configurado";
"all_alerts" = "Todos activados";
"enter_custom_value" = "Ingresar valor personalizado";
"integers_only" = "Solo números enteros";
"invalid_input" = "Entrada Inválida";
"please_enter_positive_number" = "Por favor ingresa un número positivo";
"please_enter_whole_number" = "Por favor ingresa un número entero (sin decimales)";
"please_enter_value_between" = "Por favor ingresa un valor entre %@ y %@";
"invalid" = "Inválido";

"per_day" = "por día";

// MARK: - Custom Value Interface
"custom_interval" = "Intervalo Personalizado";
"interval_value" = "Valor del Intervalo";
"enter_value_placeholder" = "Ingresar valor";
"whole_numbers_only" = "Solo números enteros";
"valid_range" = "Rango válido: %@ - %@";
"preset_options" = "Opciones rápidas:";
"custom_value" = "Valor Personalizado";

// MARK: - Audio Volume Settings
"audio_volume" = "Volumen de Audio";
"audio_volume_footer" = "Ajustar el volumen del metrónomo";
"volume" = "Volumen";
"voice_volume" = "Volumen de Voz";
"audio_settings" = "Configuración de Audio";
"audio_settings_footer" = "Ajustar el volumen de los anuncios de voz y probar la salida de audio";
"test_voice" = "Probar Voz";

// MARK: - Smart Voice Messages
"smart_voice" = "Voz Inteligente";
"smart_voice_footer" = "Activa avisos de voz naturales y motivadores en lugar de anuncios estándar";

// Test messages
"audio.test.standard" = "Los avisos de audio funcionan correctamente";
"audio.test.smart" = "¡Excelente! Tu audio suena perfecto, ¡listo para motivarte!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "¡Muy bien! Has completado %@ %@";
"audio.distance.achievement.smart" = "¡Increíble! ¡Has alcanzado la marca de %@ %@! ¡Eres imparable!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "Llevas %@ en movimiento. ¡Sigue con ese ritmo fantástico!";

// Calorie messages
"audio.calories.standard" = "%@ calorías";
"audio.calories.smart" = "¡Has quemado %@ calorías! ¡Tu esfuerzo está dando frutos!";
"audio.calories.achievement.smart" = "¡Espectacular! ¡%@ calorías quemadas! ¡Eres una máquina quemagrasa!";

// Pace messages
"audio.pace.standard" = "Ritmo actual: %@";
"audio.pace.smart" = "Mantienes un ritmo de %@. ¡Te ves muy fuerte!";

// Unit strings for audio
"audio.unit.km.singular" = "kilómetro";
"audio.unit.km.plural" = "kilómetros";
"audio.unit.mile.singular" = "milla";
"audio.unit.mile.plural" = "millas";

// Time formatting for audio
"audio.time.minute.singular" = "1 minuto";
"audio.time.minutes" = "%d minutos";
"audio.time.hour.singular" = "1 hora";
"audio.time.hours" = "%d horas";
"audio.time.hours.minutes" = "%d horas y %d minutos";

// Pace formatting for audio
"audio.pace.per.km" = "por kilómetro";
"audio.pace.per.mile" = "por milla";
"audio.pace.seconds" = "%d segundos %@";
"audio.pace.minutes" = "%d minutos %@";
"audio.pace.minutes.seconds" = "%d minutos y %d segundos %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Estándar";
"voice.quality.enhanced" = "Mejorada";
"voice.quality.premium" = "Premium";
"voice.manager.test.text" = "Esta es una prueba de la voz seleccionada para tus alertas de carrera";
"voice.test.sample" = "Esta es una prueba de la voz seleccionada para tus alertas de carrera";
"voice.selection.title" = "Selección de Voz";
"voice.current" = "Voz Actual";
"voice.preview" = "Vista Previa";
"voice.none.available" = "No hay voces disponibles para este idioma";
"voice.loading" = "Cargando voces...";

// MARK: - Voice Selection UI
"voice.download.required" = "Descarga Requerida";
"voice.loading.voices" = "Cargando voces...";
"voice.empty.title" = "No Hay Voces Disponibles";
"voice.empty.message" = "No hay voces mejoradas o premium disponibles para este idioma. Descarga voces de alta calidad desde Ajustes para mejorar tu experiencia.";
"voice.refresh.voices" = "Actualizar Voces";
"voice.refresh.footer" = "Toca para actualizar la lista de voces disponibles";
"voice.current.language" = "Idioma Actual";
"voice.selection.description" = "Selecciona tu voz preferida para las alertas de audio.\nLas opciones mejoradas y premium ofrecen la máxima calidad.";
"voice.selection.workout.description" = "Elige tu voz para las indicaciones de audio durante el entrenamiento. Las voces premium ofrecen la experiencia más clara y motivadora durante las carreras.";
"voice.selection.simple.description" = "Elige tu voz preferida para las indicaciones de audio durante tus entrenamientos.";
"voice.preview.error.title" = "Error de Vista Previa";
"voice.preview.error.message" = "No se puede reproducir la vista previa de la voz '%@'. Inténtalo de nuevo.";
"voice.add.new" = "Agregar Nueva Voz";
"voice.manage.description" = "Ve a Ajustes > Accesibilidad > Contenido Hablado > Voces para descargar y gestionar voces";
"voice.system.default" = "Predeterminado del Sistema";

// MARK: - Premium Voice Benefits
"voice.premium.benefits.title" = "Voces Premium para la Mejor Experiencia de Entrenamiento";
"voice.premium.benefits.subtitle" = "Obtén indicaciones de audio cristalinas que atraviesan el ruido ambiental y te motivan durante entrenamientos intensos";
"voice.premium.benefits.recommendation" = "Recomendamos agregar nuevas voces premium para la mejor experiencia";
"voice.premium.benefit.clarity" = "Claridad de audio superior para correr al aire libre";
"voice.premium.benefit.noise" = "Mejor rendimiento en entornos ruidosos";
"voice.premium.benefit.motivation" = "Tono de voz más natural y motivador";
"voice.premium.recommended" = "RECOMENDADO";
"voice.premium.footer" = "Las voces premium proporcionan el audio más claro durante los entrenamientos con mejor resistencia al ruido y patrones de habla naturales.";
"voice.enhanced.footer" = "Las voces mejoradas ofrecen mejor calidad que las voces estándar del sistema con mayor claridad.";

// MARK: - Voice Quality Descriptions
"voice.quality.standard.description" = "Voz de calidad estándar";
"voice.quality.enhanced.description" = "Claridad mejorada para mejor audio";
"voice.quality.premium.description" = "Optimizada para motivación en entrenamientos";

// MARK: - Voice Instructions
"voice.instructions.title" = "Descargar Nuevas Voces";
"voice.instructions.message" = "Sigue estos pasos para descargar voces de alta calidad:\n\n1. Toca 'Abrir Ajustes' abajo\n2. Navega a: Accesibilidad\n3. Selecciona: Contenido Hablado\n4. Elige: Voces\n5. Descarga tus voces preferidas\n6. Regresa a RunApp y actualiza";
"voice.instructions.open.settings" = "Abrir Ajustes";
"voice.instructions.steps.title" = "Cómo agregar voces:";
"voice.instructions.steps.detail" = "Ajustes → Accesibilidad → Contenido Hablado → Voces";
"voice.instructions.footer" = "Toca el botón de arriba para ver instrucciones paso a paso para descargar nuevas voces";
"voice.add.new.subtitle" = "Obtener instrucciones paso a paso";
"voice.management.footer" = "Descarga nuevas voces desde Ajustes o actualiza para ver las voces recién instaladas";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Selección de Voz";
"voice.selection.footer" = "Elige tu voz preferida para los anuncios de audio";

// Common buttons
"common.cancel" = "Cancelar";
"common.close" = "Cerrar";
"common.done" = "Listo";
"common.ok" = "OK";

// MARK: - Voice Management
"voice.delete.success" = "Voz eliminada con éxito";
"voice.delete.failed" = "Error al eliminar la voz";
"voice.delete.confirm.title" = "¿Eliminar voz?";
"voice.delete.confirm.message" = "Esto eliminará '%@' de tu dispositivo. Puedes volver a descargarla desde configuración más tarde.";
"voice.delete.confirm.delete" = "Eliminar";
"voice.delete.unavailable" = "Esta voz no se puede eliminar porque es la voz predeterminada del sistema.";
"voice.manage.voices" = "Gestionar voces";
"voice.download.status" = "Descargada";
"voice.builtin.status" = "Integrada";

// MARK: - Smart Voice Prompt Variations (Spanish)
// Distance Smart Variations - Español motivacional
"audio.distance.smart.1" = "¡Increíble! ¡Ya has corrido %@ %@! ¡Eres imparable!";
"audio.distance.smart.2" = "¡Órale! ¡%@ %@ conquistados! ¡Sigue así, campeón!";
"audio.distance.smart.3" = "¡Brutal! ¡%@ %@ en el bolsillo! ¡Qué máquina!";
"audio.distance.smart.4" = "¡Espectacular! ¡%@ %@ completados! ¡Dale que puedes!";
"audio.distance.smart.5" = "¡Fenomenal! ¡%@ %@ logrados! ¡Eres un crack!";
"audio.distance.smart.6" = "¡Bestial! ¡%@ %@ dominados! ¡No pares!";
"audio.distance.smart.7" = "¡Genial! ¡%@ %@ superados! ¡Qué poderío!";
"audio.distance.smart.8" = "¡Fantástico! ¡%@ %@ desbloqueados! ¡Eres fuego!";
"audio.distance.smart.9" = "¡Magnífico! ¡%@ %@ arrasados! ¡Pura potencia!";
"audio.distance.smart.10" = "¡Leyenda! ¡%@ %@ demolidos! ¡Eres el rey!";

// Time Smart Variations - Español motivacional
"audio.time.smart.1" = "¡Ya llevas %@ corriendo! ¡Qué resistencia!";
"audio.time.smart.2" = "¡Órale! ¡%@ de puro poder! ¡Sigue machacando!";
"audio.time.smart.3" = "¡Brutal! ¡%@ sin parar! ¡Eres una bestia!";
"audio.time.smart.4" = "¡Increíble! ¡%@ de determinación! ¡Dale fuerte!";
"audio.time.smart.5" = "¡Fenomenal! ¡%@ concentrado! ¡No aflojez!";
"audio.time.smart.6" = "¡Bestial! ¡%@ de esfuerzo! ¡Qué guerrero!";
"audio.time.smart.7" = "¡Genial! ¡%@ imparable! ¡Sigue así!";
"audio.time.smart.8" = "¡Fantástico! ¡%@ luchando! ¡Eres fuego!";
"audio.time.smart.9" = "¡Magnífico! ¡%@ de pasión! ¡Eres el mejor!";
"audio.time.smart.10" = "¡Leyenda! ¡%@ demoliendo! ¡Pura gloria!";

// Calories Smart Variations - Español motivacional
"audio.calories.smart.1" = "¡Órale! ¡Ya quemaste %@ calorías! ¡Eres fuego!";
"audio.calories.smart.2" = "¡Brutal! ¡%@ calorías eliminadas! ¡Sigue quemando!";
"audio.calories.smart.3" = "¡Increíble! ¡%@ calorías dominadas! ¡Qué máquina!";
"audio.calories.smart.4" = "¡Fenomenal! ¡%@ calorías conquistadas! ¡Dale!";
"audio.calories.smart.5" = "¡Bestial! ¡%@ calorías demolidas! ¡No pares!";
"audio.calories.smart.6" = "¡Genial! ¡%@ calorías arrasadas! ¡Eres un crack!";
"audio.calories.smart.7" = "¡Fantástico! ¡%@ calorías destrozadas! ¡Sigue!";
"audio.calories.smart.8" = "¡Magnífico! ¡%@ calorías vaporizadas! ¡Imparable!";
"audio.calories.smart.9" = "¡Espectacular! ¡%@ calorías fulminadas! ¡Bestia!";
"audio.calories.smart.10" = "¡Leyenda! ¡%@ calorías aniquiladas! ¡Eres el rey!";

// Pace Smart Variations - Español motivacional
"audio.pace.smart.1" = "¡Perfecto! ¡Ritmo de %@! ¡Qué control!";
"audio.pace.smart.2" = "¡Órale! ¡%@ de velocidad! ¡Puro poder!";
"audio.pace.smart.3" = "¡Brutal! ¡%@ controlado! ¡Eres una máquina!";
"audio.pace.smart.4" = "¡Increíble! ¡%@ dominado! ¡Qué técnica!";
"audio.pace.smart.5" = "¡Fenomenal! ¡%@ perfecto! ¡Sigue así!";
"audio.pace.smart.6" = "¡Bestial! ¡%@ fluido! ¡Eres un crack!";
"audio.pace.smart.7" = "¡Genial! ¡%@ profesional! ¡Qué nivel!";
"audio.pace.smart.8" = "¡Fantástico! ¡%@ imparable! ¡Eres fuego!";
"audio.pace.smart.9" = "¡Magnífico! ¡%@ de ensueño! ¡Bestia!";
"audio.pace.smart.10" = "¡Leyenda! ¡%@ demoledor! ¡Eres el rey!";

// MARK: - Workout Settings
"workout.settings.title" = "Configuración de Entrenamiento";
"workout.settings.notice" = "Entrenamiento en Progreso";
"workout.settings.safe.only" = "Solo configuraciones seguras para el entrenamiento disponibles";

// MARK: - Speech Speed Settings
"speech_speed" = "Velocidad de Habla";
"speech_speed_footer" = "Controla qué tan rápido se pronuncian las indicaciones de voz. Las velocidades más lentas ayudan con la comprensión durante entrenamientos intensos o en ambientes ruidosos.";

// MARK: - Data Management
"data_management" = "Gestión de Datos";
"data_privacy" = "Privacidad de Datos";
"export_data" = "Exportar Datos";
"import_data" = "Importar Datos";
"delete_data" = "Eliminar Datos";

// Privacy Messages
"privacy_ownership" = "Mantienes la propiedad de tus datos";
"privacy_icloud_storage" = "Almacenados de forma segura en tu iCloud";
"privacy_no_access" = "Nosotros (como desarrolladores) no podemos acceder a tus datos personales";
"privacy_full_control" = "Mantienes el control total de tus datos";

// Export
"export_all_data" = "Exportar Todos los Datos";
"export_profile_only" = "Exportar Solo Perfil";
"export_activities_only" = "Exportar Solo Actividades";
"export_all_description" = "Perfil y actividades en 2 archivos";
"export_profile_description" = "Configuraciones personales y preferencias";
"export_activities_description" = "Todos los datos de entrenamiento y estadísticas";
"export_data_description" = "Exporta tus datos como archivos CSV para respaldo o transferencia";

// Import
"import_data_files" = "Importar Archivos de Datos";
"import_data_files_description" = "Selecciona archivos CSV exportados previamente";
"import_data_description" = "Restaurar desde archivos exportados previamente";

// Delete
"delete_all_data" = "Eliminar Todos los Datos";
"delete_all_data_description" = "Remover permanentemente todos los datos";
"delete_data_description" = "Esta acción no se puede deshacer";
"delete_confirmation" = "Confirmar Eliminación";
"delete_confirmation_message" = "Esto eliminará permanentemente todos tus datos de perfil y actividades. Esta acción no se puede deshacer.";
"delete_permanently" = "Eliminar Permanentemente";

// Status Messages
"importing" = "Importando...";
"deleting" = "Eliminando...";
"export_failed" = "Exportación fallida";
"import_failed" = "Importación fallida";
"delete_failed" = "Eliminación fallida";
"import_success" = "Importación Exitosa";
"import_success_message" = "Los datos han sido importados exitosamente";
"delete_success" = "Datos Eliminados";
"delete_success_message" = "Todos tus datos han sido eliminados permanentemente. Ahora regresarás a la pantalla principal.";
"import_error" = "Error de Importación";
"export_error" = "Error de Exportación";
"delete_error" = "Error de Eliminación";

"select_gender_title" = "Seleccionar Género";

// MARK: - Activity Stats View
"act_dist" = "Dist Entreno";
"act_time" = "Tiempo Entreno";
"act_cal" = "Cal Entreno";
"performance" = "Rendimiento";
"best_pace" = "Mejor Ritmo";
"avg_speed" = "Velocidad Prom";
"max_speed" = "Velocidad Máx";
"time_analysis" = "Análisis Tiempo";
"total_time" = "Tiempo Total";
"paused_time" = "Tiempo Pausa";
"active_calories" = "Activo";
"resting_calories" = "Reposo";
"rate" = "Tasa";
"kcal" = "kcal";
"cal_per_min" = "cal/min";
