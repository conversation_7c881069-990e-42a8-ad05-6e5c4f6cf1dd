// ActivityControls
"activities" = "التدريبات";
"metronome" = "المترونوم";

// StatsView
"distance" = "المسافة";
"active" = "نشط";
"time" = "الوقت";
"current" = "الحالي";
"pace" = "السرعة";
"km" = "كم";
"mi" = "ميل";

// ActivityView
"activities" = "التدريبات";
"no_activities" = "لا توجد تدريبات";
"complete_first_run" = "أكمل أول جري لتراه هنا";

// ActivityRowView
"distance" = "المسافة";
"duration" = "المدة";
"avg_pace" = "متوسط السرعة";
"calories" = "السعرات الحرارية";
"start" = "الانطلاق";
"end" = "خط النهاية";

// ActivityDetailView
"delete_activity" = "حذف التدريب";
"are_you_sure_deletion" = "هل أنت متأكد؟ لا يمكن التراجع عن الحذف.";

// CountdownView
"go" = "اندفع!";

// LanguageSettingView
"language" = "اللغة";
"done" = "تم";

// AuthView
"runapp" = "RunApp";
"email" = "البريد الإلكتروني";
"sign_in_with_email" = "تسجيل الدخول بالبريد الإلكتروني";
"check_inbox_login_link" = "تحقق من بريدك للحصول على رابط تسجيل الدخول.";

// AnalysisView
"improve_with_personalized_data" = "تحسين مع البيانات الشخصية";
"run" = "جري";
"walk" = "مشي سريع";
"hike" = "تسلق";
"bike" = "ركوب الدراجات";
"distance" = "المسافة";
"duration" = "المدة";
"calories" = "السعرات الحرارية";
"current_week" = "الأسبوع الحالي";
"current_month" = "الشهر الحالي";
"current_year" = "السنة الحالية";
"per_day" = "في اليوم";
"per_month" = "في الشهر";
"generate_test_data" = "إنشاء بيانات تجريبية";

// MetronomeSettingsView
"metronome_settings" = "المترونوم";
"tempo" = "الإيقاع";
"tempo_footer" = "ضبط الإيقاع بين 40-240 نبضة في الدقيقة";
"enable_metronome" = "تفعيل المترونوم";
"sound" = "الصوت";
"sound_type" = "نوع الصوت";
"vibration" = "الاهتزاز";
"vibration_strength" = "قوة الاهتزاز";
"alert_frequency" = "تكرار التنبيه";
"largo" = "بطيء جداً";
"adagio" = "بطيء";
"andante" = "معتدل";
"moderato" = "متوسط";
"allegro" = "سريع";
"presto" = "سريع جداً";
"prestissimo" = "أقصى سرعة";
"default_beat" = "النبضة الافتراضية";
"beat_2" = "النبضة 2";
"beat_3" = "النبضة 3";
"beat_1" = "النبضة 1";
"beat_4" = "النبضة 4";
"beat_5" = "النبضة 5";
"beat_6" = "النبضة 6";
"feedback" = "الصوت والاهتزاز";
"feedback_footer" = "اختر من أنواع الصوت المختلفة عند تفعيل الصوت";

// NameSettingView
"name" = "الاسم";
"enter_your_name" = "أدخل اسمك";
"cancel" = "إلغاء";
"save" = "حفظ";
"invalid_name" = "اسم غير صالح";
"please_enter_valid_name" = "الرجاء إدخال اسم صالح";

// WeightSettingView
"weight" = "الوزن";
"unit" = "الوحدة";
"calorie_calculations_required" = "مطلوب لحساب السعرات الحرارية بدقة";
"invalid_weight" = "وزن غير صالح";
"please_enter_valid_weight" = "الرجاء إدخال وزن صالح بين %d و %d %@";

// GenderSettingView
"gender" = "الجنس";
"gender_footer" = "اختياري - يستخدم لحساب السعرات الحرارية بدقة أكبر";

// AgeSettingView
"age" = "العمر";
"birth_date" = "تاريخ الميلاد";
"years_old" = "سنة";
"invalid_age" = "عمر غير صالح";
"age_requirement" = "يجب أن يكون عمرك 13 سنة على الأقل لاستخدام هذا التطبيق";

// HeightSettingView
"height" = "الطول";
"height_cm" = "الطول (سم)";
"feet" = "قدم";
"inches" = "بوصة";
"bmi_calculations" = "اختياري - يستخدم لحساب مؤشر كتلة الجسم";
"invalid_height" = "طول غير صالح";
"please_enter_valid_height" = "الرجاء إدخال طول صالح.";

// SettingsView
"settings" = "الإعدادات";
"profile" = "الملف الشخصي";
"not_set" = "غير محدد";
"age" = "العمر";
"years" = "سنوات";
"preferences" = "التفضيلات";
"metronome" = "المترونوم";
"bpm" = "نبضة/دقيقة";
"units" = "الوحدات";
"metric" = "متري";
"imperial" = "إمبراطوري";
"theme" = "المظهر";
"language" = "اللغة";
"data" = "البيانات";
"all_activities" = "جميع التدريبات";
"export_activities" = "تصدير التدريبات";
"exporting" = "جاري التصدير...";
"error" = "خطأ";
"ok" = "موافق";
"unknown_error" = "حدث خطأ غير معروف";
"system_default" = "افتراضي النظام";
"light" = "فاتح";
"dark" = "داكن";
"male" = "ذكر";
"female" = "أنثى";
"other" = "آخر";
"prefer_not_to_say" = "أفضل عدم القول";
"audio" = "الصوت";

// UnitsSettingView
"unit_system" = "نظام الوحدات";

// SportTypeSelector
"run" = "جري";
"walk" = "مشي سريع";
"hike" = "تسلق";
"bike" = "ركوب الدراجات";

// ProfileHeaderView
"your_name" = "اسمك";
"email_example" = "<EMAIL>";
"change" = "تغيير";
"delete_photo" = "حذف الصورة؟";
"delete" = "حذف";

// ActivitySummaryComponents
"total" = "المجموع";
"avg" = "المتوسط";
"per_day" = "في اليوم";
"per_month" = "في الشهر";

// PeriodIndicatorView
"current_week" = "الأسبوع الحالي";
"week_ago_format" = "قبل %d أسبوع (%d)";
"weeks_ago_format" = "قبل %d أسابيع (%d)";
"current_month" = "الشهر الحالي";
"month_ago_format" = "قبل %d شهر (%d)";
"months_ago_format" = "قبل %d أشهر (%d)";
"current_year" = "السنة الحالية";
"year_ago_format" = "قبل %d سنة (%d)";
"years_ago_format" = "قبل %d سنوات (%d)";

// TimePeriodPicker
"time_period" = "الفترة الزمنية";
"seven_days" = "أسبوع";
"one_month" = "شهر";
"one_year" = "سنة";
"all_time" = "كل الوقت";

// For timeOfDay values
"morning" = "تدريب صباحي";
"afternoon" = "تدريب ظهيرة";
"evening" = "جري مسائي";
"night" = "تدريب ليلي";

"every_beat" = "كل نبضة";
"every_other_beat" = "كل نبضة ثانية";
"every_4th_beat" = "كل نبضة رابعة";
"every_6th_beat" = "كل نبضة سادسة";

// AudioAlertSettingsView
"audio_prompts" = "التنبيهات الصوتية";
"enable_audio_prompts" = "تفعيل التنبيهات الصوتية";
"audio_prompts_footer" = "استقبل الإعلانات الصوتية أثناء الجري";
"distance_alerts" = "تنبيهات المسافة";
"distance_alerts_footer" = "إعلان كل معلم مسافة";
"time_alerts" = "تنبيهات الوقت";
"time_alerts_footer" = "إعلان كل فترة زمنية";
"calorie_alerts" = "تنبيهات السعرات الحرارية";
"calorie_alerts_footer" = "إعلان معالم السعرات الحرارية";
"pace_alerts" = "تنبيهات السرعة";
"pace_alerts_footer" = "إعلان السرعة الحالية على فترات";
"custom" = "مخصص";
"value" = "القيمة";
"min" = "دقيقة";
"cal" = "سعرة";
"enabled" = "مفعل";
"disabled" = "معطل";
"hour" = "ساعة";
"h" = "س";
"m" = "د";
"distance_short" = "المسافة";
"time_short" = "الوقت";
"calorie_short" = "السعرات";
"pace_short" = "السرعة";
"no_alerts_configured" = "لم يتم التعيين";
"all_alerts" = "الكل مفعل";
"enter_custom_value" = "أدخل قيمة مخصصة";
"integers_only" = "أرقام صحيحة فقط";
"invalid_input" = "إدخال غير صالح";
"please_enter_positive_number" = "الرجاء إدخال رقم موجب";
"please_enter_whole_number" = "الرجاء إدخال رقم صحيح (بدون كسور عشرية)";
"please_enter_value_between" = "الرجاء إدخال قيمة بين %@ و %@";
"invalid" = "غير صالح";

"per_day" = "في اليوم";
"per_month" = "في الشهر";

// MARK: - Welcome View
"welcome.title" = "مرحباً بك في RunApp";
"welcome.subtitle" = "استعد، كل خطوة مهمة!";
"welcome.button.start" = "ابدأ";

// MARK: - Basic Info View
"basicInfo.header.title" = "خصص تجربتك";
"basicInfo.header.subtitle" = "لحساب السعرات الحرارية";

// MARK: - Gender Selection
"basicInfo.gender.title" = "ما هو جنسك؟";
"basicInfo.gender.male" = "ذكر";
"basicInfo.gender.female" = "أنثى";
"basicInfo.gender.preferNotToSay" = "أفضل عدم القول";
"basicInfo.button.skip" = "تخطي";

// MARK: - Weight Input
"basicInfo.weight.title" = "ما هو وزنك؟";
"basicInfo.weight.unit.kg" = "كجم";
"basicInfo.weight.unit.lbs" = "رطل";

// MARK: - Height Input
"basicInfo.height.title" = "ما هو طولك؟";
"basicInfo.height.unit.cm" = "سم";
"basicInfo.height.unit.ftIn" = "قدم";
"basicInfo.height.placeholder.cm" = "سم";
"basicInfo.height.placeholder.ft" = "قدم";
"basicInfo.height.placeholder.in" = "بوصة";

"basicInfo.button.continue" = "متابعة";

// MARK: - All Set View
"allSet.title" = "كل شيء جاهز!";
"allSet.subtitle.first" = "حان وقت الانطلاق";
"allSet.subtitle.second" = "لا أعذار، فقط أفعال.";
"allSet.button.go" = "انطلق!";

// MARK: - ContentView Alerts
"alert.location.title" = "مطلوب إذن الموقع";
"alert.location.message" = "قم بتفعيل 'السماح دائماً' للموقع لتتبع المسارات بدقة والحفاظ على تقدمك. يرجى تحديث إعداداتك.";
"alert.location.settings" = "الإعدادات";
"alert.location.later" = "لاحقاً";

"alert.complete.title" = "إنهاء التدريب؟";
"alert.complete.message" = "سينهي هذا تدريبك الحالي.\nلا يمكنك استئنافه بعد الإنهاء.";
"alert.complete.confirm" = "إنهاء";
"alert.complete.cancel" = "إلغاء";

"alert.shortWorkout.title" = "تأكيد التمرين القصير";
"alert.shortWorkout.message" = "تمرينك أقل من 30 ثانية. هل أنت متأكد من حفظه؟";
"alert.shortWorkout.save" = "حفظ";
"alert.shortWorkout.discard" = "تجاهل";

// MARK: - Map Markers
"map.marker.start" = "البداية";
"map.marker.end" = "النهاية";
"map.location.unknown" = "موقع غير معروف";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "المترونوم يعمل";
"metronome.status.off" = "المترونوم متوقف";

"activity.summary.title" = "ملخص التدريب";

// GPS Status
"gps.searching" = "جارٍ البحث عن GPS...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "لديك وصول غير محدود – انطلق بلا حدود!";
"settings.subscription.status.exceeded" = "لقد استنفدت الحد الأقصى للتجربة المجانية. اشترك الآن للاستمرار بلا قيود!";
"settings.subscription.status.oneRemaining" = "تبقّى لك تدريب مجاني واحد — اشترك الآن لتُواصل التقدّم!";
"settings.subscription.status.remaining" = "تبقّى لك %d تدريبات مجانية — اشترك فوراً لتبقى في السباق!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "الاشتراك";
"settings.subscription.unlimitedAccess" = "وصول غير محدود";
"settings.subscription.checkingStatus" = "جارٍ التحقق...";
"settings.subscription.alert.alreadySubscribed.title" = "مشترك بالفعل";
"settings.subscription.alert.alreadySubscribed.message" = "أنت تملك وصولاً غير محدود بالفعل.";
"settings.subscription.alert.checkFailed.format" = "فشل في التحقق من حالة الاشتراك: %@";

// MARK: - Custom Value Interface
"custom_interval" = "فترة مخصصة";
"interval_value" = "قيمة الفترة";
"enter_value_placeholder" = "أدخل القيمة";
"whole_numbers_only" = "أرقام صحيحة فقط";
"valid_range" = "النطاق الصالح: %@ - %@";
"preset_options" = "خيارات سريعة:";
"custom_value" = "قيمة مخصصة";

// MARK: - Audio Volume Settings
"audio_volume" = "مستوى الصوت";
"audio_volume_footer" = "اضبط مستوى صوت المترونوم";
"volume" = "الصوت";
"voice_volume" = "مستوى الصوت";
"audio_settings" = "إعدادات الصوت";
"audio_settings_footer" = "اضبط مستوى صوت وسرعة الإعلانات الصوتية";
"test_voice" = "اختبار الصوت";

// MARK: - Smart Voice Messages
"smart_voice" = "الصوت الذكي";
"smart_voice_footer" = "تفعيل الإعلانات الصوتية الطبيعية والمحفزة بدلاً من الإعلانات العادية";

// Test messages
"audio.test.standard" = "الإعلانات الصوتية تعمل بشكل صحيح";
"audio.test.smart" = "ممتاز! صوتك رائع، جاهز لتحفيزك!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "أحسنت! لقد أنجزت %@ %@";
"audio.distance.achievement.smart" = "رائع! لقد وصلت إلى علامة %@ %@! أنت لا تُقهر!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "أنت في الحركة منذ %@. حافظ على هذا الإيقاع الرائع!";

// Calorie messages
"audio.calories.standard" = "%@ سعرة حرارية";
"audio.calories.smart" = "لقد أحرقت %@ سعرة حرارية! جهودك تؤتي ثمارها!";
"audio.calories.achievement.smart" = "مذهل! %@ سعرة حرارية محروقة! أنت آلة حرق السعرات الحرارية!";

// Pace messages
"audio.pace.standard" = "السرعة الحالية: %@";
"audio.pace.smart" = "أنت تحافظ على سرعة %@. تبدو قوياً جداً!";

// Unit strings for audio
"audio.unit.km.singular" = "كيلومتر";
"audio.unit.km.plural" = "كيلومتر";
"audio.unit.mile.singular" = "ميل";
"audio.unit.mile.plural" = "ميل";

// Time formatting for audio
"audio.time.minute.singular" = "دقيقة واحدة";
"audio.time.minutes" = "%d دقائق";
"audio.time.hour.singular" = "ساعة واحدة";
"audio.time.hours" = "%d ساعات";
"audio.time.hours.minutes" = "%d ساعات و %d دقائق";

// Pace formatting for audio
"audio.pace.per.km" = "لكل كيلومتر";
"audio.pace.per.mile" = "لكل ميل";
"audio.pace.seconds" = "%d ثانية %@";
"audio.pace.minutes" = "%d دقائق %@";
"audio.pace.minutes.seconds" = "%d دقائق و %d ثانية %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "عادي";
"voice.quality.enhanced" = "محسن";
"voice.quality.premium" = "مميز";
"voice.manager.test.text" = "هذا اختبار للصوت المختار لتنبيهات الجري الخاصة بك";
"voice.test.sample" = "هذا اختبار للصوت المختار لتنبيهات الجري الخاصة بك";
"voice.selection.title" = "اختيار الصوت";
"voice.current" = "الصوت الحالي";
"voice.preview" = "معاينة";
"voice.none.available" = "لا توجد أصوات متاحة لهذه اللغة";
"voice.loading" = "جاري تحميل الأصوات...";

// MARK: - Voice Selection UI
"voice.download.required" = "التحميل مطلوب";
"voice.loading.voices" = "جاري تحميل الأصوات...";
"voice.empty.title" = "لا توجد أصوات متاحة";
"voice.empty.message" = "لا توجد أصوات محسنة أو مميزة متاحة لهذه اللغة. قم بتحميل أصوات عالية الجودة من الإعدادات لتحسين تجربتك.";
"voice.refresh.voices" = "تحديث الأصوات";
"voice.refresh.footer" = "اضغط لتحديث قائمة الأصوات المتاحة";
"voice.current.language" = "اللغة الحالية";
"voice.selection.description" = "اختر صوتك المفضل للتنبيهات الصوتية.\nالخيارات المحسنة والمميزة توفر أعلى جودة.";
"voice.preview.error.title" = "فشل في المعاينة";
"voice.preview.error.message" = "تعذر معاينة الصوت '%@'. يرجى المحاولة مرة أخرى.";
"voice.add.new" = "إضافة صوت جديد";
"voice.manage.description" = "انتقل إلى الإعدادات > إمكانية الوصول > المحتوى المنطوق > الأصوات لتحميل وإدارة الأصوات";
"voice.system.default" = "افتراضي النظام";

// MARK: - Voice Instructions
"voice.instructions.title" = "تحميل أصوات جديدة";
"voice.instructions.message" = "اتبع هذه الخطوات لتحميل أصوات عالية الجودة:\n\n1. اضغط على 'فتح الإعدادات' أدناه\n2. انتقل إلى: إمكانية الوصول\n3. اختر: المحتوى المنطوق\n4. اختر: الأصوات\n5. حمل أصواتك المفضلة\n6. ارجع إلى RunApp وحدث";
"voice.instructions.open.settings" = "فتح الإعدادات";
"voice.instructions.steps.title" = "كيفية إضافة الأصوات:";
"voice.instructions.steps.detail" = "الإعدادات ← إمكانية الوصول ← المحتوى المنطوق ← الأصوات";
"voice.instructions.footer" = "اضغط على الزر أعلاه لرؤية التعليمات خطوة بخطوة لتحميل أصوات جديدة";
"voice.add.new.subtitle" = "احصل على التعليمات خطوة بخطوة";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "اختيار الصوت";
"voice.selection.footer" = "اختر صوتك المفضل للإعلانات الصوتية";

// Common buttons
"common.cancel" = "إلغاء";
"common.close" = "إغلاق";
"common.done" = "تم";
"common.ok" = "موافق";

// MARK: - Voice Management
"voice.delete.success" = "تم حذف الصوت بنجاح";
"voice.delete.failed" = "فشل في حذف الصوت";
"voice.delete.confirm.title" = "حذف الصوت؟";
"voice.delete.confirm.message" = "سيؤدي هذا إلى إزالة '%@' من جهازك. يمكنك إعادة تحميله لاحقاً من الإعدادات.";
"voice.delete.confirm.delete" = "حذف";
"voice.delete.unavailable" = "لا يمكن حذف هذا الصوت لأنه صوت افتراضي للنظام.";
"voice.manage.voices" = "إدارة الأصوات";
"voice.download.status" = "تم التحميل";
"voice.builtin.status" = "مدمج";

// MARK: - Smart Voice Prompt Variations (Arabic)
// Distance Smart Variations - العربية تحفيزية
"audio.distance.smart.1" = "رائع! قطعت %@ %@! أنت بطل!";
"audio.distance.smart.2" = "هايل! %@ %@ تم إنجازها! يلّا كمّل!";
"audio.distance.smart.3" = "جامد! %@ %@ في الجيب! أنت ماكينة!";
"audio.distance.smart.4" = "خرافي! %@ %@ مكتملة! هيّا بنا!";
"audio.distance.smart.5" = "فوق الوصف! %@ %@ محققة! أنت وحش!";
"audio.distance.smart.6" = "دمّرت! %@ %@ مسيطر عليها! ما توقف!";
"audio.distance.smart.7" = "عبقري! %@ %@ متجاوزة! أي قوة!";
"audio.distance.smart.8" = "خطير! %@ %@ مفتوحة! أنت نار!";
"audio.distance.smart.9" = "أسطوري! %@ %@ مسحوقة! قوة خالصة!";
"audio.distance.smart.10" = "ملك! %@ %@ مدمّرة! أنت الأول!";

// Time Smart Variations - العربية تحفيزية
"audio.time.smart.1" = "ها هي %@ جري! أي مقاومة!";
"audio.time.smart.2" = "يا سلام! %@ طاقة خالصة! كمّل كده!";
"audio.time.smart.3" = "جامد! %@ بلا توقف! أنت ماكينة!";
"audio.time.smart.4" = "مذهل! %@ إصرار! روح بكل قوة!";
"audio.time.smart.5" = "رهيب! %@ تركيز! يلّا بنا!";
"audio.time.smart.6" = "دمّرت! %@ جهد! أي محارب!";
"audio.time.smart.7" = "عبقري! %@ لا يُقاوم! كمّل كده!";
"audio.time.smart.8" = "خطير! %@ كفاح! أنت نار!";
"audio.time.smart.9" = "أسطوري! %@ شغف! أنت الأفضل!";
"audio.time.smart.10" = "ملك! %@ تدمير! مجد خالص!";

// Calories Smart Variations - العربية تحفيزية
"audio.calories.smart.1" = "يا سلام! حرقت %@ سعرة! أنت نار!";
"audio.calories.smart.2" = "جامد! %@ سعرة مقضية! كمّل الحرق!";
"audio.calories.smart.3" = "مذهل! %@ سعرة مسيطر عليها! أي ماكينة!";
"audio.calories.smart.4" = "رهيب! %@ سعرة محققة! يلّا!";
"audio.calories.smart.5" = "دمّرت! %@ سعرة مهدومة! ما توقف!";
"audio.calories.smart.6" = "عبقري! %@ سعرة مسحوقة! أنت وحش!";
"audio.calories.smart.7" = "خطير! %@ سعرة مدمّرة! كمّل!";
"audio.calories.smart.8" = "أسطوري! %@ سعرة متبخرة! لا يُقاوم!";
"audio.calories.smart.9" = "خرافي! %@ سعرة مفنية! وضع الوحش!";
"audio.calories.smart.10" = "ملك! %@ سعرة مطحونة! أنت الأول!";

// Pace Smart Variations - العربية تحفيزية
"audio.pace.smart.1" = "مثالي! إيقاع %@! أي تحكم!";
"audio.pace.smart.2" = "يا سلام! %@ سرعة! قوة خالصة!";
"audio.pace.smart.3" = "جامد! %@ متحكم فيه! أنت ماكينة!";
"audio.pace.smart.4" = "مذهل! %@ مسيطر عليه! أي تقنية!";
"audio.pace.smart.5" = "رهيب! %@ مثالي! كمّل كده!";
"audio.pace.smart.6" = "دمّرت! %@ انسيابي! أنت وحش!";
"audio.pace.smart.7" = "عبقري! %@ احترافي! أي مستوى!";
"audio.pace.smart.8" = "خطير! %@ لا يُقاوم! أنت نار!";
"audio.pace.smart.9" = "أسطوري! %@ الأحلام! وضع الوحش!";
"audio.pace.smart.10" = "ملك! %@ مدمّر! أنت الأول!";

// MARK: - Workout Settings
"workout.settings.title" = "إعدادات التمرين";
"workout.settings.notice" = "التمرين قيد التقدم";
"workout.settings.safe.only" = "الإعدادات الآمنة للتمرين فقط متاحة";

// MARK: - Voice Premium Features
"voice.selection.simple.description" = "اختر صوتك المفضل للتنبيهات الصوتية أثناء الجري.";
"voice.premium.benefits.recommendation" = "نوصي بإضافة الأصوات المميزة للحصول على أفضل تجربة";
"voice.premium.benefit.clarity" = "وضوح صوتي فائق للتمارين الخارجية";
"voice.premium.benefit.noise" = "أداء أفضل في البيئات الصاخبة";
"voice.premium.benefit.motivation" = "نبرة صوت أكثر طبيعية ومحفزة";
"voice.management.footer" = "قم بتحميل أصوات جديدة من إعدادات iOS وحدث لرؤية الأصوات المثبتة حديثاً";
"voice.selection.workout.description" = "اختر صوتك للتنبيهات الصوتية أثناء التمرين. الأصوات المميزة توفر أوضح وأكثر التجارب تحفيزاً أثناء الجري.";

// MARK: - Speech Speed Settings
"speech_speed" = "سرعة الكلام";
"speech_speed_footer" = "تحكم في سرعة نطق التنبيهات الصوتية. السرعات الأبطأ تساعد على الفهم أثناء التمارين المكثفة أو في البيئات الصاخبة.";

// MARK: - Data Management
"data_management" = "إدارة البيانات";
"data_privacy" = "خصوصية البيانات";
"export_data" = "تصدير البيانات";
"import_data" = "استيراد البيانات";
"delete_data" = "حذف البيانات";

// Privacy Messages
"privacy_ownership" = "تحتفظ بملكية بياناتك";
"privacy_icloud_storage" = "مخزنة بأمان في iCloud الخاص بك";
"privacy_no_access" = "لا يمكننا (كمطورين) الوصول إلى بياناتك الشخصية";
"privacy_full_control" = "تحتفظ بالتحكم الكامل في بياناتك";

// Export
"export_all_data" = "تصدير جميع البيانات";
"export_profile_only" = "تصدير الملف الشخصي فقط";
"export_activities_only" = "تصدير الأنشطة فقط";
"export_all_description" = "الملف الشخصي والأنشطة في ملفين";
"export_profile_description" = "الإعدادات الشخصية والتفضيلات";
"export_activities_description" = "جميع بيانات التدريب والإحصائيات";
"export_data_description" = "صدّر بياناتك كملفات CSV للنسخ الاحتياطي أو النقل";

// Import
"import_data_files" = "استيراد ملفات البيانات";
"import_data_files_description" = "اختر ملفات CSV التي تم تصديرها مسبقاً";
"import_data_description" = "استرداد من ملفات تم تصديرها مسبقاً";

// Delete
"delete_all_data" = "حذف جميع البيانات";
"delete_all_data_description" = "إزالة جميع البيانات نهائياً";
"delete_data_description" = "هذا الإجراء لا يمكن التراجع عنه";
"delete_confirmation" = "تأكيد الحذف";
"delete_confirmation_message" = "سيؤدي هذا إلى حذف جميع بيانات ملفك الشخصي وأنشطتك نهائياً. لا يمكن التراجع عن هذا الإجراء.";
"delete_permanently" = "حذف نهائي";

// Status Messages
"importing" = "جاري الاستيراد...";
"deleting" = "جاري الحذف...";
"export_failed" = "فشل التصدير";
"import_failed" = "فشل الاستيراد";
"delete_failed" = "فشل الحذف";
"import_success" = "نجح الاستيراد";
"import_success_message" = "تم استيراد البيانات بنجاح";
"delete_success" = "تم حذف البيانات";
"delete_success_message" = "تم حذف جميع بياناتك نهائياً. ستعود الآن إلى الشاشة الرئيسية.";
"import_error" = "خطأ في الاستيراد";
"export_error" = "خطأ في التصدير";
"delete_error" = "خطأ في الحذف";

"select_gender_title" = "Select Gender [NEEDS TRANSLATION]";

"select_gender_title" = "اختر الجنس";

// MARK: - Activity Stats View
"act_dist" = "مسافة النشاط";
"act_time" = "وقت النشاط";
"act_cal" = "سعرات النشاط";
"performance" = "الأداء";
"best_pace" = "أفضل إيقاع";
"avg_speed" = "متوسط السرعة";
"max_speed" = "أقصى سرعة";
"time_analysis" = "تحليل الوقت";
"total_time" = "إجمالي الوقت";
"paused_time" = "وقت التوقف";
"active_calories" = "نشط";
"resting_calories" = "راحة";
"rate" = "المعدل";
"kcal" = "سعرة حرارية";
"cal_per_min" = "سعرة/دقيقة";
