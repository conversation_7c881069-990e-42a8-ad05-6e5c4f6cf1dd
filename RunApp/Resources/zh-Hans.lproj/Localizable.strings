// ActivityControls
"activities" = "运动记录";
"metronome" = "节拍器";

// StatsView
"distance" = "距离";
"active" = "活跃";
"time" = "时间";
"current" = "当前";
"pace" = "配速";
"km" = "公里";
"mi" = "英里";

// ActivityView
"activities" = "运动记录";
"no_activities" = "暂无运动记录";
"complete_first_run" = "完成首次训练，记录就会显示在这里";

// ActivityRowView
"distance" = "距离";
"duration" = "用时";
"avg_pace" = "平均配速";
"calories" = "卡路里";
"start" = "起跑";
"end" = "冲线";

// ActivityDetailView
"delete_activity" = "删除运动记录";
"are_you_sure_deletion" = "确定要删除吗？此操作无法撤销。";

// CountdownView
"go" = "出发！";

// LanguageSettingView
"language" = "语言";
"done" = "完成";

// AuthView
"runapp" = "RunApp";
"email" = "邮箱";
"sign_in_with_email" = "邮箱登录";
"check_inbox_login_link" = "请查看邮箱中的登录链接。";

// AnalysisView
"improve_with_personalized_data" = "用个性化数据科学提升";
"run" = "跑步";
"walk" = "健走";
"hike" = "徒步";
"bike" = "骑行";
"distance" = "距离";
"duration" = "用时";
"calories" = "卡路里";
"current_week" = "本周";
"current_month" = "本月";
"current_year" = "今年";
"per_day" = "每日";
"per_month" = "每月";
"generate_test_data" = "生成测试数据";

// MetronomeSettingsView
"metronome_settings" = "节拍器";
"tempo" = "节拍速度";
"tempo_footer" = "调节每分钟 40-240 拍的节奏";
"enable_metronome" = "启用节拍器";
"sound" = "声音";
"sound_type" = "声音类型";
"vibration" = "振动";
"vibration_strength" = "振动强度";
"alert_frequency" = "提醒频率";
"largo" = "极慢板";
"adagio" = "柔板";
"andante" = "行板";
"moderato" = "中板";
"allegro" = "快板";
"presto" = "急板";
"prestissimo" = "极快板";
"default_beat" = "默认节拍";
"beat_2" = "节拍2";
"beat_3" = "节拍3";
"beat_1" = "节拍1";
"beat_4" = "节拍4";
"beat_5" = "节拍5";
"beat_6" = "节拍6";
"feedback" = "声音与振动";
"feedback_footer" = "启用声音后可选择不同的节拍器音效";

// NameSettingView
"name" = "姓名";
"enter_your_name" = "输入你的姓名";
"cancel" = "取消";
"save" = "保存";
"invalid_name" = "姓名无效";
"please_enter_valid_name" = "请输入有效的姓名";

// WeightSettingView
"weight" = "体重";
"unit" = "单位";
"calorie_calculations_required" = "精准计算卡路里消耗必需";
"invalid_weight" = "体重无效";
"please_enter_valid_weight" = "请输入 %d 到 %d %@ 之间的有效体重";

// GenderSettingView
"gender" = "性别";
"gender_footer" = "可选项 - 有助于更精准的卡路里计算";

// AgeSettingView
"age" = "年龄";
"birth_date" = "出生日期";
"years_old" = "岁";
"invalid_age" = "年龄无效";
"age_requirement" = "使用本应用需年满 13 岁";

// HeightSettingView
"height" = "身高";
"height_cm" = "身高（厘米）";
"feet" = "英尺";
"inches" = "英寸";
"bmi_calculations" = "可选项 - 用于 BMI 计算";
"invalid_height" = "身高无效";
"please_enter_valid_height" = "请输入有效的身高。";

// SettingsView
"settings" = "设置";
"profile" = "个人档案";
"not_set" = "未设置";
"age" = "年龄";
"years" = "岁";
"audio" = "音频";
"preferences" = "偏好设置";
"metronome" = "节拍器";
"bpm" = "拍/分";
"units" = "单位";
"metric" = "公制";
"imperial" = "英制";
"theme" = "主题";
"language" = "语言";
"data" = "数据";
"all_activities" = "全部运动";
"export_activities" = "导出运动数据";
"exporting" = "正在导出...";
"error" = "错误";
"ok" = "确定";
"unknown_error" = "发生未知错误";
"system_default" = "系统默认";
"light" = "浅色";
"dark" = "深色";
"male" = "男";
"female" = "女";
"other" = "其他";
"prefer_not_to_say" = "不愿透露";

// UnitsSettingView
"unit_system" = "单位制";

// SportTypeSelector
"run" = "跑步";
"walk" = "健走";
"hike" = "徒步";
"bike" = "骑行";

// ProfileHeaderView
"your_name" = "你的姓名";
"email_example" = "<EMAIL>";
"change" = "更改";
"delete_photo" = "删除照片？";
"delete" = "删除";

// ActivitySummaryComponents
"total" = "总计";
"avg" = "平均";
"per_day" = "每日";
"per_month" = "每月";

// PeriodIndicatorView
"current_week" = "本周";
"week_ago_format" = "%d周前（%d）";
"weeks_ago_format" = "%d周前（%d）";
"current_month" = "本月";
"month_ago_format" = "%d个月前（%d）";
"months_ago_format" = "%d个月前（%d）";
"current_year" = "今年";
"year_ago_format" = "%d年前（%d）";
"years_ago_format" = "%d年前（%d）";

// TimePeriodPicker
"time_period" = "时间范围";
"seven_days" = "一周";
"one_month" = "一个月";
"one_year" = "一年";
"all_time" = "全部时间";

// For timeOfDay values
"morning" = "晨练";
"afternoon" = "午练";
"evening" = "傍晚";
"night" = "夜练";

"every_beat" = "每一拍";
"every_other_beat" = "每隔一拍";
"every_4th_beat" = "每4拍";
"every_6th_beat" = "每6拍";

// AudioAlertSettingsView
"audio_prompts" = "语音提醒";
"enable_audio_prompts" = "启用语音提醒";
"audio_prompts_footer" = "运动过程中接收语音播报";
"distance_alerts" = "距离提醒";
"distance_alerts_footer" = "播报每个距离里程碑";
"time_alerts" = "时间提醒";
"time_alerts_footer" = "播报每个时间间隔";
"calorie_alerts" = "卡路里提醒";
"calorie_alerts_footer" = "播报卡路里燃烧里程";
"pace_alerts" = "配速提醒";
"pace_alerts_footer" = "定时播报当前配速";
"custom" = "自定义";
"value" = "数值";
"min" = "分钟";
"cal" = "卡";
"enabled" = "已启用";
"disabled" = "已禁用";
"hour" = "小时";
"h" = "时";
"m" = "分";
"distance_short" = "距离";
"time_short" = "时间";
"calorie_short" = "卡路里";
"pace_short" = "配速";
"no_alerts_configured" = "未设置";
"all_alerts" = "全部启用";
"enter_custom_value" = "输入自定义值";
"integers_only" = "仅限整数";
"invalid_input" = "输入无效";
"please_enter_positive_number" = "请输入正数";
"please_enter_whole_number" = "请输入整数（无小数）";
"please_enter_value_between" = "请输入 %@ 到 %@ 之间的值";
"invalid" = "无效";

"per_day" = "每日";
"per_month" = "每月";

// MARK: - Welcome View
"welcome.title" = "欢迎使用 RunApp";
"welcome.subtitle" = "装备就绪，每一步都算数！";
"welcome.button.start" = "开始训练";

// MARK: - Basic Info View
"basicInfo.header.title" = "个性化设置";
"basicInfo.header.subtitle" = "精准计算卡路里消耗";

// MARK: - Gender Selection
"basicInfo.gender.title" = "你的性别是？";
"basicInfo.gender.male" = "男";
"basicInfo.gender.female" = "女";
"basicInfo.gender.preferNotToSay" = "不愿透露";
"basicInfo.button.skip" = "跳过";

// MARK: - Weight Input
"basicInfo.weight.title" = "你的体重是多少？";
"basicInfo.weight.unit.kg" = "公斤";
"basicInfo.weight.unit.lbs" = "磅";

// MARK: - Height Input
"basicInfo.height.title" = "你的身高是多少？";
"basicInfo.height.unit.cm" = "厘米";
"basicInfo.height.unit.ftIn" = "英尺";
"basicInfo.height.placeholder.cm" = "厘米";
"basicInfo.height.placeholder.ft" = "英尺";
"basicInfo.height.placeholder.in" = "英寸";

"basicInfo.button.continue" = "继续";

// MARK: - All Set View
"allSet.title" = "准备就绪！";
"allSet.subtitle.first" = "是时候开始挥汗如雨了";
"allSet.subtitle.second" = "没有借口，只有行动！";
"allSet.button.go" = "出发！";

// MARK: - ContentView Alerts
"alert.location.title" = "需要位置权限";
"alert.location.message" = "启用「始终允许」定位以精准追踪路线，保持运动数据准确。请在设置中更新权限。";
"alert.location.settings" = "设置";
"alert.location.later" = "稍后";

"alert.complete.title" = "完成运动？";
"alert.complete.message" = "这将结束当前运动。\n完成后无法继续。";
"alert.complete.confirm" = "完成";
"alert.complete.cancel" = "取消";

"alert.shortWorkout.title" = "确认短时训练";
"alert.shortWorkout.message" = "你的训练少于 30 秒，确定要保存吗？";
"alert.shortWorkout.save" = "保存";
"alert.shortWorkout.discard" = "放弃";

// MARK: - Map Markers
"map.marker.start" = "起点";
"map.marker.end" = "终点";
"map.location.unknown" = "未知位置";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "节拍器已开启";
"metronome.status.off" = "节拍器已关闭";

"activity.summary.title" = "运动总结";

// GPS Status
"gps.searching" = "正在搜索 GPS 信号...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "你已解锁无限训练，尽情挥洒汗水，燃烧激情！";
"settings.subscription.status.exceeded" = "免费训练已用完！立即订阅，继续燃烧你的运动激情！";
"settings.subscription.status.oneRemaining" = "还剩 1 次免费训练 — 抓紧订阅，保持运动节奏！";
"settings.subscription.status.remaining" = "还剩 %d 次免费训练 — 马上订阅，继续燃烧卡路里！";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "订阅服务";
"settings.subscription.unlimitedAccess" = "无限训练";
"settings.subscription.checkingStatus" = "检查中...";
"settings.subscription.alert.alreadySubscribed.title" = "已订阅";
"settings.subscription.alert.alreadySubscribed.message" = "你已拥有无限训练权限。";
"settings.subscription.alert.checkFailed.format" = "检查订阅状态失败：%@";

// MARK: - Custom Value Interface
"custom_interval" = "自定义间隔";
"interval_value" = "间隔值";
"enter_value_placeholder" = "输入数值";
"whole_numbers_only" = "仅限整数";
"valid_range" = "有效范围：%@ - %@";
"preset_options" = "快速选项：";
"custom_value" = "自定义值";

// MARK: - Audio Volume Settings
"audio_volume" = "音频音量";
"audio_volume_footer" = "调节节拍器音量";
"volume" = "音量";
"voice_volume" = "语音音量";
"speech_speed" = "语音速度";
"speech_speed_footer" = "控制语音提醒的播放速度。在高强度训练或嘈杂环境中，较慢的速度有助于理解。";
"audio_settings" = "音频设置";
"audio_settings_footer" = "调节语音播报音量和速度";
"test_voice" = "测试语音";

// MARK: - Smart Voice Messages
"smart_voice" = "智能语音";
"smart_voice_footer" = "启用自然且富有激励性的语音提醒，替代标准播报";

// Test messages
"audio.test.standard" = "语音提醒工作正常";
"audio.test.smart" = "加油！你的语音清晰无比，准备为你加油助威！";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "棒极了！你已完成 %@ %@";
"audio.distance.achievement.smart" = "太厉害了！%@ %@ 里程碑达成！你简直无敌！";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "你已经坚持了 %@，继续保持这种精彩状态！";

// Calorie messages
"audio.calories.standard" = "%@ 卡路里";
"audio.calories.smart" = "你已燃烧 %@ 卡路里！努力正在发光发热！";
"audio.calories.achievement.smart" = "震撼！%@ 卡路里燃烧完成！你就是燃脂机器！";
"audio.calories.achievement.50.smart" = "好样的！50卡路里到手！继续保持这股劲头！";
"audio.calories.achievement.100.smart" = "超棒！100卡路里燃烧完毕！你的坚持正在创造奇迹！";

// Pace messages
"audio.pace.standard" = "当前配速：%@";
"audio.pace.smart" = "你正保持 %@ 的配速，节奏很棒！";

// Unit strings for audio
"audio.unit.km.singular" = "公里";
"audio.unit.km.plural" = "公里";
"audio.unit.mile.singular" = "英里";
"audio.unit.mile.plural" = "英里";

// Time formatting for audio
"audio.time.minute.singular" = "1分钟";
"audio.time.minutes" = "%d分钟";
"audio.time.hour.singular" = "1小时";
"audio.time.hours" = "%d小时";
"audio.time.hours.minutes" = "%d小时%d分钟";

// Pace formatting for audio
"audio.pace.per.km" = "每公里";
"audio.pace.per.mile" = "每英里";
"audio.pace.seconds" = "%d秒%@";
"audio.pace.minutes" = "%d分钟%@";
"audio.pace.minutes.seconds" = "%d分%d秒%@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "标准";
"voice.quality.enhanced" = "增强";
"voice.quality.premium" = "高级";
"voice.manager.test.text" = "这是为您的跑步提醒选择的语音测试";
"voice.test.sample" = "这是为您的跑步提醒选择的语音测试";
"voice.selection.title" = "语音选择";
"voice.current" = "当前语音";
"voice.preview" = "预览";
"voice.none.available" = "此语言没有可用语音";
"voice.loading" = "正在加载语音...";

// MARK: - Voice Selection UI
"voice.download.required" = "需要下载";
"voice.loading.voices" = "正在加载语音...";
"voice.empty.title" = "没有可用语音";
"voice.empty.message" = "此语言没有增强或高级语音。从设置中下载高质量语音以改善体验。";
"voice.refresh.voices" = "刷新语音";
"voice.refresh.footer" = "点击刷新可用语音列表";
"voice.current.language" = "当前语言";
"voice.selection.description" = "为语音提醒选择您偏好的语音。\n增强和高级选项提供最高质量。";
"voice.selection.workout.description" = "为运动语音提醒选择您的语音。高级语音在跑步过程中提供最清晰、最激励人心的体验。";
"voice.selection.simple.description" = "为运动语音提醒选择您偏好的语音。";
"voice.preview.error.title" = "预览失败";
"voice.preview.error.message" = "无法预览语音 '%@'。请重试。";
"voice.add.new" = "添加新语音";
"voice.manage.description" = "前往 设置 > 辅助功能 > 朗读内容 > 语音 下载和管理语音";
"voice.system.default" = "系统默认";

// MARK: - Voice Instructions
"voice.instructions.title" = "下载新语音";
"voice.instructions.message" = "按照以下步骤下载高质量语音：\n\n1. 点击下方「打开设置」\n2. 导航至：辅助功能\n3. 选择：朗读内容\n4. 选择：语音\n5. 下载您偏好的语音\n6. 返回 RunApp 并刷新";
"voice.instructions.open.settings" = "打开设置";
"voice.instructions.steps.title" = "如何添加语音：";
"voice.instructions.steps.detail" = "设置 → 辅助功能 → 朗读内容 → 语音";
"voice.instructions.footer" = "点击上方按钮查看下载新语音的分步说明";
"voice.add.new.subtitle" = "获取分步说明";
"voice.management.footer" = "从iOS设置下载新语音，然后刷新以查看新安装的语音";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "语音选择";
"voice.selection.footer" = "为语音播报选择您偏好的语音";

// Common buttons
"common.cancel" = "取消";
"common.close" = "关闭";
"common.done" = "完成";
"common.ok" = "确定";

// MARK: - Voice Management
"voice.delete.success" = "语音删除成功";
"voice.delete.failed" = "删除语音失败";
"voice.delete.confirm.title" = "删除语音？";
"voice.delete.confirm.message" = "这将从您的设备中移除 '%@'。您可以稍后从设置中重新下载。";
"voice.delete.confirm.delete" = "删除";
"voice.delete.unavailable" = "此语音无法删除，因为它是系统默认语音。";
"voice.manage.voices" = "管理语音";
"voice.download.status" = "已下载";
"voice.builtin.status" = "内置";

// MARK: - Smart Voice Prompt Variations (Simplified Chinese)
// Distance Smart Variations - 地道中文口语化
"audio.distance.smart.1" = "已经跑了 %@ %@ 了！太厉害了！";
"audio.distance.smart.2" = "%@ %@ 到手！继续保持！";
"audio.distance.smart.3" = "不错嘛！%@ %@ 轻松拿下！";
"audio.distance.smart.4" = "太强了！%@ %@ 已经完成！加油加油！";
"audio.distance.smart.5" = "厉害厉害！%@ %@ 达成了！你真行！";
"audio.distance.smart.6" = "%@ %@ 搞定！继续冲鸭！";
"audio.distance.smart.7" = "真的棒！%@ %@ 又突破了！";
"audio.distance.smart.8" = "好样的！%@ %@ 成功解锁！";
"audio.distance.smart.9" = "太赞了！%@ %@ 轻松过关！";
"audio.distance.smart.10" = "%@ %@ 完美达成！";

// Time Smart Variations - 地道中文口语化
"audio.time.smart.1" = "已经坚持 %@ 啦！真有毅力！";
"audio.time.smart.2" = "%@ 过去了！还这么有劲儿！";
"audio.time.smart.3" = "厉害！%@ 了还在跑！太强了！";
"audio.time.smart.4" = "不错！%@ 的坚持！继续保持！";
"audio.time.smart.5" = "好棒！专注了 %@ ！加油！";
"audio.time.smart.6" = "%@ 的努力！真的佩服！";
"audio.time.smart.7" = "太赞了！%@ 的坚持！继续冲！";
"audio.time.smart.8" = "好样的！%@ 还在拼！真厉害！";
"audio.time.smart.9" = "真棒！%@ 的付出！你最棒！";
"audio.time.smart.10" = "%@ 的坚持！无敌了！";

// Calories Smart Variations - 地道中文口语化
"audio.calories.smart.1" = "已经烧掉 %@ 卡了！太厉害！";
"audio.calories.smart.2" = "牛啊！%@ 卡路里消灭！继续燃烧！";
"audio.calories.smart.3" = "不错嘛！%@ 卡轻松搞定！";
"audio.calories.smart.4" = "太强了！%@ 卡已经拿下！加油！";
"audio.calories.smart.5" = "厉害！%@ 卡路里烧光光！";
"audio.calories.smart.6" = "%@ 卡成功消耗！真棒！";
"audio.calories.smart.7" = "好样的！%@ 卡又突破了！";
"audio.calories.smart.8" = "太赞了！%@ 卡轻松过关！";
"audio.calories.smart.9" = "真厉害！%@ 卡完美燃烧！";
"audio.calories.smart.10" = "%@ 卡路里秒杀！无敌！";

// Pace Smart Variations - 地道中文口语化
"audio.pace.smart.1" = "  稳住！%@ 的配速！节奏很棒！";
"audio.pace.smart.2" = "  不错！%@ 配速控制得很好！";
"audio.pace.smart.3" = "  厉害！%@ 的节奏！很稳定！";
"audio.pace.smart.4" = "  好样的！%@ 配速保持得真好！";
"audio.pace.smart.5" = "  哇塞！%@ 的速度！太完美了！";
"audio.pace.smart.6" = "  真棒！%@ 配速刚刚好！";
"audio.pace.smart.7" = "  太赞了！%@ 的节奏！专业级别！";
"audio.pace.smart.8" = "  好厉害！%@ 配速很流畅！";
"audio.pace.smart.9" = "  %@ 的速度！太稳了！";
"audio.pace.smart.10" = "  %@ 配速完美控制！";

// MARK: - Workout Settings
"workout.settings.title" = "运动设置";
"workout.settings.notice" = "运动进行中";
"workout.settings.safe.only" = "仅运动安全设置可用";

// MARK: - Premium Voice Benefits
"voice.premium.benefits.title" = "优质语音带来最佳运动体验";
"voice.premium.benefits.subtitle" = "获得水晶般清晰的语音提醒，在环境噪音中清晰可闻，在高强度训练中激励您";
"voice.premium.benefits.recommendation" = "建议添加高级语音以获得最佳体验";
"voice.premium.benefit.clarity" = "户外运动音频清晰度更佳";
"voice.premium.benefit.noise" = "在嘈杂环境中表现更好";
"voice.premium.benefit.motivation" = "更自然、更激励人心的语音音调";
"voice.premium.recommended" = "推荐";
"voice.premium.footer" = "高级语音在运动中提供最清晰的音频，具有更好的抗噪性和自然的语音模式。";
"voice.enhanced.footer" = "增强语音相比标准系统语音提供更好的音质和清晰度。";

// MARK: - Voice Quality Descriptions
"voice.quality.standard.description" = "标准质量语音";
"voice.quality.enhanced.description" = "增强清晰度的音频";
"voice.quality.premium.description" = "针对运动激励优化";

// MARK: - Data Management
"data_management" = "数据管理";
"data_privacy" = "数据隐私";
"export_data" = "导出数据";
"import_data" = "导入数据";
"delete_data" = "删除数据";

// Privacy Messages
"privacy_ownership" = "您保留数据的所有权";
"privacy_icloud_storage" = "安全存储在您的iCloud中";
"privacy_no_access" = "我们（作为开发者）无法访问您的个人数据";
"privacy_full_control" = "您保持对数据的完全控制";

// Export
"export_all_data" = "导出所有数据";
"export_profile_only" = "仅导出个人资料";
"export_activities_only" = "仅导出活动";
"export_all_description" = "个人资料和活动在2个文件中";
"export_profile_description" = "个人设置和偏好";
"export_activities_description" = "所有运动数据和统计信息";
"export_data_description" = "将数据导出为CSV文件以备份或转移";

// Import
"import_data_files" = "导入数据文件";
"import_data_files_description" = "选择之前导出的CSV文件";
"import_data_description" = "从之前导出的文件恢复";

// Delete
"delete_all_data" = "删除所有数据";
"delete_all_data_description" = "永久删除所有数据";
"delete_data_description" = "此操作无法撤销";
"delete_confirmation" = "确认删除";
"delete_confirmation_message" = "这将永久删除您的所有个人资料数据和活动。此操作无法撤销。";
"delete_permanently" = "永久删除";

// Status Messages
"importing" = "正在导入...";
"deleting" = "正在删除...";
"export_failed" = "导出失败";
"import_failed" = "导入失败";
"delete_failed" = "删除失败";
"import_success" = "导入成功";
"import_success_message" = "数据已成功导入";
"delete_success" = "数据已删除";
"delete_success_message" = "您的所有数据已被永久删除。现在将返回主屏幕。";
"import_error" = "导入错误";
"export_error" = "导出错误";
"delete_error" = "删除错误";

"select_gender_title" = "选择性别";

// MARK: - Activity Stats View
"act_dist" = "训练距离";
"act_time" = "训练时间";
"act_cal" = "训练卡";
"performance" = "运动表现";
"best_pace" = "最佳配速";
"avg_speed" = "平均速度";
"max_speed" = "最高速度";
"time_analysis" = "时间分析";
"total_time" = "总时间";
"paused_time" = "暂停时间";
"active_calories" = "运动";
"resting_calories" = "静息";
"rate" = "燃烧率";
"kcal" = "千卡";
"cal_per_min" = "卡/分";
