// ActivityControls
"activities" = "Activities";
"metronome" = "Metronome";

// StatsView
"distance" = "Distance";
"active" = "Active";
"time" = "Time";
"current" = "Current";
"pace" = "Pace";
"km" = "km";
"mi" = "mi";

// ActivityView
"activities" = "Activities";
"no_activities" = "No Activities";
"complete_first_run" = "Complete your first run to see it here";

// ActivityRowView
"distance" = "Distance";
"duration" = "Duration";
"avg_pace" = "Avg Pace";
"calories" = "Calories";
"start" = "Start";
"end" = "End";

// ActivityDetailView
"delete_activity" = "Delete Activity";
"are_you_sure_deletion" = "Are you sure? Deletion cannot be undone.";

// CountdownView
"go" = "GO!";

// LanguageSettingView
"language" = "Language";
"done" = "Done";

// AuthView
"runapp" = "RunApp";
"email" = "Email";
"sign_in_with_email" = "Sign in with Email";
"check_inbox_login_link" = "Check your inbox for the login link.";

// AnalysisView
"improve_with_personalized_data" = "Improve with Personalized Data";
"run" = "Run";
"walk" = "Walk";
"hike" = "Hike";
"bike" = "Bike";
"distance" = "Distance";
"duration" = "Duration";
"calories" = "Calories";
"current_week" = "Current week";
"current_month" = "Current month";
"current_year" = "Current year";
"per_day" = "per day";
"per_month" = "per month";
"generate_test_data" = "Generate Test Data";

// MetronomeSettingsView
"metronome_settings" = "Metronome";
"tempo" = "Beat tempo";
"tempo_footer" = "Adjust the tempo between 40-240 beats per minute";
"enable_metronome" = "Enable Metronome";
"sound" = "Sound";
"sound_type" = "Sound Type";
"vibration" = "Vibration";
"vibration_strength" = "Vibration Strength";
"alert_frequency" = "Alert Frequency";
"largo" = "Largo";
"adagio" = "Adagio";
"andante" = "Andante";
"moderato" = "Moderato";
"allegro" = "Allegro";
"presto" = "Presto";
"prestissimo" = "Prestissimo";
"default_beat" = "Default Beat";
"beat_2" = "Beat 2";
"beat_3" = "Beat 3";
"beat_1" = "Beat 1";
"beat_4" = "Beat 4";
"beat_5" = "Beat 5";
"beat_6" = "Beat 6";

"feedback" = "Sound and Vibration";
"feedback_footer" = "Choose from different metronome sound types when sound is enabled";

// NameSettingView
"name" = "Name";
"enter_your_name" = "Enter Your Name";
"cancel" = "Cancel";
"save" = "Save";
"invalid_name" = "Invalid Name";
"please_enter_valid_name" = "Please enter a valid name";

// WeightSettingView
"weight" = "Weight";
"unit" = "Unit";
"calorie_calculations_required" = "Required for accurate calorie calculations";
"invalid_weight" = "Invalid Weight";
"please_enter_valid_weight" = "Please enter a valid weight between %d and %d %@";

// GenderSettingView
"gender" = "Gender";
"gender_footer" = "Optional - Used for more accurate calorie calculations";

// AgeSettingView
"age" = "Age";
"birth_date" = "Birth Date";
"years_old" = "years old";
"invalid_age" = "Invalid Age";
"age_requirement" = "You must be at least 13 years old to use this app";

// HeightSettingView
"height" = "Height";
"height_cm" = "Height (cm)";
"feet" = "Feet";
"inches" = "Inches";
"bmi_calculations" = "Optional - Used for BMI calculations";
"invalid_height" = "Invalid Height";
"please_enter_valid_height" = "Please enter a valid height.";

// SettingsView
"settings" = "Settings";
"profile" = "Profile";
"not_set" = "Not set";
"age" = "Age";
"years" = "years";
"audio" = "Audio";
"preferences" = "Preferences";
"metronome" = "Metronome";
"bpm" = "bpm";
"units" = "Units";
"metric" = "Metric";
"imperial" = "Imperial";
"theme" = "Theme";
"language" = "Language";
"data" = "Data";
"all_activities" = "All Activities";
"export_activities" = "Export Activities";
"exporting" = "Exporting...";
"error" = "Error";
"ok" = "OK";
"unknown_error" = "Unknown error occurred";
"system_default" = "System Default";
"light" = "Light";
"dark" = "Dark";
   "male" = "Male";
   "female" = "Female";
   "other" = "Other";
   "prefer_not_to_say" = "Prefer not to say";

// UnitsSettingView
"unit_system" = "Unit System";

// SportTypeSelector
"run" = "Run";
"walk" = "Walk";
"hike" = "Hike";
"bike" = "Bike";

// ProfileHeaderView
"your_name" = "Your Name";
"email_example" = "<EMAIL>";
"change" = "Change";
"delete_photo" = "Delete Photo?";
"delete" = "Delete";

// ActivitySummaryComponents
"total" = "Total";
"avg" = "Avg";
"per_day" = "per day";
"per_month" = "per month";

// PeriodIndicatorView
"current_week" = "Current week";
"week_ago_format" = "%d week ago (%d)";
"weeks_ago_format" = "%d weeks ago (%d)";
"current_month" = "Current month";
"month_ago_format" = "%d month ago (%d)";
"months_ago_format" = "%d months ago (%d)";
"current_year" = "Current year";
"year_ago_format" = "%d year ago (%d)";
"years_ago_format" = "%d years ago (%d)";

// TimePeriodPicker
"time_period" = "Time Period";
"seven_days" = "1 week";
"one_month" = "1 Month";
"one_year" = "1 Year";
"all_time" = "All Time";

     // For timeOfDay values
     "morning" = "Morning";
     "afternoon" = "Afternoon";
     "evening" = "Evening";
     "night" = "Night";

"every_beat" = "Every Beat";
"every_other_beat" = "Every Other Beat";
"every_4th_beat" = "Every 4th Beat";
"every_6th_beat" = "Every 6th Beat";

// AudioAlertSettingsView
"audio_prompts" = "Audio Prompts";
"enable_audio_prompts" = "Enable Audio Prompts";
"audio_prompts_footer" = "Receive voice announcements during your runs";
"distance_alerts" = "Distance Alerts";
"distance_alerts_footer" = "Announce every distance milestone";
"time_alerts" = "Time Alerts";
"time_alerts_footer" = "Announce every time interval";
"calorie_alerts" = "Calorie Alerts";
"calorie_alerts_footer" = "Announce calorie milestones";
"pace_alerts" = "Pace Alerts";
"pace_alerts_footer" = "Announce current pace at intervals";
"custom" = "Custom";
"value" = "Value";
"min" = "min";
"cal" = "cal";
"enabled" = "Enabled";
"disabled" = "Disabled";
"hour" = "hour";
"h" = "h";
"m" = "m";
"distance_short" = "Distance";
"time_short" = "Time";
"calorie_short" = "Calories";
"pace_short" = "Pace";
"no_alerts_configured" = "None set";
"all_alerts" = "All enabled";
"enter_custom_value" = "Enter custom value";
"integers_only" = "Whole numbers only";
"invalid_input" = "Invalid Input";
"please_enter_positive_number" = "Please enter a positive number";
"please_enter_whole_number" = "Please enter a whole number (no decimals)";
"please_enter_value_between" = "Please enter a value between %@ and %@";
"invalid" = "Invalid";

"per_day" = "per day";
"per_month" = "per month";


// MARK: - Welcome View
"welcome.title" = "Welcome to RunApp";
"welcome.subtitle" = "Gear up, every step counts!";
"welcome.button.start" = "Get Started";

// MARK: - Basic Info View
"basicInfo.header.title" = "Personalize experience";
"basicInfo.header.subtitle" = "for Calories estimation";

// MARK: - Gender Selection
"basicInfo.gender.title" = "What's your gender?";
"basicInfo.gender.male" = "Male";
"basicInfo.gender.female" = "Female";
"basicInfo.gender.preferNotToSay" = "Prefer Not To Say";
"basicInfo.button.skip" = "Skip";

// MARK: - Weight Input
"basicInfo.weight.title" = "What's your weight?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lbs";

// MARK: - Height Input
"basicInfo.height.title" = "What's your height?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "FT";
"basicInfo.height.placeholder.in" = "IN";

"basicInfo.button.continue" = "Continue";

// MARK: - All Set View
"allSet.title" = "You're All Set!";
"allSet.subtitle.first" = "Time to hit the pavement";
"allSet.subtitle.second" = "No excuses, just action！";
"allSet.button.go" = "Go!";

// MARK: - ContentView Alerts
"alert.location.title" = "Location Permission Required";
"alert.location.message" = "Enable 'Always Allow' location to flawlessly track routes, and keep your progress accurate. Please update your settings.";
"alert.location.settings" = "Settings";
"alert.location.later" = "Later";

"alert.complete.title" = "Complete Activity?";
"alert.complete.message" = "This will end your current activity.\nYou cannot resume after completing.";
"alert.complete.confirm" = "Complete";
"alert.complete.cancel" = "Cancel";

"alert.shortWorkout.title" = "Confirm Short Workout";
"alert.shortWorkout.message" = "Your workout is less than 30 seconds. Are you sure you want to save it?";
"alert.shortWorkout.save" = "Save";
"alert.shortWorkout.discard" = "Discard";

// MARK: - Map Markers
"map.marker.start" = "Start";
"map.marker.end" = "End";
"map.location.unknown" = "Unknown Location";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "Metronome On";
"metronome.status.off" = "Metronome Off";

"activity.summary.title" = "Activity summary";

// GPS Status
"gps.searching" = "Searching for GPS...";
"gps.poor_signal" = "Poor GPS Signal";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "You have unlimited access.";
"settings.subscription.status.exceeded" = "You have reached the free trial limit. Please subscribe for unlimited access.";
"settings.subscription.status.oneRemaining" = "You have 1 free activity remaining — subscribe now to keep going!";
"settings.subscription.status.remaining" = "You have %d free activities remaining — subscribe now!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Subscription";
"settings.subscription.unlimitedAccess" = "Unlimited Access";
"settings.subscription.checkingStatus" = "Checking...";
"settings.subscription.alert.alreadySubscribed.title" = "Already Subscribed";
"settings.subscription.alert.alreadySubscribed.message" = "You already have unlimited access.";
"settings.subscription.alert.checkFailed.format" = "Failed to check subscription status: %@";

// MARK: - Custom Value Interface
"custom_interval" = "Custom Interval";
"interval_value" = "Interval Value";
"enter_value_placeholder" = "Enter value";
"whole_numbers_only" = "Whole numbers only";
"valid_range" = "Valid range: %@ - %@";
"preset_options" = "Quick options:";
"custom_value" = "Custom Value";

// MARK: - Audio Volume Settings
"audio_volume" = "Audio Volume";
"audio_volume_footer" = "Adjust the metronome sound volume";
"volume" = "Volume";
"voice_volume" = "Voice Volume";
"speech_speed" = "Speech Speed";
"speech_speed_footer" = "Control how fast voice prompts are spoken. Slower speeds help with comprehension during intense workouts or in noisy environments.";
"audio_settings" = "Audio Settings";
"audio_settings_footer" = "Adjust the voice announcement volume and speed";
"test_voice" = "Test Voice";

// MARK: - Smart Voice Messages
"smart_voice" = "Smart Voice";
"smart_voice_footer" = "Enable natural and motivational audio prompts instead of standard announcements";

// Test messages
"audio.test.standard" = "Audio alerts are working correctly";
"audio.test.smart" = "Hey there! Your audio is crystal clear and ready to motivate you!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "Great job! You've completed %@ %@";
"audio.distance.achievement.smart" = "Incredible! %@ %@ milestone reached! You're crushing it!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "You've been moving for %@. Keep up the fantastic work!";

// Calorie messages
"audio.calories.standard" = "%@ calories";
"audio.calories.smart" = "You've burned %@ calories! Your effort is paying off!";
"audio.calories.achievement.smart" = "Amazing! %@ calories burned! You're on fire!";

// Pace messages
"audio.pace.standard" = "Current pace: %@";
"audio.pace.smart" = "You're maintaining a %@ pace. Looking strong!";

// Unit strings for audio
"audio.unit.km.singular" = "kilometer";
"audio.unit.km.plural" = "kilometers";
"audio.unit.mile.singular" = "mile";
"audio.unit.mile.plural" = "miles";

// Time formatting for audio
"audio.time.minute.singular" = "1 minute";
"audio.time.minutes" = "%d minutes";
"audio.time.hour.singular" = "1 hour";
"audio.time.hours" = "%d hours";
"audio.time.hours.minutes" = "%d hours and %d minutes";

// Pace formatting for audio
"audio.pace.per.km" = "per kilometer";
"audio.pace.per.mile" = "per mile";
"audio.pace.seconds" = "%d seconds %@";
"audio.pace.minutes" = "%d minutes %@";
"audio.pace.minutes.seconds" = "%d minutes and %d seconds %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Standard";
"voice.quality.enhanced" = "Enhanced";
"voice.quality.premium" = "Premium";
"voice.manager.test.text" = "This is a test of the selected voice for your workout alerts";
"voice.test.sample" = "This is a test of the selected voice for your workout alerts";
"voice.selection.title" = "Voice Selection";
"voice.current" = "Current Voice";
"voice.preview" = "Preview";
"voice.none.available" = "No voices available for this language";
"voice.loading" = "Loading voices...";

// MARK: - Voice Selection UI
"voice.download.required" = "Download Required";
"voice.loading.voices" = "Loading voices...";
"voice.empty.title" = "No Voices Available";
"voice.empty.message" = "No enhanced or premium voices are available for this language. Download high-quality voices from Settings to improve your experience.";
"voice.refresh.voices" = "Refresh Voices";
"voice.refresh.footer" = "Tap to refresh the list of available voices";
"voice.current.language" = "Current Language";
"voice.selection.description" = "Select your preferred voice for audio alerts. \nEnhanced and premium options offer the highest quality.";
"voice.selection.workout.description" = "Choose your voice for workout audio prompts. Premium voices deliver the clearest, most motivating experience during runs.";
"voice.selection.simple.description" = "Choose your preferred voice for workout audio prompts during your runs.";
"voice.preview.error.title" = "Preview Failed";
"voice.preview.error.message" = "Unable to preview the voice '%@'. Please try again.";
"voice.add.new" = "Add New Voice";
"voice.manage.description" = "Go to Settings > Accessibility > Spoken Content > Voices to download and manage voices";
"voice.system.default" = "System Default";

// MARK: - Premium Voice Benefits
"voice.premium.benefits.title" = "Premium Voices for Best Workout Experience";
"voice.premium.benefits.subtitle" = "Get crystal-clear audio prompts that cut through ambient noise and motivate you during intense workouts";
"voice.premium.benefits.recommendation" = "Recommend to add PREMIUM voice for best experience";
"voice.premium.benefit.clarity" = "Superior audio clarity for outdoor workouts";
"voice.premium.benefit.noise" = "Better performance in noisy environments";
"voice.premium.benefit.motivation" = "More natural, motivating voice tone";
"voice.premium.recommended" = "RECOMMENDED";
"voice.premium.footer" = "Premium voices provide the clearest audio during workouts with better noise resistance and natural speech patterns.";
"voice.enhanced.footer" = "Enhanced voices offer improved quality over standard system voices with better clarity.";

// MARK: - Voice Quality Descriptions
"voice.quality.standard.description" = "Standard quality voice";
"voice.quality.enhanced.description" = "Enhanced clarity for better audio";
"voice.quality.premium.description" = "Optimized for workout motivation";

// MARK: - Voice Instructions
"voice.instructions.title" = "Download New Voices";
"voice.instructions.message" = "Follow these steps to download high-quality voices:\n\n1. Tap 'Open Settings' below\n2. Navigate to: Accessibility\n3. Select: Spoken Content\n4. Choose: Voices\n5. Download your preferred voices\n6. Return to RunApp and refresh";
"voice.instructions.open.settings" = "Open Settings";
"voice.instructions.steps.title" = "How to add voices:";
"voice.instructions.steps.detail" = "Settings → Accessibility → Spoken Content → Voices";
"voice.instructions.footer" = "Tap the button above to see step-by-step instructions for downloading new voices";
"voice.add.new.subtitle" = "Get step-by-step instructions";
"voice.management.footer" = "Download new voices from ios Settings and refresh to see newly installed voices";

// Common buttons
"common.cancel" = "Cancel";
"common.close" = "Close";
"common.done" = "Done";
"common.ok" = "OK";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Voice Selection";
"voice.selection.footer" = "Choose your preferred voice for audio announcements";

// MARK: - Voice Management
"voice.delete.success" = "Voice deleted successfully";
"voice.delete.failed" = "Failed to delete voice";
"voice.delete.confirm.title" = "Delete Voice?";
"voice.delete.confirm.message" = "This will remove '%@' from your device. You can re-download it later from Settings.";
"voice.delete.confirm.delete" = "Delete";
"voice.delete.unavailable" = "This voice cannot be deleted as it's a system default voice.";
"voice.manage.voices" = "Manage Voices";
"voice.download.status" = "Downloaded";
"voice.builtin.status" = "Built-in";

// MARK: - Workout Settings
"workout.settings.title" = "Workout Settings";
"workout.settings.notice" = "Workout in Progress";
"workout.settings.safe.only" = "Only workout-safe settings available";

// MARK: - Smart Voice Prompt Variations
// Distance Smart Variations
"audio.distance.smart.1" = "YES! %@ %@ down! You're crushing it!";
"audio.distance.smart.2" = "Amazing work! %@ %@ conquered!";
"audio.distance.smart.3" = "Look at you! %@ %@ in the books!";
"audio.distance.smart.4" = "BOOM! %@ %@ of pure determination!";
"audio.distance.smart.5" = "Incredible! %@ %@ and going strong!";
"audio.distance.smart.6" = "You're on fire! %@ %@ achieved!";
"audio.distance.smart.7" = "Outstanding! %@ %@ of pure grit!";
"audio.distance.smart.8" = "Way to go! %@ %@ unlocked!";
"audio.distance.smart.9" = "Fantastic! %@ %@ and unstoppable!";
"audio.distance.smart.10" = "LEGEND! %@ %@ absolutely crushed!";

// Time Smart Variations
"audio.time.smart.1" = "Strong for %@! Your dedication shows!";
"audio.time.smart.2" = "YES! %@ of pure heart and hustle!";
"audio.time.smart.3" = "Amazing! %@ in and still pushing!";
"audio.time.smart.4" = "BOOM! %@ of relentless effort!";
"audio.time.smart.5" = "Incredible focus for %@! Keep going!";
"audio.time.smart.6" = "You're owning %@ of commitment!";
"audio.time.smart.7" = "Outstanding! %@ of pure determination!";
"audio.time.smart.8" = "Way to push! %@ of true grit!";
"audio.time.smart.9" = "Fantastic! %@ and building habits!";
"audio.time.smart.10" = "UNSTOPPABLE! %@ of pure dedication!";

// Calories Smart Variations
"audio.calories.smart.1" = "FIRE! %@ calories torched! You're amazing!";
"audio.calories.smart.2" = "YES! %@ calories burned through pure effort!";
"audio.calories.smart.3" = "BOOM! %@ calories obliterated! Keep going!";
"audio.calories.smart.4" = "Incredible! %@ calories of pure determination!";
"audio.calories.smart.5" = "Amazing work! %@ calories incinerated!";
"audio.calories.smart.6" = "You're crushing %@ calories of progress!";
"audio.calories.smart.7" = "Outstanding! %@ calories burned with heart!";
"audio.calories.smart.8" = "Way to go! %@ calories absolutely destroyed!";
"audio.calories.smart.9" = "FANTASTIC! %@ calories of pure dedication!";
"audio.calories.smart.10" = "LEGEND! %@ calories torched and counting!";

// Pace Smart Variations
"audio.pace.smart.1" = "LOCKED! %@ pace! You're in the zone!";
"audio.pace.smart.2" = "YES! %@ pace perfectly controlled!";
"audio.pace.smart.3" = "Amazing! %@ pace and looking strong!";
"audio.pace.smart.4" = "SMOOTH! %@ pace! Pure consistency!";
"audio.pace.smart.5" = "Incredible! %@ pace beautifully maintained!";
"audio.pace.smart.6" = "You're owning %@ pace right now!";
"audio.pace.smart.7" = "Outstanding! %@ pace! That's champion level!";
"audio.pace.smart.8" = "Way to flow! %@ pace is perfect!";
"audio.pace.smart.9" = "FANTASTIC! %@ pace locked and loaded!";
"audio.pace.smart.10" = "UNSTOPPABLE! %@ pace! Pure excellence!";

// MARK: - Data Management
"data_management" = "Data Management";
"data_privacy" = "Data Privacy";
"export_data" = "Export Data";
"import_data" = "Import Data";
"delete_data" = "Delete Data";

// Privacy Messages
"privacy_ownership" = "You retain ownership of your data";
"privacy_icloud_storage" = "Securely stored in your iCloud";
"privacy_no_access" = "We (as developers) cannot access your personal data";
"privacy_full_control" = "You maintain full control of your data";

// Export
"export_all_data" = "Export All Data";
"export_profile_only" = "Export Profile Only";
"export_activities_only" = "Export Activities Only";
"export_all_description" = "Profile and activities in 2 files";
"export_profile_description" = "Personal settings and preferences";
"export_activities_description" = "All workout data and statistics";
"export_data_description" = "Export your data as CSV files for backup or transfer";
"exporting_profile" = "Exporting profile...";
"exporting_activities" = "Exporting activities...";

// Import
"import_data_description" = "Import previously exported data to restore your information";
"import_data_files" = "Import Data Files";
"import_data_files_description" = "Select CSV files to restore your profile and activity data";
"importing" = "Importing";
"importing_data" = "Importing data...";
"select_csv_files" = "Select CSV files to import";
"import_profile_and_activities" = "Import Profile & Activities";
"import_merge_description" = "New data will be merged with existing data";

// Delete
"delete_data_description" = "Permanently remove all your data from this device";
"delete_all_data" = "Delete All Data";
"delete_all_data_description" = "Remove all activities, profile, and settings permanently";
"delete_confirmation" = "Delete All Data";
"delete_confirmation_message" = "This will permanently delete all your activity data and reset your profile. This action cannot be undone.";
"delete_permanently" = "Delete Permanently";
"deleting" = "Deleting";
"deleting_data" = "Deleting data...";
"delete_error" = "Delete Error";
"delete_success" = "Data Deleted";
"delete_success_message" = "All your data has been permanently deleted. You will now return to the main screen.";
"delete_warning" = "⚠️ This action cannot be undone";

// Success Messages
"export_success" = "Export Complete";
"export_success_message" = "Your data has been successfully exported";
"import_success" = "Import Complete";
"import_success_message" = "Data has been successfully imported";
"import_error" = "Import Error";
"export_error" = "Export Error";

// File Operations
"saving_file" = "Saving file...";
"file_saved" = "File saved successfully";
"choose_save_location" = "Choose where to save your data";

// Background Operations
"processing_in_background" = "Processing in background...";
"operation_completed" = "Operation completed successfully";

"select_gender_title" = "Select Gender";

// MARK: - Activity Stats View
"act_dist" = "Act Dist";
"act_time" = "Act Time";
"act_cal" = "Act Cal";
"performance" = "Performance";
"best_pace" = "Best Pace";
"avg_speed" = "Avg Speed";
"max_speed" = "Max Speed";
"time_analysis" = "Time Analysis";
"total_time" = "Total Time";
"paused_time" = "Paused Time";
"active_calories" = "Active";
"resting_calories" = "Resting";
"rate" = "Rate";
"kcal" = "kcal";
"cal_per_min" = "cal/min";
