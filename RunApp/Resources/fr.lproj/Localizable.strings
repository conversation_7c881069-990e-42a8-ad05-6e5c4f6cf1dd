// ActivityControls
"activities" = "Entraînements";
"metronome" = "Métronome";

// StatsView
"distance" = "Distance";
"active" = "Actif";
"time" = "Temps";
"current" = "Actuel";
"pace" = "Allure";
"km" = "km";
"mi" = "mi";

// ActivityView
"activities" = "Entraînements";
"no_activities" = "Aucun entraînement";
"complete_first_run" = "Termine ton premier entraînement pour le voir ici";

// ActivityRowView
"distance" = "Distance";
"duration" = "Temps";
"avg_pace" = "Allure moyenne";
"calories" = "Calories";
"start" = "Départ";
"end" = "Arrivée";

// ActivityDetailView
"delete_activity" = "Supprimer l'entraînement";
"are_you_sure_deletion" = "Es-tu sûr ? La suppression est irréversible.";

// CountdownView
"go" = "FONCE !";

// LanguageSettingView
"language" = "Langue";
"done" = "Terminé";

// AuthView
"runapp" = "RunApp";
"email" = "E-mail";
"sign_in_with_email" = "Se connecter par e-mail";
"check_inbox_login_link" = "Vérifie ta boîte mail pour le lien de connexion.";

// AnalysisView
"improve_with_personalized_data" = "Progresse scientifiquement avec tes données personnalisées";
"run" = "Course";
"walk" = "Marche";
"hike" = "Randonnée";
"bike" = "Vélo";
"distance" = "Distance";
"duration" = "Temps";
"calories" = "Calories";
"current_week" = "Semaine actuelle";
"current_month" = "Mois actuel";
"current_year" = "Année actuelle";
"per_day" = "par jour";
"per_month" = "par mois";
"generate_test_data" = "Générer des données test";

// MetronomeSettingsView
"metronome_settings" = "Métronome";
"tempo" = "Tempo";
"tempo_footer" = "Ajuster le tempo entre 40-240 battements par minute";
"enable_metronome" = "Activer le métronome";
"sound" = "Son";
"sound_type" = "Type de son";
"vibration" = "Vibration";
"vibration_strength" = "Force de vibration";
"alert_frequency" = "Fréquence d'alerte";
"largo" = "Largo";
"adagio" = "Adagio";
"andante" = "Andante";
"moderato" = "Moderato";
"allegro" = "Allegro";
"presto" = "Presto";
"prestissimo" = "Prestissimo";
"default_beat" = "Battement par défaut";
"beat_2" = "Battement 2";
"beat_3" = "Battement 3";
"beat_1" = "Battement 1";
"beat_4" = "Battement 4";
"beat_5" = "Battement 5";
"beat_6" = "Battement 6";
"feedback" = "Son et vibration";
"feedback_footer" = "Choisis parmi différents types de sons lorsque le son est activé";

// NameSettingView
"name" = "Nom";
"enter_your_name" = "Entre ton nom";
"cancel" = "Annuler";
"save" = "Enregistrer";
"invalid_name" = "Nom invalide";
"please_enter_valid_name" = "Merci d'entrer un nom valide";

// WeightSettingView
"weight" = "Poids";
"unit" = "Unité";
"calorie_calculations_required" = "Requis pour des calculs précis de calories";
"invalid_weight" = "Poids invalide";
"please_enter_valid_weight" = "Merci d'entrer un poids valide entre %d et %d %@";

// GenderSettingView
"gender" = "Genre";
"gender_footer" = "Optionnel - Utilisé pour des calculs plus précis de calories";

// AgeSettingView
"age" = "Âge";
"birth_date" = "Date de naissance";
"years_old" = "ans";
"invalid_age" = "Âge invalide";
"age_requirement" = "Tu dois avoir au moins 13 ans pour utiliser cette application";

// HeightSettingView
"height" = "Taille";
"height_cm" = "Taille (cm)";
"feet" = "Pieds";
"inches" = "Pouces";
"bmi_calculations" = "Optionnel - Utilisé pour les calculs d'IMC";
"invalid_height" = "Taille invalide";
"please_enter_valid_height" = "Merci d'entrer une taille valide.";

// SettingsView
"settings" = "Réglages";
"profile" = "Profil";
"not_set" = "Non défini";
"age" = "Âge";
"years" = "ans";
"audio" = "Audio";
"preferences" = "Préférences";
"metronome" = "Métronome";
"bpm" = "BPM";
"units" = "Unités";
"metric" = "Métrique";
"imperial" = "Impérial";
"theme" = "Thème";
"language" = "Langue";
"data" = "Données";
"all_activities" = "Tous les entraînements";
"export_activities" = "Exporter les données d'entraînement";
"exporting" = "Exportation...";
"error" = "Erreur";
"ok" = "OK";
"unknown_error" = "Une erreur inconnue s'est produite";
"system_default" = "Par défaut système";
"light" = "Clair";
"dark" = "Sombre";
"male" = "Homme";
"female" = "Femme";
"other" = "Autre";
"prefer_not_to_say" = "Préfère ne pas dire";

// UnitsSettingView
"unit_system" = "Système d'unités";

// SportTypeSelector
"run" = "Course";
"walk" = "Marche";
"hike" = "Randonnée";
"bike" = "Vélo";

// ProfileHeaderView
"your_name" = "Ton nom";
"email_example" = "<EMAIL>";
"change" = "Modifier";
"delete_photo" = "Supprimer la photo ?";
"delete" = "Supprimer";

// ActivitySummaryComponents
"total" = "Total";
"avg" = "Moyenne";
"per_day" = "par jour";
"per_month" = "par mois";

// PeriodIndicatorView
"current_week" = "Semaine actuelle";
"week_ago_format" = "Il y a %d semaine (%d)";
"weeks_ago_format" = "Il y a %d semaines (%d)";
"current_month" = "Mois actuel";
"month_ago_format" = "Il y a %d mois (%d)";
"months_ago_format" = "Il y a %d mois (%d)";
"current_year" = "Année actuelle";
"year_ago_format" = "Il y a %d an (%d)";
"years_ago_format" = "Il y a %d ans (%d)";

// TimePeriodPicker
"time_period" = "Période";
"seven_days" = "1 semaine";
"one_month" = "1 mois";
"one_year" = "1 an";
"all_time" = "Tout";

// For timeOfDay values
"morning" = "Entraînement matinal";
"afternoon" = "Entraînement après-midi";
"evening" = "Course du soir";
"night" = "Entraînement nocturne";

"every_beat" = "Chaque battement";
"every_other_beat" = "Tous les 2 battements";
"every_4th_beat" = "Tous les 4 battements";
"every_6th_beat" = "Tous les 6 battements";

// AudioAlertSettingsView
"audio_prompts" = "Annonces vocales";
"enable_audio_prompts" = "Activer les annonces vocales";
"audio_prompts_footer" = "Reçois des annonces vocales pendant tes entraînements";
"distance_alerts" = "Alertes distance";
"distance_alerts_footer" = "Annonce chaque étape de distance";
"time_alerts" = "Alertes temps";
"time_alerts_footer" = "Annonce chaque intervalle de temps";
"calorie_alerts" = "Alertes calories";
"calorie_alerts_footer" = "Annonce les étapes de calories brûlées";
"pace_alerts" = "Alertes allure";
"pace_alerts_footer" = "Annonce ton allure actuelle à intervalles";
"custom" = "Personnalisé";
"value" = "Valeur";
"min" = "min";
"cal" = "cal";
"enabled" = "Activé";
"disabled" = "Désactivé";
"hour" = "heure";
"h" = "h";
"m" = "m";
"distance_short" = "Distance";
"time_short" = "Temps";
"calorie_short" = "Calories";
"pace_short" = "Allure";
"no_alerts_configured" = "Aucune alerte";
"all_alerts" = "Toutes activées";
"enter_custom_value" = "Entrer une valeur personnalisée";
"integers_only" = "Nombres entiers seulement";
"invalid_input" = "Entrée invalide";
"please_enter_positive_number" = "Merci d'entrer un nombre positif";
"please_enter_whole_number" = "Merci d'entrer un nombre entier (sans décimales)";
"please_enter_value_between" = "Merci d'entrer une valeur entre %@ et %@";
"invalid" = "Invalide";

"per_day" = "par jour";
"per_month" = "par mois";

// MARK: - Welcome View
"welcome.title" = "Bienvenue dans RunApp";
"welcome.subtitle" = "Équipement prêt, chaque pas compte !";
"welcome.button.start" = "Commencer l'entraînement";

// MARK: - Basic Info View
"basicInfo.header.title" = "Personnalise ton expérience";
"basicInfo.header.subtitle" = "pour un calcul précis des calories";

// MARK: - Gender Selection
"basicInfo.gender.title" = "Quel est ton genre ?";
"basicInfo.gender.male" = "Homme";
"basicInfo.gender.female" = "Femme";
"basicInfo.gender.preferNotToSay" = "Préfère ne pas dire";
"basicInfo.button.skip" = "Passer";

// MARK: - Weight Input
"basicInfo.weight.title" = "Quel est ton poids ?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lbs";

// MARK: - Height Input
"basicInfo.height.title" = "Quelle est ta taille ?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "FT";
"basicInfo.height.placeholder.in" = "IN";

"basicInfo.button.continue" = "Continuer";

// MARK: - All Set View
"allSet.title" = "Tout est prêt !";
"allSet.subtitle.first" = "Il est temps de transpirer";
"allSet.subtitle.second" = "Pas d'excuses, que de l'action !";
"allSet.button.go" = "Fonce !";

// MARK: - ContentView Alerts
"alert.location.title" = "Autorisation de localisation requise";
"alert.location.message" = "Active 'Toujours autoriser' la localisation pour suivre parfaitement tes parcours et garder tes données d'entraînement précises. Merci de mettre à jour tes réglages.";
"alert.location.settings" = "Réglages";
"alert.location.later" = "Plus tard";

"alert.complete.title" = "Terminer l'entraînement ?";
"alert.complete.message" = "Cela va terminer ton entraînement actuel.\nTu ne pourras pas reprendre après avoir terminé.";
"alert.complete.confirm" = "Terminer";
"alert.complete.cancel" = "Annuler";

"alert.shortWorkout.title" = "Confirmer l'entraînement court";
"alert.shortWorkout.message" = "Ton entraînement dure moins de 30 secondes. Es-tu sûr de vouloir le sauvegarder ?";
"alert.shortWorkout.save" = "Sauvegarder";
"alert.shortWorkout.discard" = "Abandonner";

// MARK: - Map Markers
"map.marker.start" = "Ligne de départ";
"map.marker.end" = "Ligne d'arrivée";
"map.location.unknown" = "Lieu inconnu";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "Métronome activé";
"metronome.status.off" = "Métronome désactivé";

"activity.summary.title" = "Résumé d'entraînement";

// GPS Status
"gps.searching" = "Recherche du signal GPS...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "Tu as débloqué l'entraînement illimité, transpire et brûle ta passion !";
"settings.subscription.status.exceeded" = "Essai gratuit terminé ! Abonne-toi maintenant pour continuer à brûler ta passion sportive !";
"settings.subscription.status.oneRemaining" = "Il te reste 1 entraînement gratuit — abonne-toi maintenant pour garder le rythme !";
"settings.subscription.status.remaining" = "Il te reste %d entraînements gratuits — abonne-toi maintenant pour continuer à brûler des calories !";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Abonnement";
"settings.subscription.unlimitedAccess" = "Entraînement illimité";
"settings.subscription.checkingStatus" = "Vérification...";
"settings.subscription.alert.alreadySubscribed.title" = "Déjà abonné";
"settings.subscription.alert.alreadySubscribed.message" = "Tu as déjà un accès illimité à l'entraînement.";
"settings.subscription.alert.checkFailed.format" = "Échec de la vérification du statut d'abonnement : %@";

// MARK: - Custom Value Interface
"custom_interval" = "Intervalle personnalisé";
"interval_value" = "Valeur d'intervalle";
"enter_value_placeholder" = "Entrer la valeur";
"whole_numbers_only" = "Nombres entiers seulement";
"valid_range" = "Plage valide : %@ - %@";
"preset_options" = "Options rapides :";
"custom_value" = "Valeur personnalisée";

// MARK: - Audio Volume Settings
"audio_volume" = "Volume audio";
"audio_volume_footer" = "Ajuster le volume du métronome";
"volume" = "Volume";
"voice_volume" = "Volume vocal";
"audio_settings" = "Réglages audio";
"audio_settings_footer" = "Ajuster le volume et la vitesse des annonces vocales";
"test_voice" = "Test vocal";

// MARK: - Smart Voice Messages
"smart_voice" = "Voix Intelligente";
"smart_voice_footer" = "Active des annonces vocales naturelles et motivantes au lieu des annonces standard";

// Test messages
"audio.test.standard" = "Les annonces audio fonctionnent correctement";
"audio.test.smart" = "Parfait ! Ton audio est excellent, prêt à te motiver !";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "Bravo ! Tu as parcouru %@ %@";
"audio.distance.achievement.smart" = "Fantastique ! Tu as atteint le cap des %@ %@ ! Tu es formidable !";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "Tu es en mouvement depuis %@. Continue sur cette excellente lancée !";

// Calorie messages
"audio.calories.standard" = "%@ calories";
"audio.calories.smart" = "Tu as brûlé %@ calories ! Tes efforts portent leurs fruits !";
"audio.calories.achievement.smart" = "Magnifique ! %@ calories brûlées ! Tu es une machine à brûler les calories !";

// Pace messages
"audio.pace.standard" = "Allure actuelle : %@";
"audio.pace.smart" = "Tu maintiens une allure de %@. Tu as l'air très fort !";

// Unit strings for audio
"audio.unit.km.singular" = "kilomètre";
"audio.unit.km.plural" = "kilomètres";
"audio.unit.mile.singular" = "mile";
"audio.unit.mile.plural" = "miles";

// Time formatting for audio
"audio.time.minute.singular" = "1 minute";
"audio.time.minutes" = "%d minutes";
"audio.time.hour.singular" = "1 heure";
"audio.time.hours" = "%d heures";
"audio.time.hours.minutes" = "%d heures et %d minutes";

// Pace formatting for audio
"audio.pace.per.km" = "par kilomètre";
"audio.pace.per.mile" = "par mile";
"audio.pace.seconds" = "%d secondes %@";
"audio.pace.minutes" = "%d minutes %@";
"audio.pace.minutes.seconds" = "%d minutes et %d secondes %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Standard";
"voice.quality.enhanced" = "Améliorée";
"voice.quality.premium" = "Premium";
"voice.manager.test.text" = "Ceci est un test de la voix sélectionnée pour vos alertes de course";
"voice.test.sample" = "Ceci est un test de la voix sélectionnée pour vos alertes de course";
"voice.selection.title" = "Sélection de Voix";
"voice.current" = "Voix Actuelle";
"voice.preview" = "Aperçu";
"voice.none.available" = "Aucune voix disponible pour cette langue";
"voice.loading" = "Chargement des voix...";

// MARK: - Voice Selection UI
"voice.download.required" = "Téléchargement Requis";
"voice.loading.voices" = "Chargement des voix...";
"voice.empty.title" = "Aucune Voix Disponible";
"voice.empty.message" = "Aucune voix améliorée ou premium n'est disponible pour cette langue. Téléchargez des voix de haute qualité depuis les Réglages pour améliorer votre expérience.";
"voice.refresh.voices" = "Actualiser les Voix";
"voice.refresh.footer" = "Touchez pour actualiser la liste des voix disponibles";
"voice.current.language" = "Langue Actuelle";
"voice.selection.description" = "Sélectionnez votre voix préférée pour les alertes audio.\nLes options améliorées et premium offrent la plus haute qualité.";
"voice.selection.workout.description" = "Choisissez votre voix pour les invites audio d'entraînement. Les voix premium offrent l'expérience la plus claire et la plus motivante pendant les courses.";
"voice.selection.simple.description" = "Choisissez votre voix préférée pour les invites audio pendant vos entraînements.";
"voice.preview.error.title" = "Échec de l'Aperçu";
"voice.preview.error.message" = "Impossible d'écouter la voix '%@'. Veuillez réessayer.";
"voice.add.new" = "Ajouter Nouvelle Voix";
"voice.manage.description" = "Allez dans Réglages > Accessibilité > Contenu Parlé > Voix pour télécharger et gérer les voix";
"voice.system.default" = "Défaut du Système";

// MARK: - Premium Voice Benefits
"voice.premium.benefits.title" = "Voix Premium pour la Meilleure Expérience d'Entraînement";
"voice.premium.benefits.subtitle" = "Obtenez des invites audio cristallines qui percent le bruit ambiant et vous motivent pendant les entraînements intenses";
"voice.premium.benefits.recommendation" = "Recommandons d'ajouter de nouvelles voix premium pour la meilleure expérience";
"voice.premium.benefit.clarity" = "Clarté audio supérieure pour la course en extérieur";
"voice.premium.benefit.noise" = "Meilleures performances dans les environnements bruyants";
"voice.premium.benefit.motivation" = "Ton de voix plus naturel et motivant";
"voice.premium.recommended" = "RECOMMANDÉ";
"voice.premium.footer" = "Les voix premium fournissent l'audio le plus clair pendant les entraînements avec une meilleure résistance au bruit et des motifs de parole naturels.";
"voice.enhanced.footer" = "Les voix améliorées offrent une meilleure qualité que les voix système standard avec plus de clarté.";

// MARK: - Voice Quality Descriptions
"voice.quality.standard.description" = "Voix de qualité standard";
"voice.quality.enhanced.description" = "Clarté améliorée pour un meilleur audio";
"voice.quality.premium.description" = "Optimisée pour la motivation d'entraînement";

// MARK: - Voice Instructions
"voice.instructions.title" = "Télécharger de Nouvelles Voix";
"voice.instructions.message" = "Suivez ces étapes pour télécharger des voix de haute qualité :\n\n1. Touchez 'Ouvrir les Réglages' ci-dessous\n2. Naviguez vers : Accessibilité\n3. Sélectionnez : Contenu Parlé\n4. Choisissez : Voix\n5. Téléchargez vos voix préférées\n6. Revenez à RunApp et actualisez";
"voice.instructions.open.settings" = "Ouvrir les Réglages";
"voice.instructions.steps.title" = "Comment ajouter des voix :";
"voice.instructions.steps.detail" = "Réglages → Accessibilité → Contenu Parlé → Voix";
"voice.instructions.footer" = "Touchez le bouton ci-dessus pour voir les instructions étape par étape pour télécharger de nouvelles voix";
"voice.add.new.subtitle" = "Obtenir les instructions étape par étape";
"voice.management.footer" = "Téléchargez de nouvelles voix depuis les Réglages ou actualisez pour voir les voix nouvellement installées";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Sélection de Voix";
"voice.selection.footer" = "Choisissez votre voix préférée pour les annonces audio";

// Common buttons
"common.cancel" = "Annuler";
"common.close" = "Fermer";
"common.done" = "Terminé";
"common.ok" = "OK";

// MARK: - Voice Management
"voice.delete.success" = "Voix supprimée avec succès";
"voice.delete.failed" = "Échec de la suppression de la voix";
"voice.delete.confirm.title" = "Supprimer la voix ?";
"voice.delete.confirm.message" = "Cela supprimera '%@' de votre appareil. Vous pourrez la retélécharger depuis les paramètres plus tard.";
"voice.delete.confirm.delete" = "Supprimer";
"voice.delete.unavailable" = "Cette voix ne peut pas être supprimée car c'est la voix système par défaut.";
"voice.manage.voices" = "Gérer les voix";
"voice.download.status" = "Téléchargée";
"voice.builtin.status" = "Intégrée";

// MARK: - Smart Voice Prompt Variations (French)
// Distance Smart Variations - Français motivant
"audio.distance.smart.1" = "Incroyable ! Tu as déjà couru %@ %@ ! Tu es en feu !";
"audio.distance.smart.2" = "Fantastique ! %@ %@ dans la poche ! Continue comme ça !";
"audio.distance.smart.3" = "Magnifique ! %@ %@ de conquis ! Quelle machine !";
"audio.distance.smart.4" = "Extraordinaire ! %@ %@ accomplis ! Tu assures !";
"audio.distance.smart.5" = "Formidable ! %@ %@ réalisés ! Tu es un champion !";
"audio.distance.smart.6" = "Époustouflant ! %@ %@ maîtrisés ! Continue à fond !";
"audio.distance.smart.7" = "Génial ! %@ %@ dépassés ! Quelle performance !";
"audio.distance.smart.8" = "Superbe ! %@ %@ débloqués ! Tu es une bête !";
"audio.distance.smart.9" = "Remarquable ! %@ %@ explosés ! Pure puissance !";
"audio.distance.smart.10" = "Légendaire ! %@ %@ pulvérisés ! Tu es le boss !";

// Time Smart Variations - Français motivant
"audio.time.smart.1" = "Déjà %@ d'effort ! Quelle endurance !";
"audio.time.smart.2" = "Waouw ! %@ de pure énergie ! Continue à tout donner !";
"audio.time.smart.3" = "Fantastique ! %@ non-stop ! Tu es une machine !";
"audio.time.smart.4" = "Incroyable ! %@ de détermination ! Lâche rien !";
"audio.time.smart.5" = "Formidable ! %@ concentré ! Tu gères !";
"audio.time.smart.6" = "Époustouflant ! %@ d'effort ! Quel guerrier !";
"audio.time.smart.7" = "Génial ! %@ implacable ! Continue !";
"audio.time.smart.8" = "Superbe ! %@ à se battre ! Tu es en feu !";
"audio.time.smart.9" = "Remarquable ! %@ de passion ! Tu es le meilleur !";
"audio.time.smart.10" = "Légendaire ! %@ à tout déchirer ! Pure gloire !";

// Calories Smart Variations - Français motivant
"audio.calories.smart.1" = "Waouw ! Tu as brûlé %@ calories ! Tu es en feu !";
"audio.calories.smart.2" = "Fantastique ! %@ calories éliminées ! Continue à brûler !";
"audio.calories.smart.3" = "Incroyable ! %@ calories dominées ! Quelle machine !";
"audio.calories.smart.4" = "Formidable ! %@ calories conquises ! Allez !";
"audio.calories.smart.5" = "Époustouflant ! %@ calories démolies ! Continue !";
"audio.calories.smart.6" = "Génial ! %@ calories explosées ! Tu es un crack !";
"audio.calories.smart.7" = "Superbe ! %@ calories détruites ! Continue !";
"audio.calories.smart.8" = "Remarquable ! %@ calories vaporisées ! Implacable !";
"audio.calories.smart.9" = "Extraordinaire ! %@ calories anéanties ! Beast mode !";
"audio.calories.smart.10" = "Légendaire ! %@ calories pulvérisées ! Tu es le boss !";

// Pace Smart Variations - Français motivant
"audio.pace.smart.1" = "Parfait ! Rythme à %@ ! Quel contrôle !";
"audio.pace.smart.2" = "Waouw ! %@ de vitesse ! Pure puissance !";
"audio.pace.smart.3" = "Fantastique ! %@ maîtrisé ! Tu es une machine !";
"audio.pace.smart.4" = "Incroyable ! %@ dominé ! Quelle technique !";
"audio.pace.smart.5" = "Formidable ! %@ parfait ! Continue !";
"audio.pace.smart.6" = "Époustouflant ! %@ fluide ! Tu es un crack !";
"audio.pace.smart.7" = "Génial ! %@ professionnel ! Quel niveau !";
"audio.pace.smart.8" = "Superbe ! %@ implacable ! Tu es en feu !";
"audio.pace.smart.9" = "Remarquable ! %@ de rêve ! Beast mode !";
"audio.pace.smart.10" = "Légendaire ! %@ destructeur ! Tu es le boss !";

// MARK: - Workout Settings
"workout.settings.title" = "Paramètres d'Entraînement";
"workout.settings.notice" = "Entraînement en Cours";
"workout.settings.safe.only" = "Seuls les paramètres sûrs pour l'entraînement sont disponibles";

// MARK: - Speech Speed Settings
"speech_speed" = "Vitesse de Parole";
"speech_speed_footer" = "Contrôlez la vitesse de prononciation des instructions vocales. Des vitesses plus lentes aident à la compréhension pendant les entraînements intenses ou dans des environnements bruyants.";

// MARK: - Data Management
"data_management" = "Gestion des données";
"data_privacy" = "Confidentialité des données";
"export_data" = "Exporter les données";
"import_data" = "Importer les données";
"delete_data" = "Supprimer les données";

// Privacy Messages
"privacy_ownership" = "Vous conservez la propriété de vos données";
"privacy_icloud_storage" = "Stockées en sécurité dans votre iCloud";
"privacy_no_access" = "Nous (en tant que développeurs) ne pouvons pas accéder à vos données personnelles";
"privacy_full_control" = "Vous gardez le contrôle total de vos données";

// Export
"export_all_data" = "Exporter toutes les données";
"export_profile_only" = "Exporter le profil uniquement";
"export_activities_only" = "Exporter les activités uniquement";
"export_all_description" = "Profil et activités en 2 fichiers";
"export_profile_description" = "Paramètres personnels et préférences";
"export_activities_description" = "Toutes les données d'entraînement et statistiques";
"export_data_description" = "Exportez vos données en fichiers CSV pour sauvegarde ou transfert";

// Import
"import_data_files" = "Importer des fichiers de données";
"import_data_files_description" = "Sélectionnez des fichiers CSV exportés précédemment";
"import_data_description" = "Restaurer à partir de fichiers exportés précédemment";

// Delete
"delete_all_data" = "Supprimer toutes les données";
"delete_all_data_description" = "Supprimer définitivement toutes les données";
"delete_data_description" = "Cette action ne peut pas être annulée";
"delete_confirmation" = "Confirmer la suppression";
"delete_confirmation_message" = "Ceci supprimera définitivement toutes vos données de profil et activités. Cette action ne peut pas être annulée.";
"delete_permanently" = "Supprimer définitivement";

// Status Messages
"importing" = "Importation...";
"deleting" = "Suppression...";
"export_failed" = "Échec de l'exportation";
"import_failed" = "Échec de l'importation";
"delete_failed" = "Échec de la suppression";
"import_success" = "Importation réussie";
"import_success_message" = "Les données ont été importées avec succès";
"delete_success" = "Données supprimées";
"delete_success_message" = "Toutes vos données ont été supprimées définitivement. Vous allez maintenant retourner à l'écran principal.";
"import_error" = "Erreur d'importation";
"export_error" = "Erreur d'exportation";
"delete_error" = "Erreur de suppression";

"select_gender_title" = "Sélectionner le sexe";

// MARK: - Activity Stats View
"act_dist" = "Distance Act";
"act_time" = "Temps Act";
"act_cal" = "Cal Act";
"performance" = "Performance";
"best_pace" = "Meilleure Allure";
"avg_speed" = "Vitesse Moy";
"max_speed" = "Vitesse Max";
"time_analysis" = "Analyse Temps";
"total_time" = "Temps Total";
"paused_time" = "Temps Pause";
"active_calories" = "Actif";
"resting_calories" = "Repos";
"rate" = "Taux";
"kcal" = "kcal";
"cal_per_min" = "cal/min";
