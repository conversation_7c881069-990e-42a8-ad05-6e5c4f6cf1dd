// ActivityControls
"activities" = "Тренировки";
"metronome" = "Метроном";

// StatsView
"distance" = "Расстояние";
"active" = "Активность";
"time" = "Время";
"current" = "Текущий";
"pace" = "Темп";
"km" = "км";
"mi" = "ми";

// ActivityView
"activities" = "Тренировки";
"no_activities" = "Нет тренировок";
"complete_first_run" = "Завершите первую пробежку, чтобы увидеть её здесь";

// ActivityRowView
"distance" = "Расстояние";
"duration" = "Длительность";
"avg_pace" = "Средний темп";
"calories" = "Калории";
"start" = "Старт";
"end" = "Финиш";

// ActivityDetailView
"delete_activity" = "Удалить тренировку";
"are_you_sure_deletion" = "Вы уверены? Удаление нельзя отменить.";

// CountdownView
"go" = "РЫВОК!";

// LanguageSettingView
"language" = "Язык";
"done" = "Готово";

// AuthView
"runapp" = "RunApp";
"email" = "Email";
"sign_in_with_email" = "Войти через email";
"check_inbox_login_link" = "Проверьте почту для ссылки входа.";

// AnalysisView
"improve_with_personalized_data" = "Улучшайтесь с персонализированными данными";
"run" = "Бег";
"walk" = "Спортивная ходьба";
"hike" = "Трекинг";
"bike" = "Велоспорт";
"distance" = "Расстояние";
"duration" = "Длительность";
"calories" = "Калории";
"current_week" = "Текущая неделя";
"current_month" = "Текущий месяц";
"current_year" = "Текущий год";
"per_day" = "в день";
"per_month" = "в месяц";
"generate_test_data" = "Создать тестовые данные";

// MetronomeSettingsView
"metronome_settings" = "Настройки метронома";
"tempo" = "Темп";
"tempo_footer" = "Настройте темп от 40 до 240 ударов в минуту";
"enable_metronome" = "Включить метроном";
"sound" = "Звук";
"sound_type" = "Тип звука";
"vibration" = "Вибрация";
"vibration_strength" = "Сила вибрации";
"alert_frequency" = "Частота оповещений";
"largo" = "Ларго";
"adagio" = "Адажио";
"andante" = "Анданте";
"moderato" = "Модерато";
"allegro" = "Аллегро";
"presto" = "Престо";
"prestissimo" = "Престиссимо";
"default_beat" = "Стандартный ритм";
"beat_2" = "Ритм 2";
"beat_3" = "Ритм 3";
"beat_1" = "Ритм 1";
"beat_4" = "Ритм 4";
"beat_5" = "Ритм 5";
"beat_6" = "Ритм 6";
"feedback" = "Звук и вибрация";
"feedback_footer" = "Выберите различные типы звука при включённом звуке";

// NameSettingView
"name" = "Имя";
"enter_your_name" = "Введите ваше имя";
"cancel" = "Отмена";
"save" = "Сохранить";
"invalid_name" = "Неверное имя";
"please_enter_valid_name" = "Пожалуйста, введите корректное имя";

// WeightSettingView
"weight" = "Вес";
"unit" = "Единица";
"calorie_calculations_required" = "Необходимо для точного подсчёта калорий";
"invalid_weight" = "Неверный вес";
"please_enter_valid_weight" = "Пожалуйста, введите корректный вес от %d до %d %@";

// GenderSettingView
"gender" = "Пол";
"gender_footer" = "Опционально - Используется для точного подсчёта калорий";

// AgeSettingView
"age" = "Возраст";
"birth_date" = "Дата рождения";
"years_old" = "лет";
"invalid_age" = "Неверный возраст";
"age_requirement" = "Вам должно быть не менее 13 лет для использования этого приложения";

// HeightSettingView
"height" = "Рост";
"height_cm" = "Рост (см)";
"feet" = "Футы";
"inches" = "Дюймы";
"bmi_calculations" = "Опционально - Используется для расчёта ИМТ";
"invalid_height" = "Неверный рост";
"please_enter_valid_height" = "Пожалуйста, введите корректный рост.";

// SettingsView
"settings" = "Настройки";
"profile" = "Профиль";
"not_set" = "Не задано";
"age" = "Возраст";
"years" = "лет";
"audio" = "Аудио";
"preferences" = "Предпочтения";
"metronome" = "Метроном";
"bpm" = "уд/мин";
"units" = "Единицы";
"metric" = "Метрические";
"imperial" = "Имперские";
"theme" = "Тема";
"language" = "Язык";
"data" = "Данные";
"all_activities" = "Все тренировки";
"export_activities" = "Экспорт тренировок";
"exporting" = "Экспорт...";
"error" = "Ошибка";
"ok" = "OK";
"unknown_error" = "Произошла неизвестная ошибка";
"system_default" = "Системная";
"light" = "Светлая";
"dark" = "Тёмная";
"male" = "Мужской";
"female" = "Женский";
"other" = "Другой";
"prefer_not_to_say" = "Не указывать";

// UnitsSettingView
"unit_system" = "Система единиц";

// SportTypeSelector
"run" = "Бег";
"walk" = "Спортивная ходьба";
"hike" = "Трекинг";
"bike" = "Велоспорт";

// ProfileHeaderView
"your_name" = "Ваше имя";
"email_example" = "<EMAIL>";
"change" = "Изменить";
"delete_photo" = "Удалить фото?";
"delete" = "Удалить";

// ActivitySummaryComponents
"total" = "Всего";
"avg" = "В среднем";
"per_day" = "в день";
"per_month" = "в месяц";

// PeriodIndicatorView
"current_week" = "Текущая неделя";
"week_ago_format" = "%d неделю назад (%d)";
"weeks_ago_format" = "%d недель назад (%d)";
"current_month" = "Текущий месяц";
"month_ago_format" = "%d месяц назад (%d)";
"months_ago_format" = "%d месяцев назад (%d)";
"current_year" = "Текущий год";
"year_ago_format" = "%d год назад (%d)";
"years_ago_format" = "%d лет назад (%d)";

// TimePeriodPicker
"time_period" = "Период времени";
"seven_days" = "1 неделя";
"one_month" = "1 месяц";
"one_year" = "1 год";
"all_time" = "Всё время";

// For timeOfDay values
"morning" = "Утренняя тренировка";
"afternoon" = "Дневная тренировка";
"evening" = "Вечерний забег";
"night" = "Ночная тренировка";

"every_beat" = "Каждый удар";
"every_other_beat" = "Каждый второй удар";
"every_4th_beat" = "Каждый 4-й удар";
"every_6th_beat" = "Каждый 6-й удар";

"per_day" = "в день";
"per_month" = "в месяц";

// MARK: - Welcome View
"welcome.title" = "Добро пожаловать в RunApp";
"welcome.subtitle" = "Приготовьтесь, каждый шаг важен!";
"welcome.button.start" = "Начать";

// MARK: - Basic Info View
"basicInfo.header.title" = "Персонализируйте опыт";
"basicInfo.header.subtitle" = "для подсчёта калорий";

// MARK: - Gender Selection
"basicInfo.gender.title" = "Какой ваш пол?";
"basicInfo.gender.male" = "Мужской";
"basicInfo.gender.female" = "Женский";
"basicInfo.gender.preferNotToSay" = "Не указывать";
"basicInfo.button.skip" = "Пропустить";

// MARK: - Weight Input
"basicInfo.weight.title" = "Какой ваш вес?";
"basicInfo.weight.unit.kg" = "кг";
"basicInfo.weight.unit.lbs" = "фнт";

// MARK: - Height Input
"basicInfo.height.title" = "Какой ваш рост?";
"basicInfo.height.unit.cm" = "см";
"basicInfo.height.unit.ftIn" = "фт";
"basicInfo.height.placeholder.cm" = "СМ";
"basicInfo.height.placeholder.ft" = "ФУТ";
"basicInfo.height.placeholder.in" = "ДЮЙМ";

"basicInfo.button.continue" = "Продолжить";

// MARK: - All Set View
"allSet.title" = "Всё готово!";
"allSet.subtitle.first" = "Пора выходить на дорогу";
"allSet.subtitle.second" = "Никаких оправданий, только действие.";
"allSet.button.go" = "Вперёд!";

// MARK: - ContentView Alerts
"alert.location.title" = "Требуется разрешение на геолокацию";
"alert.location.message" = "Включите разрешение 'Всегда разрешать' для геолокации, чтобы точно отслеживать маршруты и сохранять ваш прогресс. Пожалуйста, обновите настройки.";
"alert.location.settings" = "Настройки";
"alert.location.later" = "Позже";

"alert.complete.title" = "Завершить тренировку?";
"alert.complete.message" = "Это закончит вашу текущую тренировку.\nВы не сможете возобновить её после завершения.";
"alert.complete.confirm" = "Завершить";
"alert.complete.cancel" = "Отмена";

"alert.shortWorkout.title" = "Подтвердить короткую тренировку";
"alert.shortWorkout.message" = "Ваша тренировка меньше 30 секунд. Вы уверены, что хотите сохранить её?";
"alert.shortWorkout.save" = "Сохранить";
"alert.shortWorkout.discard" = "Отменить";

// MARK: - Map
"map.marker.start" = "Старт";
"map.marker.end" = "Финиш";
"map.location.unknown" = "Неизвестное место";

// MARK: - Metronome Status
"metronome.status.on" = "Метроном включён";
"metronome.status.off" = "Метроном выключен";

"activity.summary.title" = "Сводка тренировки";

// GPS Status
"gps.searching" = "Поиск GPS...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "У тебя безлимитный доступ — вперёд к новым рекордам!";
"settings.subscription.status.exceeded" = "Лимит бесплатных тренировок исчерпан. Подпишись, чтобы продолжать без ограничений!";
"settings.subscription.status.oneRemaining" = "Осталась 1 бесплатная тренировка — подпишись и не останавливайся!";
"settings.subscription.status.remaining" = "Осталось %d бесплатных тренировок — подпишись и держи темп!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Подписка";
"settings.subscription.unlimitedAccess" = "Безлимитный доступ";
"settings.subscription.checkingStatus" = "Проверка...";
"settings.subscription.alert.alreadySubscribed.title" = "Уже подписан";
"settings.subscription.alert.alreadySubscribed.message" = "У тебя уже есть безлимитный доступ.";
"settings.subscription.alert.checkFailed.format" = "Не удалось проверить статус подписки: %@";

// AudioAlertSettingsView
"audio_prompts" = "Голосовые подсказки";
"enable_audio_prompts" = "Включить голосовые подсказки";
"audio_prompts_footer" = "Получать голосовые объявления во время тренировок";
"distance_alerts" = "Уведомления о дистанции";
"distance_alerts_footer" = "Объявлять каждую веху дистанции";
"time_alerts" = "Уведомления о времени";
"time_alerts_footer" = "Объявлять каждый временной интервал";
"calorie_alerts" = "Уведомления о калориях";
"calorie_alerts_footer" = "Объявлять вехи калорий";
"pace_alerts" = "Уведомления о темпе";
"pace_alerts_footer" = "Объявлять текущий темп через интервалы";
"custom" = "Пользовательский";
"value" = "Значение";
"min" = "мин";
"cal" = "кал";
"enabled" = "Включено";
"disabled" = "Отключено";
"hour" = "час";
"h" = "ч";
"m" = "м";
"distance_short" = "Расстояние";
"time_short" = "Время";
"calorie_short" = "Калории";
"pace_short" = "Темп";
"no_alerts_configured" = "Не настроено";
"all_alerts" = "Все включены";
"enter_custom_value" = "Введите пользовательское значение";
"integers_only" = "Только целые числа";
"invalid_input" = "Неверный ввод";
"please_enter_positive_number" = "Пожалуйста, введите положительное число";
"please_enter_whole_number" = "Пожалуйста, введите целое число (без десятичных)";
"please_enter_value_between" = "Пожалуйста, введите значение между %@ и %@";
"invalid" = "Неверно";

"per_day" = "в день";

// MARK: - Custom Value Interface
"custom_interval" = "Пользовательский интервал";
"interval_value" = "Значение интервала";
"enter_value_placeholder" = "Введите значение";
"whole_numbers_only" = "Только целые числа";
"valid_range" = "Допустимый диапазон: %@ - %@";
"preset_options" = "Быстрые варианты:";
"custom_value" = "Пользовательское значение";

// MARK: - Audio Volume Settings
"audio_volume" = "Громкость аудио";
"audio_volume_footer" = "Настроить громкость метронома";
"volume" = "Громкость";
"voice_volume" = "Громкость голоса";
"audio_settings" = "Настройки аудио";
"audio_settings_footer" = "Настроить громкость и скорость голосовых объявлений";
"test_voice" = "Тест голоса";

// MARK: - Smart Voice Messages
"smart_voice" = "Умный голос";
"smart_voice_footer" = "Включите естественные и мотивирующие голосовые объявления вместо стандартных";

// Test messages
"audio.test.standard" = "Аудио объявления работают корректно";
"audio.test.smart" = "Превосходно! Ваше аудио отличное, готово мотивировать вас!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "Отлично! Вы преодолели %@ %@";
"audio.distance.achievement.smart" = "Впечатляюще! Вы достигли отметки %@ %@! Вас не остановить!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "Вы в движении уже %@. Продолжайте в том же превосходном темпе!";

// Calorie messages
"audio.calories.standard" = "%@ калорий";
"audio.calories.smart" = "Вы сожгли %@ калорий! Ваши усилия приносят результат!";
"audio.calories.achievement.smart" = "Потрясающе! %@ калорий сожжено! Вы — машина по сжиганию калорий!";

// Pace messages
"audio.pace.standard" = "Текущий темп: %@";
"audio.pace.smart" = "Вы держите темп %@. Выглядите очень сильными!";

// Unit strings for audio
"audio.unit.km.singular" = "километр";
"audio.unit.km.plural" = "километров";
"audio.unit.mile.singular" = "миля";
"audio.unit.mile.plural" = "миль";

// Time formatting for audio
"audio.time.minute.singular" = "1 минута";
"audio.time.minutes" = "%d минут";
"audio.time.hour.singular" = "1 час";
"audio.time.hours" = "%d часов";
"audio.time.hours.minutes" = "%d часов %d минут";

// Pace formatting for audio
"audio.pace.per.km" = "на километр";
"audio.pace.per.mile" = "на милю";
"audio.pace.seconds" = "%d секунд %@";
"audio.pace.minutes" = "%d минут %@";
"audio.pace.minutes.seconds" = "%d минут %d секунд %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Стандартный";
"voice.quality.enhanced" = "Улучшенный";
"voice.quality.premium" = "Премиум";
"voice.manager.test.text" = "Это тест выбранного голоса для ваших уведомлений о беге";
"voice.test.sample" = "Это тест выбранного голоса для ваших уведомлений о беге";
"voice.selection.title" = "Выбор Голоса";
"voice.current" = "Текущий Голос";
"voice.preview" = "Предпросмотр";
"voice.none.available" = "Нет доступных голосов для этого языка";
"voice.loading" = "Загрузка голосов...";

// MARK: - Voice Selection UI
"voice.download.required" = "Требуется Загрузка";
"voice.loading.voices" = "Загрузка голосов...";
"voice.empty.title" = "Нет Доступных Голосов";
"voice.empty.message" = "Для этого языка нет улучшенных или премиум голосов. Загрузите высококачественные голоса из Настроек для улучшения опыта.";
"voice.refresh.voices" = "Обновить Голоса";
"voice.refresh.footer" = "Нажмите для обновления списка доступных голосов";
"voice.current.language" = "Текущий Язык";
"voice.selection.description" = "Выберите предпочитаемый голос для аудио уведомлений.\nУлучшенные и премиум варианты предлагают наивысшее качество.";
"voice.preview.error.title" = "Ошибка Предпросмотра";
"voice.preview.error.message" = "Не удалось воспроизвести голос '%@'. Попробуйте снова.";
"voice.add.new" = "Добавить Новый Голос";
"voice.manage.description" = "Перейдите в Настройки > Универсальный доступ > Произносимое содержимое > Голоса для загрузки и управления голосами";
"voice.system.default" = "Системный По Умолчанию";

// MARK: - Voice Instructions
"voice.instructions.title" = "Загрузить Новые Голоса";
"voice.instructions.message" = "Следуйте этим шагам для загрузки высококачественных голосов:\n\n1. Нажмите 'Открыть Настройки' ниже\n2. Перейдите к: Универсальный доступ\n3. Выберите: Произносимое содержимое\n4. Выберите: Голоса\n5. Загрузите предпочитаемые голоса\n6. Вернитесь в RunApp и обновите";
"voice.instructions.open.settings" = "Открыть Настройки";
"voice.instructions.steps.title" = "Как добавить голоса:";
"voice.instructions.steps.detail" = "Настройки → Универсальный доступ → Произносимое содержимое → Голоса";
"voice.instructions.footer" = "Нажмите кнопку выше для просмотра пошаговых инструкций по загрузке новых голосов";
"voice.add.new.subtitle" = "Получить пошаговые инструкции";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Выбор Голоса";
"voice.selection.footer" = "Выберите предпочитаемый голос для аудио объявлений";

// Common buttons
"common.cancel" = "Отмена";
"common.close" = "Закрыть";
"common.done" = "Готово";
"common.ok" = "ОК";

// MARK: - Voice Management
"voice.delete.success" = "Голос успешно удален";
"voice.delete.failed" = "Не удалось удалить голос";
"voice.delete.confirm.title" = "Удалить голос?";
"voice.delete.confirm.message" = "Это удалит '%@' с вашего устройства. Вы сможете загрузить его снова позже из Настроек.";
"voice.delete.confirm.delete" = "Удалить";
"voice.delete.unavailable" = "Этот голос нельзя удалить, так как это системный голос по умолчанию.";
"voice.manage.voices" = "Управление голосами";
"voice.download.status" = "Загружен";
"voice.builtin.status" = "Встроенный";

// MARK: - Smart Voice Prompt Variations (Russian)
// Distance Smart Variations - Русский мотивационный
"audio.distance.smart.1" = "Невероятно! Вы уже пробежали %@ %@! Вы крутой!";
"audio.distance.smart.2" = "Класс! %@ %@ покорены! Продолжайте так, чемпион!";
"audio.distance.smart.3" = "Мощно! %@ %@ в кармане! Какая машина!";
"audio.distance.smart.4" = "Потрясающе! %@ %@ завершены! Давайте дальше!";
"audio.distance.smart.5" = "Феноменально! %@ %@ достигнуты! Вы ас!";
"audio.distance.smart.6" = "Зверски! %@ %@ подчинены! Не останавливайтесь!";
"audio.distance.smart.7" = "Гениально! %@ %@ превзойдены! Какая мощь!";
"audio.distance.smart.8" = "Великолепно! %@ %@ разблокированы! Вы огонь!";
"audio.distance.smart.9" = "Восхитительно! %@ %@ сметены! Чистая сила!";
"audio.distance.smart.10" = "Легендарно! %@ %@ уничтожены! Вы король!";

// Time Smart Variations - Русский мотивационный
"audio.time.smart.1" = "Уже %@ в беге! Какая выносливость!";
"audio.time.smart.2" = "Вау! %@ чистой энергии! Продолжайте давить!";
"audio.time.smart.3" = "Мощно! %@ без остановки! Вы машина!";
"audio.time.smart.4" = "Невероятно! %@ решимости! Давайте сильнее!";
"audio.time.smart.5" = "Феноменально! %@ сосредоточенности! Вперёд!";
"audio.time.smart.6" = "Зверски! %@ усилий! Какой воин!";
"audio.time.smart.7" = "Гениально! %@ неудержимости! Продолжайте!";
"audio.time.smart.8" = "Великолепно! %@ борьбы! Вы огонь!";
"audio.time.smart.9" = "Восхитительно! %@ страсти! Вы лучший!";
"audio.time.smart.10" = "Легендарно! %@ разрушения! Чистая слава!";

// Calories Smart Variations - Русский мотивационный
"audio.calories.smart.1" = "Вау! Вы сожгли %@ калорий! Вы огонь!";
"audio.calories.smart.2" = "Мощно! %@ калорий уничтожены! Продолжайте жечь!";
"audio.calories.smart.3" = "Невероятно! %@ калорий подчинены! Какая машина!";
"audio.calories.smart.4" = "Феноменально! %@ калорий покорены! Давайте!";
"audio.calories.smart.5" = "Зверски! %@ калорий снесены! Не останавливайтесь!";
"audio.calories.smart.6" = "Гениально! %@ калорий сметены! Вы ас!";
"audio.calories.smart.7" = "Великолепно! %@ калорий разрушены! Продолжайте!";
"audio.calories.smart.8" = "Восхитительно! %@ калорий испарены! Неудержимый!";
"audio.calories.smart.9" = "Потрясающе! %@ калорий аннигилированы! Режим зверя!";
"audio.calories.smart.10" = "Легендарно! %@ калорий пульверизированы! Вы король!";

// Pace Smart Variations - Русский мотивационный
"audio.pace.smart.1" = "Идеально! Темп %@! Какой контроль!";
"audio.pace.smart.2" = "Вау! %@ скорости! Чистая мощь!";
"audio.pace.smart.3" = "Мощно! %@ под контролем! Вы машина!";
"audio.pace.smart.4" = "Невероятно! %@ подчинён! Какая техника!";
"audio.pace.smart.5" = "Феноменально! %@ идеален! Продолжайте так!";
"audio.pace.smart.6" = "Зверски! %@ плавный! Вы ас!";
"audio.pace.smart.7" = "Гениально! %@ профессиональный! Какой уровень!";
"audio.pace.smart.8" = "Великолепно! %@ неудержимый! Вы огонь!";
"audio.pace.smart.9" = "Восхитительно! %@ мечты! Режим зверя!";
"audio.pace.smart.10" = "Легендарно! %@ разрушительный! Вы король!";

// MARK: - Workout Settings
"workout.settings.title" = "Настройки Тренировки";
"workout.settings.notice" = "Тренировка в Процессе";
"workout.settings.safe.only" = "Доступны только безопасные настройки для тренировки";

// MARK: - Voice Premium Features
"voice.selection.simple.description" = "Выберите предпочитаемый голос для звуковых подсказок во время пробежек.";
"voice.premium.benefits.recommendation" = "Рекомендуем добавить ПРЕМИУМ голоса для лучшего опыта";
"voice.premium.benefit.clarity" = "Превосходная четкость звука для уличных тренировок";
"voice.premium.benefit.noise" = "Лучшая производительность в шумной среде";
"voice.premium.benefit.motivation" = "Более естественный, мотивирующий тон голоса";
"voice.management.footer" = "Загрузите новые голоса из настроек iOS и обновите, чтобы увидеть вновь установленные голоса";
"voice.selection.workout.description" = "Выберите голос для звуковых подсказок тренировки. Премиум голоса обеспечивают самый четкий и мотивирующий опыт во время бега.";

// MARK: - Speech Speed Settings
"speech_speed" = "Скорость речи";
"speech_speed_footer" = "Контролируйте скорость произнесения голосовых подсказок. Медленные скорости помогают понимать во время интенсивных тренировок или в шумной среде.";

// MARK: - Data Management
"data_management" = "Управление данными";
"data_privacy" = "Конфиденциальность данных";
"export_data" = "Экспорт данных";
"import_data" = "Импорт данных";
"delete_data" = "Удалить данные";

// Privacy Messages
"privacy_ownership" = "Вы сохраняете право собственности на свои данные";
"privacy_icloud_storage" = "Безопасно хранятся в вашем iCloud";
"privacy_no_access" = "Мы (как разработчики) не можем получить доступ к вашим личным данным";
"privacy_full_control" = "Вы сохраняете полный контроль над своими данными";

// Export
"export_all_data" = "Экспортировать все данные";
"export_profile_only" = "Экспортировать только профиль";
"export_activities_only" = "Экспортировать только активности";
"export_all_description" = "Профиль и активности в 2 файлах";
"export_profile_description" = "Личные настройки и предпочтения";
"export_activities_description" = "Все данные тренировок и статистика";
"export_data_description" = "Экспортируйте свои данные как CSV-файлы для резервного копирования или переноса";

// Import
"import_data_files" = "Импортировать файлы данных";
"import_data_files_description" = "Выберите ранее экспортированные CSV-файлы";
"import_data_description" = "Восстановить из ранее экспортированных файлов";

// Delete
"delete_all_data" = "Удалить все данные";
"delete_all_data_description" = "Навсегда удалить все данные";
"delete_data_description" = "Это действие нельзя отменить";
"delete_confirmation" = "Подтвердить удаление";
"delete_confirmation_message" = "Это навсегда удалит все ваши данные профиля и активности. Это действие нельзя отменить.";
"delete_permanently" = "Удалить навсегда";

// Status Messages
"importing" = "Импорт...";
"deleting" = "Удаление...";
"export_failed" = "Экспорт не удался";
"import_failed" = "Импорт не удался";
"delete_failed" = "Удаление не удалось";
"import_success" = "Импорт успешен";
"import_success_message" = "Данные были успешно импортированы";
"delete_success" = "Данные удалены";
"delete_success_message" = "Все ваши данные были навсегда удалены. Теперь вы вернетесь к главному экрану.";
"import_error" = "Ошибка импорта";
"export_error" = "Ошибка экспорта";
"delete_error" = "Ошибка удаления";

"select_gender_title" = "Выберите пол";

// MARK: - Activity Stats View
"act_dist" = "Дист Тренировки";
"act_time" = "Время Тренировки";
"act_cal" = "Кал Тренировки";
"performance" = "Производительность";
"best_pace" = "Лучший Темп";
"avg_speed" = "Средняя Скорость";
"max_speed" = "Макс Скорость";
"time_analysis" = "Анализ Времени";
"total_time" = "Общее Время";
"paused_time" = "Время Паузы";
"active_calories" = "Активные";
"resting_calories" = "Покой";
"rate" = "Скорость";
"kcal" = "ккал";
"cal_per_min" = "кал/мин";
