// ActivityControls
"activities" = "Latihan";
"metronome" = "Metronom";

// StatsView
"distance" = "Jarak";
"active" = "Aktif";
"time" = "Waktu";
"current" = "Saat Ini";
"pace" = "Tempo";
"km" = "km";
"mi" = "mi";

// ActivityView
"activities" = "Latihan";
"no_activities" = "Tidak Ada Latihan";
"complete_first_run" = "Selesaikan lari pertama Anda untuk melihatnya di sini";

// ActivityRowView
"distance" = "Jarak";
"duration" = "Durasi";
"avg_pace" = "Tempo Rata-rata";
"calories" = "Kalori";
"start" = "Start";
"end" = "Finish";

// ActivityDetailView
"delete_activity" = "Hapus Latihan";
"are_you_sure_deletion" = "Apakah Anda yakin? Penghapusan tidak dapat dibatalkan.";

// CountdownView
"go" = "PACU!";

// LanguageSettingView
"language" = "Bahasa";
"done" = "Selesai";

// AuthView
"runapp" = "RunApp";
"email" = "Email";
"sign_in_with_email" = "Masuk dengan Email";
"check_inbox_login_link" = "Periksa kotak masuk Anda untuk tautan masuk.";

// AnalysisView
"improve_with_personalized_data" = "Tingkatkan dengan Data Personal";
"run" = "Lari";
"walk" = "Jalan Cepat";
"hike" = "Hiking";
"bike" = "Bersepeda";
"distance" = "Jarak";
"duration" = "Durasi";
"calories" = "Kalori";
"current_week" = "Minggu ini";
"current_month" = "Bulan ini";
"current_year" = "Tahun ini";
"per_day" = "per hari";
"per_month" = "per bulan";
"generate_test_data" = "Hasilkan Data Uji";

// MetronomeSettingsView
"metronome_settings" = "Metronom";
"tempo" = "Tempo ketukan";
"tempo_footer" = "Sesuaikan tempo antara 40-240 ketukan per menit";
"enable_metronome" = "Aktifkan Metronom";
"sound" = "Suara";
"sound_type" = "Jenis Suara";
"vibration" = "Getaran";
"vibration_strength" = "Kekuatan Getaran";
"alert_frequency" = "Frekuensi Peringatan";
"largo" = "Largo";
"adagio" = "Adagio";
"andante" = "Andante";
"moderato" = "Moderato";
"allegro" = "Allegro";
"presto" = "Presto";
"prestissimo" = "Prestissimo";
"default_beat" = "Ketukan Default";
"beat_2" = "Ketukan 2";
"beat_3" = "Ketukan 3";
"beat_1" = "Ketukan 1";
"beat_4" = "Ketukan 4";
"beat_5" = "Ketukan 5";
"beat_6" = "Ketukan 6";
"feedback" = "Suara dan Getaran";
"feedback_footer" = "Pilih dari berbagai jenis suara metronom ketika suara diaktifkan";

// NameSettingView
"name" = "Nama";
"enter_your_name" = "Masukkan Nama Anda";
"cancel" = "Batal";
"save" = "Simpan";
"invalid_name" = "Nama Tidak Valid";
"please_enter_valid_name" = "Silakan masukkan nama yang valid";

// WeightSettingView
"weight" = "Berat";
"unit" = "Satuan";
"calorie_calculations_required" = "Diperlukan untuk perhitungan kalori yang akurat";
"invalid_weight" = "Berat Tidak Valid";
"please_enter_valid_weight" = "Silakan masukkan berat yang valid antara %d dan %d %@";

// GenderSettingView
"gender" = "Jenis Kelamin";
"gender_footer" = "Opsional - Digunakan untuk perhitungan kalori yang lebih akurat";

// AgeSettingView
"age" = "Usia";
"birth_date" = "Tanggal Lahir";
"years_old" = "tahun";
"invalid_age" = "Usia Tidak Valid";
"age_requirement" = "Anda harus berusia minimal 13 tahun untuk menggunakan aplikasi ini";

// HeightSettingView
"height" = "Tinggi";
"height_cm" = "Tinggi (cm)";
"feet" = "Kaki";
"inches" = "Inci";
"bmi_calculations" = "Opsional - Digunakan untuk perhitungan BMI";
"invalid_height" = "Tinggi Tidak Valid";
"please_enter_valid_height" = "Silakan masukkan tinggi yang valid.";

// SettingsView
"settings" = "Pengaturan";
"profile" = "Profil";
"not_set" = "Belum diatur";
"age" = "Usia";
"years" = "tahun";
"audio" = "Audio";
"preferences" = "Preferensi";
"metronome" = "Metronom";
"bpm" = "bpm";
"units" = "Satuan";
"metric" = "Metrik";
"imperial" = "Imperial";
"theme" = "Tema";
"language" = "Bahasa";
"data" = "Data";
"all_activities" = "Semua Latihan";
"export_activities" = "Ekspor Latihan";
"exporting" = "Mengekspor...";
"error" = "Error";
"ok" = "OK";
"unknown_error" = "Terjadi kesalahan yang tidak diketahui";
"system_default" = "Default Sistem";
"light" = "Terang";
"dark" = "Gelap";
"male" = "Pria";
"female" = "Wanita";
"other" = "Lainnya";
"prefer_not_to_say" = "Lebih baik tidak menyebutkan";

// UnitsSettingView
"unit_system" = "Sistem Satuan";

// SportTypeSelector
"run" = "Lari";
"walk" = "Jalan Cepat";
"hike" = "Hiking";
"bike" = "Bersepeda";

// ProfileHeaderView
"your_name" = "Nama Anda";
"email_example" = "<EMAIL>";
"change" = "Ubah";
"delete_photo" = "Hapus Foto?";
"delete" = "Hapus";

// ActivitySummaryComponents
"total" = "Total";
"avg" = "Rata-rata";
"per_day" = "per hari";
"per_month" = "per bulan";

// PeriodIndicatorView
"current_week" = "Minggu ini";
"week_ago_format" = "%d minggu yang lalu (%d)";
"weeks_ago_format" = "%d minggu yang lalu (%d)";
"current_month" = "Bulan ini";
"month_ago_format" = "%d bulan yang lalu (%d)";
"months_ago_format" = "%d bulan yang lalu (%d)";
"current_year" = "Tahun ini";
"year_ago_format" = "%d tahun yang lalu (%d)";
"years_ago_format" = "%d tahun yang lalu (%d)";

// TimePeriodPicker
"time_period" = "Periode Waktu";
"seven_days" = "1 minggu";
"one_month" = "1 Bulan";
"one_year" = "1 Tahun";
"all_time" = "Sepanjang Waktu";

// For timeOfDay values
"morning" = "Latihan Pagi";
"afternoon" = "Latihan Siang";
"evening" = "Lari Sore";
"night" = "Latihan Malam";

"every_beat" = "Setiap Ketukan";
"every_other_beat" = "Setiap Ketukan Kedua";
"every_4th_beat" = "Setiap Ketukan ke-4";
"every_6th_beat" = "Setiap Ketukan ke-6";

"per_day" = "per hari";
"per_month" = "per bulan";

// MARK: - Welcome View
"welcome.title" = "Selamat datang di RunApp";
"welcome.subtitle" = "Bersiaplah, setiap langkah berarti!";
"welcome.button.start" = "Mulai";

// MARK: - Basic Info View
"basicInfo.header.title" = "Personalisasi pengalaman";
"basicInfo.header.subtitle" = "untuk estimasi Kalori";

// MARK: - Gender Selection
"basicInfo.gender.title" = "Apa jenis kelamin Anda?";
"basicInfo.gender.male" = "Pria";
"basicInfo.gender.female" = "Wanita";
"basicInfo.gender.preferNotToSay" = "Lebih Baik Tidak Menyebutkan";
"basicInfo.button.skip" = "Lewati";

// MARK: - Weight Input
"basicInfo.weight.title" = "Berapa berat Anda?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lbs";

// MARK: - Height Input
"basicInfo.height.title" = "Berapa tinggi Anda?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "FT";
"basicInfo.height.placeholder.in" = "IN";

"basicInfo.button.continue" = "Lanjutkan";

// MARK: - All Set View
"allSet.title" = "Anda Siap!";
"allSet.subtitle.first" = "Saatnya berlari";
"allSet.subtitle.second" = "Tidak ada alasan, langsung bertindak!";
"allSet.button.go" = "Mulai!";

// MARK: - ContentView Alerts
"alert.location.title" = "Izin Lokasi Diperlukan";
"alert.location.message" = "Aktifkan 'Selalu Izinkan' lokasi untuk melacak rute dengan lancar, dan menjaga akurasi kemajuan Anda. Silakan perbarui pengaturan Anda.";
"alert.location.settings" = "Pengaturan";
"alert.location.later" = "Nanti";

"alert.complete.title" = "Selesaikan Aktivitas?";
"alert.complete.message" = "Ini akan mengakhiri aktivitas Anda saat ini.\nAnda tidak dapat melanjutkan setelah menyelesaikan.";
"alert.complete.confirm" = "Selesai";
"alert.complete.cancel" = "Batal";

"alert.shortWorkout.title" = "Konfirmasi Latihan Singkat";
"alert.shortWorkout.message" = "Latihan Anda kurang dari 30 detik. Apakah Anda yakin ingin menyimpannya?";
"alert.shortWorkout.save" = "Simpan";
"alert.shortWorkout.discard" = "Buang";

// MARK: - Map Markers
"map.marker.start" = "Mulai";
"map.marker.end" = "Akhir";
"map.location.unknown" = "Lokasi Tidak Diketahui";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "Metronom Aktif";
"metronome.status.off" = "Metronom Nonaktif";

"activity.summary.title" = "Ringkasan aktivitas";

// GPS Status
"gps.searching" = "Mencari GPS...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "Akses tak terbatas aktif — latihan sepuasnya!";
"settings.subscription.status.exceeded" = "Batas uji coba gratis telah tercapai. Langganan sekarang untuk akses penuh!";
"settings.subscription.status.oneRemaining" = "Tersisa 1 aktivitas gratis — langganan sekarang untuk terus maju!";
"settings.subscription.status.remaining" = "Tersisa %d aktivitas gratis — buruan langganan dan lanjutkan semangatnya!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Langganan";
"settings.subscription.unlimitedAccess" = "Akses Tak Terbatas";
"settings.subscription.checkingStatus" = "Memeriksa...";
"settings.subscription.alert.alreadySubscribed.title" = "Sudah Berlangganan";
"settings.subscription.alert.alreadySubscribed.message" = "Kamu sudah memiliki akses tak terbatas.";
"settings.subscription.alert.checkFailed.format" = "Gagal memeriksa status langganan: %@";

// AudioAlertSettingsView
"audio_prompts" = "Peringatan Audio";
"enable_audio_prompts" = "Aktifkan Peringatan Audio";
"audio_prompts_footer" = "Terima pengumuman suara selama berlari";
"distance_alerts" = "Peringatan Jarak";
"distance_alerts_footer" = "Umumkan setiap pencapaian jarak";
"time_alerts" = "Peringatan Waktu";
"time_alerts_footer" = "Umumkan setiap interval waktu";
"calorie_alerts" = "Peringatan Kalori";
"calorie_alerts_footer" = "Umumkan pencapaian kalori";
"pace_alerts" = "Peringatan Tempo";
"pace_alerts_footer" = "Umumkan tempo saat ini pada interval";
"custom" = "Kustom";
"value" = "Nilai";
"min" = "mnt";
"cal" = "kal";
"enabled" = "Aktif";
"disabled" = "Nonaktif";
"hour" = "jam";
"h" = "j";
"m" = "m";
"distance_short" = "Jarak";
"time_short" = "Waktu";
"calorie_short" = "Kalori";
"pace_short" = "Tempo";
"no_alerts_configured" = "Tidak ada yang diatur";
"all_alerts" = "Semua aktif";
"enter_custom_value" = "Masukkan nilai kustom";
"integers_only" = "Hanya bilangan bulat";
"invalid_input" = "Input Tidak Valid";
"please_enter_positive_number" = "Silakan masukkan angka positif";
"please_enter_whole_number" = "Silakan masukkan bilangan bulat (tanpa desimal)";
"please_enter_value_between" = "Silakan masukkan nilai antara %@ dan %@";
"invalid" = "Tidak Valid";

"per_day" = "per hari";

// MARK: - Custom Value Interface
"custom_interval" = "Interval Kustom";
"interval_value" = "Nilai Interval";
"enter_value_placeholder" = "Masukkan nilai";
"whole_numbers_only" = "Hanya bilangan bulat";
"valid_range" = "Rentang valid: %@ - %@";
"preset_options" = "Opsi cepat:";
"custom_value" = "Nilai Kustom";

// MARK: - Audio Volume Settings
"audio_volume" = "Volume Audio";
"audio_volume_footer" = "Sesuaikan volume suara metronome";
"volume" = "Volume";
"voice_volume" = "Volume Suara";
"audio_settings" = "Pengaturan Audio";
"audio_settings_footer" = "Sesuaikan volume dan kecepatan pengumuman suara";
"test_voice" = "Uji Suara";

// MARK: - Smart Voice Messages
"smart_voice" = "Suara Pintar";
"smart_voice_footer" = "Aktifkan pengumuman suara yang alami dan memotivasi alih-alih pengumuman standar";

// Test messages
"audio.test.standard" = "Pengumuman audio berfungsi dengan baik";
"audio.test.smart" = "Keren! Audio Anda bagus sekali, siap memotivasi Anda!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "Bagus! Anda telah menyelesaikan %@ %@";
"audio.distance.achievement.smart" = "Luar biasa! Anda telah mencapai %@ %@! Anda tak terhentikan!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "Anda sudah bergerak selama %@. Pertahankan tempo yang fantastis ini!";

// Calorie messages
"audio.calories.standard" = "%@ kalori";
"audio.calories.smart" = "Anda telah membakar %@ kalori! Usaha Anda membuahkan hasil!";
"audio.calories.achievement.smart" = "Spektakuler! %@ kalori terbakar! Anda mesin pembakar kalori!";

// Pace messages
"audio.pace.standard" = "Tempo saat ini: %@";
"audio.pace.smart" = "Anda mempertahankan tempo %@. Anda terlihat sangat kuat!";

// Unit strings for audio
"audio.unit.km.singular" = "kilometer";
"audio.unit.km.plural" = "kilometer";
"audio.unit.mile.singular" = "mil";
"audio.unit.mile.plural" = "mil";

// Time formatting for audio
"audio.time.minute.singular" = "1 menit";
"audio.time.minutes" = "%d menit";
"audio.time.hour.singular" = "1 jam";
"audio.time.hours" = "%d jam";
"audio.time.hours.minutes" = "%d jam %d menit";

// Pace formatting for audio
"audio.pace.per.km" = "per kilometer";
"audio.pace.per.mile" = "per mil";
"audio.pace.seconds" = "%d detik %@";
"audio.pace.minutes" = "%d menit %@";
"audio.pace.minutes.seconds" = "%d menit %d detik %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Standar";
"voice.quality.enhanced" = "Ditingkatkan";
"voice.quality.premium" = "Premium";
"voice.manager.test.text" = "Ini adalah tes suara yang dipilih untuk peringatan lari Anda";
"voice.test.sample" = "Ini adalah tes suara yang dipilih untuk peringatan lari Anda";
"voice.selection.title" = "Pilihan Suara";
"voice.current" = "Suara Saat Ini";
"voice.preview" = "Pratinjau";
"voice.none.available" = "Tidak ada suara yang tersedia untuk bahasa ini";
"voice.loading" = "Memuat suara...";

// MARK: - Voice Selection UI
"voice.download.required" = "Unduhan Diperlukan";
"voice.loading.voices" = "Memuat suara...";
"voice.empty.title" = "Tidak Ada Suara Tersedia";
"voice.empty.message" = "Tidak ada suara yang ditingkatkan atau premium tersedia untuk bahasa ini. Unduh suara berkualitas tinggi dari Pengaturan untuk meningkatkan pengalaman Anda.";
"voice.refresh.voices" = "Segarkan Suara";
"voice.refresh.footer" = "Ketuk untuk menyegarkan daftar suara yang tersedia";
"voice.current.language" = "Bahasa Saat Ini";
"voice.selection.description" = "Pilih suara yang Anda sukai untuk peringatan audio.\nOpsi yang ditingkatkan dan premium menawarkan kualitas tertinggi.";
"voice.preview.error.title" = "Pratinjau Gagal";
"voice.preview.error.message" = "Tidak dapat mempratinjau suara '%@'. Silakan coba lagi.";
"voice.add.new" = "Tambah Suara Baru";
"voice.manage.description" = "Buka Pengaturan > Aksesibilitas > Konten Lisan > Suara untuk mengunduh dan mengelola suara";
"voice.system.default" = "Bawaan Sistem";

// MARK: - Voice Instructions
"voice.instructions.title" = "Unduh Suara Baru";
"voice.instructions.message" = "Ikuti langkah-langkah ini untuk mengunduh suara berkualitas tinggi:\n\n1. Ketuk 'Buka Pengaturan' di bawah\n2. Navigasi ke: Aksesibilitas\n3. Pilih: Konten Lisan\n4. Pilih: Suara\n5. Unduh suara yang Anda sukai\n6. Kembali ke RunApp dan segarkan";
"voice.instructions.open.settings" = "Buka Pengaturan";
"voice.instructions.steps.title" = "Cara menambah suara:";
"voice.instructions.steps.detail" = "Pengaturan → Aksesibilitas → Konten Lisan → Suara";
"voice.instructions.footer" = "Ketuk tombol di atas untuk melihat petunjuk langkah demi langkah mengunduh suara baru";
"voice.add.new.subtitle" = "Dapatkan petunjuk langkah demi langkah";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Pilihan Suara";
"voice.selection.footer" = "Pilih suara yang Anda sukai untuk pengumuman audio";

// Common buttons
"common.cancel" = "Batal";
"common.close" = "Tutup";
"common.done" = "Selesai";
"common.ok" = "OK";

// MARK: - Voice Management
"voice.delete.success" = "Suara berhasil dihapus";
"voice.delete.failed" = "Gagal menghapus suara";
"voice.delete.confirm.title" = "Hapus Suara?";
"voice.delete.confirm.message" = "Ini akan menghapus '%@' dari perangkat Anda. Anda dapat mengunduhnya lagi nanti dari Pengaturan.";
"voice.delete.confirm.delete" = "Hapus";
"voice.delete.unavailable" = "Suara ini tidak dapat dihapus karena merupakan suara bawaan sistem.";
"voice.manage.voices" = "Kelola Suara";
"voice.download.status" = "Telah Diunduh";
"voice.builtin.status" = "Bawaan";

// MARK: - Smart Voice Prompt Variations (Indonesian)
// Distance Smart Variations - Indonesia motivasional
"audio.distance.smart.1" = "Keren! Udah %@ %@ nih! Lo jagoan!";
"audio.distance.smart.2" = "Mantap! %@ %@ tercapai! Gas terus bro!";
"audio.distance.smart.3" = "Joss! %@ %@ kantong! Lo mesin banget!";
"audio.distance.smart.4" = "Gokil! %@ %@ selesai! Ayo lanjut!";
"audio.distance.smart.5" = "Luar biasa! %@ %@ terwujud! Lo monster!";
"audio.distance.smart.6" = "Mantul! %@ %@ dikuasai! Jangan berhenti!";
"audio.distance.smart.7" = "Top! %@ %@ dilewati! Power banget!";
"audio.distance.smart.8" = "Hebat! %@ %@ dibuka! Lo api!";
"audio.distance.smart.9" = "Spektakuler! %@ %@ dihancurkan! Kekuatan murni!";
"audio.distance.smart.10" = "Legend! %@ %@ dimusnahkan! Lo yang terbaik!";

// Time Smart Variations - Indonesia motivasional
"audio.time.smart.1" = "Udah %@ lari! Daya tahan lo keren!";
"audio.time.smart.2" = "Wih! %@ energi murni! Lanjut terus!";
"audio.time.smart.3" = "Joss! %@ tanpa henti! Lo mesin!";
"audio.time.smart.4" = "Keren! %@ tekad! Gas pol!";
"audio.time.smart.5" = "Mantap! %@ fokus! Ayo terus!";
"audio.time.smart.6" = "Mantul! %@ usaha! Warrior banget!";
"audio.time.smart.7" = "Top! %@ tak tertahankan! Lanjut gitu!";
"audio.time.smart.8" = "Hebat! %@ perjuangan! Lo api!";
"audio.time.smart.9" = "Spektakuler! %@ passion! Lo terhebat!";
"audio.time.smart.10" = "Legend! %@ destruksi! Glory murni!";

// Calories Smart Variations - Indonesia motivasional
"audio.calories.smart.1" = "Wih! Lo udah bakar %@ kalori! Lo api!";
"audio.calories.smart.2" = "Joss! %@ kalori lenyap! Terus bakar!";
"audio.calories.smart.3" = "Keren! %@ kalori dikuasai! Mesin banget!";
"audio.calories.smart.4" = "Mantap! %@ kalori tercapai! Gas!";
"audio.calories.smart.5" = "Mantul! %@ kalori dihancurkan! Jangan stop!";
"audio.calories.smart.6" = "Top! %@ kalori diremukkan! Lo monster!";
"audio.calories.smart.7" = "Hebat! %@ kalori dimusnahkan! Lanjut!";
"audio.calories.smart.8" = "Spektakuler! %@ kalori menguap! Tak tertahankan!";
"audio.calories.smart.9" = "Gokil! %@ kalori dihabisi! Mode beast!";
"audio.calories.smart.10" = "Legend! %@ kalori dihancurkan! Lo raja!";

// Pace Smart Variations - Indonesia motivasional
"audio.pace.smart.1" = "Perfect! Pace %@! Kontrol mantap!";
"audio.pace.smart.2" = "Wih! %@ kecepatan! Power murni!";
"audio.pace.smart.3" = "Joss! %@ terkendali! Lo mesin!";
"audio.pace.smart.4" = "Keren! %@ dikuasai! Teknik bagus!";
"audio.pace.smart.5" = "Mantap! %@ sempurna! Lanjut gitu!";
"audio.pace.smart.6" = "Mantul! %@ flow! Lo monster!";
"audio.pace.smart.7" = "Top! %@ profesional! Level tinggi!";
"audio.pace.smart.8" = "Hebat! %@ tak tertahankan! Lo api!";
"audio.pace.smart.9" = "Spektakuler! %@ impian! Mode beast!";
"audio.pace.smart.10" = "Legend! %@ pemusnah! Lo raja!";

// MARK: - Workout Settings
"workout.settings.title" = "Pengaturan Latihan";
"workout.settings.notice" = "Latihan Sedang Berlangsung";
"workout.settings.safe.only" = "Hanya pengaturan aman untuk latihan yang tersedia";

// MARK: - Voice Premium Features
"voice.selection.simple.description" = "Pilih suara pilihan Anda untuk panduan audio latihan selama lari.";
"voice.premium.benefits.recommendation" = "Rekomendasikan menambahkan suara PREMIUM untuk pengalaman terbaik";
"voice.premium.benefit.clarity" = "Kejernihan audio superior untuk latihan outdoor";
"voice.premium.benefit.noise" = "Performa lebih baik di lingkungan bising";
"voice.premium.benefit.motivation" = "Nada suara yang lebih alami dan memotivasi";
"voice.management.footer" = "Unduh suara baru dari Pengaturan iOS dan refresh untuk melihat suara yang baru dipasang";
"voice.selection.workout.description" = "Pilih suara Anda untuk panduan audio latihan. Suara premium memberikan pengalaman yang paling jelas dan memotivasi selama lari.";

// MARK: - Speech Speed Settings
"speech_speed" = "Kecepatan Bicara";
"speech_speed_footer" = "Kontrol seberapa cepat panduan suara diucapkan. Kecepatan yang lebih lambat membantu pemahaman selama latihan intensif atau di lingkungan bising.";

// MARK: - Data Management
"data_management" = "Manajemen Data";
"data_privacy" = "Privasi Data";
"export_data" = "Ekspor Data";
"import_data" = "Impor Data";
"delete_data" = "Hapus Data";

// Privacy Messages
"privacy_ownership" = "Anda mempertahankan kepemilikan data Anda";
"privacy_icloud_storage" = "Disimpan dengan aman di iCloud Anda";
"privacy_no_access" = "Kami (sebagai pengembang) tidak dapat mengakses data pribadi Anda";
"privacy_full_control" = "Anda mempertahankan kontrol penuh atas data Anda";

// Export
"export_all_data" = "Ekspor Semua Data";
"export_profile_only" = "Ekspor Profil Saja";
"export_activities_only" = "Ekspor Aktivitas Saja";
"export_all_description" = "Profil dan aktivitas dalam 2 file";
"export_profile_description" = "Pengaturan pribadi dan preferensi";
"export_activities_description" = "Semua data latihan dan statistik";
"export_data_description" = "Ekspor data Anda sebagai file CSV untuk cadangan atau transfer";

// Import
"import_data_files" = "Impor File Data";
"import_data_files_description" = "Pilih file CSV yang diekspor sebelumnya";
"import_data_description" = "Pulihkan dari file yang diekspor sebelumnya";

// Delete
"delete_all_data" = "Hapus Semua Data";
"delete_all_data_description" = "Hapus semua data secara permanen";
"delete_data_description" = "Tindakan ini tidak dapat dibatalkan";
"delete_confirmation" = "Konfirmasi Penghapusan";
"delete_confirmation_message" = "Ini akan menghapus semua data profil dan aktivitas Anda secara permanen. Tindakan ini tidak dapat dibatalkan.";
"delete_permanently" = "Hapus Permanen";

// Status Messages
"importing" = "Mengimpor...";
"deleting" = "Menghapus...";
"export_failed" = "Ekspor gagal";
"import_failed" = "Impor gagal";
"delete_failed" = "Penghapusan gagal";
"import_success" = "Impor Berhasil";
"import_success_message" = "Data telah berhasil diimpor";
"delete_success" = "Data Terhapus";
"delete_success_message" = "Semua data Anda telah dihapus secara permanen. Sekarang Anda akan kembali ke layar utama.";
"import_error" = "Kesalahan Impor";
"export_error" = "Kesalahan Ekspor";
"delete_error" = "Kesalahan Penghapusan";

"select_gender_title" = "Pilih Jenis Kelamin";

// MARK: - Activity Stats View
"act_dist" = "Jarak Latihan";
"act_time" = "Waktu Latihan";
"act_cal" = "Kal Latihan";
"performance" = "Performa";
"best_pace" = "Tempo Terbaik";
"avg_speed" = "Kecepatan Rata";
"max_speed" = "Kecepatan Maks";
"time_analysis" = "Analisis Waktu";
"total_time" = "Total Waktu";
"paused_time" = "Waktu Jeda";
"active_calories" = "Aktif";
"resting_calories" = "Istirahat";
"rate" = "Tingkat";
"kcal" = "kkal";
"cal_per_min" = "kal/mnt";
