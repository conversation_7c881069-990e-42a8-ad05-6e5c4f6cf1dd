// ActivityControls
"activities" = "운동 기록";
"metronome" = "메트로놈";

// StatsView
"distance" = "거리";
"active" = "활동";
"time" = "시간";
"current" = "현재";
"pace" = "페이스";
"km" = "km";
"mi" = "마일";

// ActivityView
"activities" = "운동 기록";
"no_activities" = "운동 기록 없음";
"complete_first_run" = "첫 러닝을 완료하면 여기에 표시됩니다";

// ActivityRowView
"distance" = "거리";
"duration" = "시간";
"avg_pace" = "평균 페이스";
"calories" = "칼로리";
"start" = "출발";
"end" = "골인";

// ActivityDetailView
"delete_activity" = "운동 기록 삭제";
"are_you_sure_deletion" = "정말 삭제하시겠습니까? 삭제 후에는 복구할 수 없습니다.";

// CountdownView
"go" = "돌진!";

// LanguageSettingView
"language" = "언어";
"done" = "완료";

// AuthView
"runapp" = "RunApp";
"email" = "이메일";
"sign_in_with_email" = "이메일로 로그인";
"check_inbox_login_link" = "받은 편지함에서 로그인 링크를 확인하세요.";

// AnalysisView
"improve_with_personalized_data" = "개인화된 데이터로 향상하기";
"run" = "러닝";
"walk" = "파워 워킹";
"hike" = "하이킹";
"bike" = "사이클링";
"distance" = "거리";
"duration" = "시간";
"calories" = "칼로리";
"current_week" = "이번 주";
"current_month" = "이번 달";
"current_year" = "올해";
"per_day" = "일당";
"per_month" = "월당";
"generate_test_data" = "테스트 데이터 생성";

// MetronomeSettingsView
"metronome_settings" = "메트로놈";
"tempo" = "템포";
"tempo_footer" = "40-240 BPM 사이로 템포 조절";
"enable_metronome" = "메트로놈 활성화";
"sound" = "소리";
"sound_type" = "소리 종류";
"vibration" = "진동";
"vibration_strength" = "진동 강도";
"alert_frequency" = "알림 빈도";
"largo" = "라르고";
"adagio" = "아다지오";
"andante" = "안단테";
"moderato" = "모데라토";
"allegro" = "알레그로";
"presto" = "프레스토";
"prestissimo" = "프레스티시모";
"default_beat" = "기본 비트";
"beat_2" = "비트 2";
"beat_3" = "비트 3";
"beat_1" = "비트 1";
"beat_4" = "비트 4";
"beat_5" = "비트 5";
"beat_6" = "비트 6";
"feedback" = "소리와 진동";
"feedback_footer" = "소리가 활성화되면 다양한 소리 종류 중에서 선택할 수 있습니다";

// NameSettingView
"name" = "이름";
"enter_your_name" = "이름을 입력하세요";
"cancel" = "취소";
"save" = "저장";
"invalid_name" = "잘못된 이름";
"please_enter_valid_name" = "올바른 이름을 입력해 주세요";

// WeightSettingView
"weight" = "체중";
"unit" = "단위";
"calorie_calculations_required" = "정확한 칼로리 계산에 필요합니다";
"invalid_weight" = "잘못된 체중";
"please_enter_valid_weight" = "%d에서 %d %@ 사이의 올바른 체중을 입력해 주세요";

// GenderSettingView
"gender" = "성별";
"gender_footer" = "선택사항 - 더 정확한 칼로리 계산에 사용됩니다";

// AgeSettingView
"age" = "나이";
"birth_date" = "생년월일";
"years_old" = "세";
"invalid_age" = "잘못된 나이";
"age_requirement" = "이 앱을 사용하려면 13세 이상이어야 합니다";

// HeightSettingView
"height" = "신장";
"height_cm" = "신장 (cm)";
"feet" = "피트";
"inches" = "인치";
"bmi_calculations" = "선택사항 - BMI 계산에 사용됩니다";
"invalid_height" = "잘못된 신장";
"please_enter_valid_height" = "올바른 신장을 입력해 주세요.";

// SettingsView
"settings" = "설정";
"profile" = "프로필";
"not_set" = "설정되지 않음";
"age" = "나이";
"years" = "세";
"audio" = "오디오";
"preferences" = "환경설정";
"metronome" = "메트로놈";
"bpm" = "BPM";
"units" = "단위";
"metric" = "미터법";
"imperial" = "야드파운드법";
"theme" = "테마";
"language" = "언어";
"data" = "데이터";
"all_activities" = "모든 운동 기록";
"export_activities" = "운동 기록 내보내기";
"exporting" = "내보내는 중...";
"error" = "오류";
"ok" = "확인";
"unknown_error" = "알 수 없는 오류가 발생했습니다";
"system_default" = "시스템 기본값";
"light" = "라이트";
"dark" = "다크";
"male" = "남성";
"female" = "여성";
"other" = "기타";
"prefer_not_to_say" = "응답하지 않음";

// UnitsSettingView
"unit_system" = "단위 시스템";

// SportTypeSelector
"run" = "러닝";
"walk" = "파워 워킹";
"hike" = "하이킹";
"bike" = "사이클링";

// ProfileHeaderView
"your_name" = "이름";
"email_example" = "<EMAIL>";
"change" = "변경";
"delete_photo" = "사진을 삭제하시겠습니까?";
"delete" = "삭제";

// ActivitySummaryComponents
"total" = "전체";
"avg" = "평균";
"per_day" = "일당";
"per_month" = "월당";

// PeriodIndicatorView
"current_week" = "이번 주";
"week_ago_format" = "%d주 전 (%d)";
"weeks_ago_format" = "%d주 전 (%d)";
"current_month" = "이번 달";
"month_ago_format" = "%d개월 전 (%d)";
"months_ago_format" = "%d개월 전 (%d)";
"current_year" = "올해";
"year_ago_format" = "%d년 전 (%d)";
"years_ago_format" = "%d년 전 (%d)";

// TimePeriodPicker
"time_period" = "기간";
"seven_days" = "1주";
"one_month" = "1개월";
"one_year" = "1년";
"all_time" = "전체";

// For timeOfDay values
"morning" = "아침 훈련";
"afternoon" = "오후 훈련";
"evening" = "저녁 러닝";
"night" = "야간 훈련";

"every_beat" = "매 비트";
"every_other_beat" = "2비트마다";
"every_4th_beat" = "4비트마다";
"every_6th_beat" = "6비트마다";

"per_day" = "일당";
"per_month" = "월당";

// MARK: - Welcome View
"welcome.title" = "RunApp에 오신 것을 환영합니다";
"welcome.subtitle" = "준비하세요, 모든 발걸음이 중요합니다!";
"welcome.button.start" = "시작하기";

// MARK: - Basic Info View
"basicInfo.header.title" = "경험을 개인화하세요";
"basicInfo.header.subtitle" = "칼로리 예측을 위해";

// MARK: - Gender Selection
"basicInfo.gender.title" = "성별이 어떻게 되시나요?";
"basicInfo.gender.male" = "남성";
"basicInfo.gender.female" = "여성";
"basicInfo.gender.preferNotToSay" = "응답하지 않음";
"basicInfo.button.skip" = "건너뛰기";

// MARK: - Weight Input
"basicInfo.weight.title" = "체중이 어떻게 되시나요?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lb";

// MARK: - Height Input
"basicInfo.height.title" = "신장이 어떻게 되시나요?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "FT";
"basicInfo.height.placeholder.in" = "IN";

"basicInfo.button.continue" = "계속";

// MARK: - All Set View
"allSet.title" = "모든 준비 완료!";
"allSet.subtitle.first" = "시작할 시간입니다";
"allSet.subtitle.second" = "핑계는 없습니다, 행동만 있을 뿐!";
"allSet.button.go" = "시작!";

// MARK: - ContentView Alerts
"alert.location.title" = "위치 권한이 필요합니다";
"alert.location.message" = "경로를 원활하게 추적하고 진행 상황을 정확하게 유지하려면 위치 '항상 허용'을 활성화하세요. 설정을 업데이트해 주세요.";
"alert.location.settings" = "설정";
"alert.location.later" = "나중에";

"alert.complete.title" = "활동을 완료하시겠습니까?";
"alert.complete.message" = "현재 활동이 종료됩니다.\n완료 후에는 재개할 수 없습니다.";
"alert.complete.confirm" = "완료";
"alert.complete.cancel" = "취소";

"alert.shortWorkout.title" = "짧은 운동 확인";
"alert.shortWorkout.message" = "운동 시간이 30초 미만입니다. 저장하시겠습니까?";
"alert.shortWorkout.save" = "저장";
"alert.shortWorkout.discard" = "삭제";

// MARK: - Map Markers
"map.marker.start" = "시작";
"map.marker.end" = "종료";
"map.location.unknown" = "알 수 없는 위치";

// MARK: - MetronomeMessageOverlay
"metronome.status.on" = "메트로놈 켜짐";
"metronome.status.off" = "메트로놈 꺼짐";

"activity.summary.title" = "활동 요약";

// GPS Status
"gps.searching" = "GPS 검색 중...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "무제한 액세스가 활성화되었습니다 — 이제 마음껏 운동하세요!";
"settings.subscription.status.exceeded" = "무료 체험 한도에 도달했어요. 지금 구독하고 계속 달려요!";
"settings.subscription.status.oneRemaining" = "남은 무료 활동 1회 — 지금 구독하고 계속 달리자!";
"settings.subscription.status.remaining" = "남은 무료 활동 %d회 — 구독하고 운동을 멈추지 마세요!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "구독";
"settings.subscription.unlimitedAccess" = "무제한 액세스";
"settings.subscription.checkingStatus" = "확인 중...";
"settings.subscription.alert.alreadySubscribed.title" = "이미 구독됨";
"settings.subscription.alert.alreadySubscribed.message" = "이미 무제한 액세스 권한이 있습니다.";
"settings.subscription.alert.checkFailed.format" = "구독 상태 확인 실패: %@";

// AudioAlertSettingsView
"audio_prompts" = "음성 안내";
"enable_audio_prompts" = "음성 안내 활성화";
"audio_prompts_footer" = "러닝 중 음성 안내 받기";
"distance_alerts" = "거리 알림";
"distance_alerts_footer" = "거리 이정표마다 안내";
"time_alerts" = "시간 알림";
"time_alerts_footer" = "시간 간격마다 안내";
"calorie_alerts" = "칼로리 알림";
"calorie_alerts_footer" = "칼로리 이정표 안내";
"pace_alerts" = "페이스 알림";
"pace_alerts_footer" = "현재 페이스를 주기적으로 안내";
"custom" = "사용자 정의";
"value" = "값";
"min" = "분";
"cal" = "칼로리";
"enabled" = "활성화됨";
"disabled" = "비활성화됨";
"hour" = "시간";
"h" = "시";
"m" = "분";
"distance_short" = "거리";
"time_short" = "시간";
"calorie_short" = "칼로리";
"pace_short" = "페이스";
"no_alerts_configured" = "설정 없음";
"all_alerts" = "모두 활성화";
"enter_custom_value" = "사용자 정의 값 입력";
"integers_only" = "정수만";
"invalid_input" = "잘못된 입력";
"please_enter_positive_number" = "양수를 입력해 주세요";
"please_enter_whole_number" = "정수를 입력해 주세요 (소수점 없이)";
"please_enter_value_between" = "%@에서 %@ 사이의 값을 입력해 주세요";
"invalid" = "잘못됨";

// MARK: - Custom Value Interface
"custom_interval" = "사용자 정의 간격";
"interval_value" = "간격 값";
"enter_value_placeholder" = "값 입력";
"whole_numbers_only" = "정수만";
"valid_range" = "유효 범위: %@ - %@";
"preset_options" = "빠른 옵션:";
"custom_value" = "사용자 정의 값";

// MARK: - Audio Volume Settings
"audio_volume" = "오디오 볼륨";
"audio_volume_footer" = "메트로놈 소리 볼륨 조정";
"volume" = "볼륨";
"voice_volume" = "음성 볼륨";
"speech_speed" = "음성 속도";
"speech_speed_footer" = "음성 안내의 재생 속도를 제어합니다. 고강도 운동이나 시끄러운 환경에서는 느린 속도가 이해에 도움이 됩니다.";
"audio_settings" = "오디오 설정";
"audio_settings_footer" = "음성 안내 볼륨과 속도 조정";
"test_voice" = "음성 테스트";

// MARK: - Smart Voice Messages
"smart_voice" = "스마트 음성";
"smart_voice_footer" = "표준 안내 대신 자연스럽고 동기부여가 되는 음성 안내를 활성화합니다";

// Test messages
"audio.test.standard" = "오디오 안내가 정상적으로 작동하고 있습니다";
"audio.test.smart" = "완벽해요! 오디오가 최고예요, 동기부여할 준비가 되었어요!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "잘했어요! %@ %@를 완주했습니다";
"audio.distance.achievement.smart" = "대단해요! %@ %@ 목표를 달성했습니다! 당신은 멈출 수 없어요!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "%@ 동안 운동을 계속하고 있어요. 이 멋진 페이스를 유지하세요!";

// Calorie messages
"audio.calories.standard" = "%@ 칼로리";
"audio.calories.smart" = "%@ 칼로리를 태웠어요! 노력이 결실을 맺고 있어요!";
"audio.calories.achievement.smart" = "환상적이에요! %@ 칼로리를 태웠어요! 당신은 칼로리 태우는 머신이에요!";

// Pace messages
"audio.pace.standard" = "현재 페이스: %@";
"audio.pace.smart" = "%@ 페이스를 유지하고 있어요. 정말 강해 보여요!";

// Unit strings for audio
"audio.unit.km.singular" = "킬로미터";
"audio.unit.km.plural" = "킬로미터";
"audio.unit.mile.singular" = "마일";
"audio.unit.mile.plural" = "마일";

// Time formatting for audio
"audio.time.minute.singular" = "1분";
"audio.time.minutes" = "%d분";
"audio.time.hour.singular" = "1시간";
"audio.time.hours" = "%d시간";
"audio.time.hours.minutes" = "%d시간 %d분";

// Pace formatting for audio
"audio.pace.per.km" = "킬로미터당";
"audio.pace.per.mile" = "마일당";
"audio.pace.seconds" = "%d초 %@";
"audio.pace.minutes" = "%d분 %@";
"audio.pace.minutes.seconds" = "%d분 %d초 %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "표준";
"voice.quality.enhanced" = "향상된";
"voice.quality.premium" = "프리미엄";
"voice.manager.test.text" = "러닝 알림을 위해 선택된 음성의 테스트입니다";
"voice.test.sample" = "러닝 알림을 위해 선택된 음성의 테스트입니다";
"voice.selection.title" = "음성 선택";
"voice.current" = "현재 음성";
"voice.preview" = "미리보기";
"voice.none.available" = "이 언어에 사용 가능한 음성이 없습니다";
"voice.loading" = "음성 로딩 중...";

// MARK: - Voice Selection UI
"voice.download.required" = "다운로드 필요";
"voice.loading.voices" = "음성 로딩 중...";
"voice.empty.title" = "사용 가능한 음성 없음";
"voice.empty.message" = "이 언어에 향상된 또는 프리미엄 음성이 없습니다. 설정에서 고품질 음성을 다운로드하여 경험을 개선하세요.";
"voice.refresh.voices" = "음성 새로고침";
"voice.refresh.footer" = "사용 가능한 음성 목록을 새로고침하려면 탭하세요";
"voice.current.language" = "현재 언어";
"voice.selection.description" = "오디오 알림을 위한 선호 음성을 선택하세요.\n향상된 및 프리미엄 옵션이 최고 품질을 제공합니다.";
"voice.selection.workout.description" = "운동 음성 안내를 위한 음성을 선택하세요. 프리미엄 음성은 러닝 중 가장 선명하고 동기부여가 되는 경험을 제공합니다.";
"voice.selection.simple.description" = "러닝 중 운동 음성 안내를 위한 선호 음성을 선택하세요.";
"voice.preview.error.title" = "미리보기 실패";
"voice.preview.error.message" = "'%@' 음성을 미리볼 수 없습니다. 다시 시도해 주세요.";
"voice.add.new" = "새 음성 추가";
"voice.manage.description" = "설정 > 손쉬운 사용 > 음성 콘텐츠 > 음성으로 이동하여 음성을 다운로드하고 관리하세요";
"voice.system.default" = "시스템 기본값";

// MARK: - Voice Instructions
"voice.instructions.title" = "새 음성 다운로드";
"voice.instructions.message" = "고품질 음성을 다운로드하려면 다음 단계를 따르세요:\n\n1. 아래 '설정 열기'를 탭하세요\n2. 다음으로 이동: 손쉬운 사용\n3. 선택: 음성 콘텐츠\n4. 선택: 음성\n5. 선호하는 음성을 다운로드하세요\n6. RunApp으로 돌아와서 새로고침하세요";
"voice.instructions.open.settings" = "설정 열기";
"voice.instructions.steps.title" = "음성 추가 방법:";
"voice.instructions.steps.detail" = "설정 → 손쉬운 사용 → 음성 콘텐츠 → 음성";
"voice.instructions.footer" = "새 음성 다운로드를 위한 단계별 지침을 보려면 위 버튼을 탭하세요";
"voice.add.new.subtitle" = "단계별 지침 받기";
"voice.management.footer" = "iOS 설정에서 새 음성을 다운로드하고 새로고침하여 새로 설치된 음성을 확인하세요";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "음성 선택";
"voice.selection.footer" = "오디오 안내를 위한 선호 음성을 선택하세요";

// Common buttons
"common.cancel" = "취소";
"common.close" = "닫기";
"common.done" = "완료";
"common.ok" = "확인";

// MARK: - Voice Management
"voice.delete.success" = "음성이 성공적으로 삭제되었습니다";
"voice.delete.failed" = "음성 삭제에 실패했습니다";
"voice.delete.confirm.title" = "음성을 삭제하시겠습니까?";
"voice.delete.confirm.message" = "기기에서 '%@'을(를) 제거합니다. 나중에 설정에서 다시 다운로드할 수 있습니다.";
"voice.delete.confirm.delete" = "삭제";
"voice.delete.unavailable" = "이 음성은 시스템 기본 음성이므로 삭제할 수 없습니다.";
"voice.manage.voices" = "음성 관리";
"voice.download.status" = "다운로드됨";
"voice.builtin.status" = "내장";

// MARK: - Smart Voice Prompt Variations (Korean)
// Distance Smart Variations - 한국어 응원 메시지
"audio.distance.smart.1" = "대박! 벌써 %@ %@ 뛰었어요! 짱이에요!";
"audio.distance.smart.2" = "와! %@ %@ 달성! 계속 파이팅!";
"audio.distance.smart.3" = "쩔어! %@ %@ 깼네요! 완전 짱!";
"audio.distance.smart.4" = "멋져! %@ %@ 돌파! 너무 강해요!";
"audio.distance.smart.5" = "미친! %@ %@ 성공! 진짜 에이스!";
"audio.distance.smart.6" = "개쩐다! %@ %@ 제압! 멈추지 마요!";
"audio.distance.smart.7" = "굿! %@ %@ 넘었어요! 파워 폭발!";
"audio.distance.smart.8" = "킹왕짱! %@ %@ 해제! 불타고 있어요!";
"audio.distance.smart.9" = "레전드! %@ %@ 압살! 순수 파워!";
"audio.distance.smart.10" = "신급! %@ %@ 박살! 당신이 왕이야!";

// Time Smart Variations - 한국어 응원 메시지
"audio.time.smart.1" = "벌써 %@ 달렸어요! 지구력 장난 아니네요!";
"audio.time.smart.2" = "와! %@ 순수 파워! 계속 밀어붙여요!";
"audio.time.smart.3" = "쩔어! %@ 논스톱! 완전 기계같아요!";
"audio.time.smart.4" = "대박! %@ 의지력! 강하게 가요!";
"audio.time.smart.5" = "멋져! %@ 집중! 쭉쭉 가요!";
"audio.time.smart.6" = "미친! %@ 노력! 진짜 워리어!";
"audio.time.smart.7" = "굿! %@ 무적 상태! 계속해요!";
"audio.time.smart.8" = "킹왕짱! %@ 파이팅 중! 불타고 있어요!";
"audio.time.smart.9" = "레전드! %@ 열정! 최고예요!";
"audio.time.smart.10" = "신급! %@ 파괴 중! 순수한 영광!";

// Calories Smart Variations - 한국어 응원 메시지
"audio.calories.smart.1" = "와! %@ 칼로리 태웠어요! 불타고 있네요!";
"audio.calories.smart.2" = "쩔어! %@ 칼로리 소멸! 계속 태워요!";
"audio.calories.smart.3" = "대박! %@ 칼로리 제압! 완전 기계!";
"audio.calories.smart.4" = "멋져! %@ 칼로리 정복! 가즈아!";
"audio.calories.smart.5" = "미친! %@ 칼로리 박살! 멈추지 마요!";
"audio.calories.smart.6" = "굿! %@ 칼로리 폭발! 진짜 에이스!";
"audio.calories.smart.7" = "킹왕짱! %@ 칼로리 분쇄! 계속해요!";
"audio.calories.smart.8" = "레전드! %@ 칼로리 증발! 무적이에요!";
"audio.calories.smart.9" = "쩔다! %@ 칼로리 전멸! 비스트 모드!";
"audio.calories.smart.10" = "신급! %@ 칼로리 삭제! 당신이 왕이야!";

// Pace Smart Variations - 한국어 응원 메시지
"audio.pace.smart.1" = "완벽! %@ 페이스! 컨트롤 쩔어요!";
"audio.pace.smart.2" = "와! %@ 속도! 순수한 파워!";
"audio.pace.smart.3" = "쩔어! %@ 마스터! 완전 기계같아요!";
"audio.pace.smart.4" = "대박! %@ 제압! 테크닉 미쳤어요!";
"audio.pace.smart.5" = "멋져! %@ 퍼펙트! 계속해요!";
"audio.pace.smart.6" = "미친! %@ 매끄러워! 진짜 에이스!";
"audio.pace.smart.7" = "굿! %@ 프로급! 레벨 높아요!";
"audio.pace.smart.8" = "킹왕짱! %@ 무적! 불타고 있어요!";
"audio.pace.smart.9" = "레전드! %@ 환상적! 비스트 모드!";
"audio.pace.smart.10" = "신급! %@ 파괴적! 당신이 왕이야!";

// MARK: - Workout Settings
"workout.settings.title" = "운동 설정";
"workout.settings.notice" = "운동 진행 중";
"workout.settings.safe.only" = "운동 중 안전한 설정만 이용 가능";

// MARK: - Premium Voice Benefits
"voice.premium.benefits.title" = "최고의 운동 경험을 위한 프리미엄 음성";
"voice.premium.benefits.subtitle" = "주변 소음을 뚫고 나오는 수정처럼 맑은 음성 안내를 받고 고강도 운동 중에 동기부여를 받으세요";
"voice.premium.benefits.recommendation" = "최고의 경험을 위해 프리미엄 음성 추가를 권장합니다";
"voice.premium.benefit.clarity" = "야외 운동에서 뛰어난 오디오 선명도";
"voice.premium.benefit.noise" = "시끄러운 환경에서 더 나은 성능";
"voice.premium.benefit.motivation" = "더 자연스럽고 동기부여가 되는 음성 톤";
"voice.premium.recommended" = "권장";
"voice.premium.footer" = "프리미엄 음성은 운동 중 가장 선명한 오디오를 제공하며 더 나은 소음 저항성과 자연스러운 음성 패턴을 제공합니다.";
"voice.enhanced.footer" = "향상된 음성은 표준 시스템 음성보다 개선된 품질과 더 나은 선명도를 제공합니다.";

// MARK: - Voice Quality Descriptions
"voice.quality.standard.description" = "표준 품질 음성";
"voice.quality.enhanced.description" = "더 나은 오디오를 위한 향상된 선명도";
"voice.quality.premium.description" = "운동 동기부여에 최적화";

// MARK: - Data Management
"data_management" = "데이터 관리";
"data_privacy" = "데이터 프라이버시";
"export_data" = "데이터 내보내기";
"import_data" = "데이터 가져오기";
"delete_data" = "데이터 삭제";

// Privacy Messages
"privacy_ownership" = "데이터의 소유권을 유지합니다";
"privacy_icloud_storage" = "iCloud에 안전하게 저장됩니다";
"privacy_no_access" = "저희(개발자)는 귀하의 개인 데이터에 접근할 수 없습니다";
"privacy_full_control" = "데이터에 대한 완전한 제어권을 유지합니다";

// Export
"export_all_data" = "모든 데이터 내보내기";
"export_profile_only" = "프로필만 내보내기";
"export_activities_only" = "활동만 내보내기";
"export_all_description" = "프로필과 활동을 2개 파일로";
"export_profile_description" = "개인 설정 및 환경설정";
"export_activities_description" = "모든 운동 데이터 및 통계";
"export_data_description" = "백업이나 전송을 위해 데이터를 CSV 파일로 내보내기";

// Import
"import_data_files" = "데이터 파일 가져오기";
"import_data_files_description" = "이전에 내보낸 CSV 파일 선택";
"import_data_description" = "이전에 내보낸 파일에서 복원";

// Delete
"delete_all_data" = "모든 데이터 삭제";
"delete_all_data_description" = "모든 데이터를 영구적으로 제거";
"delete_data_description" = "이 작업은 되돌릴 수 없습니다";
"delete_confirmation" = "삭제 확인";
"delete_confirmation_message" = "모든 프로필 데이터와 활동이 영구적으로 삭제됩니다. 이 작업은 되돌릴 수 없습니다.";
"delete_permanently" = "영구 삭제";

// Status Messages
"importing" = "가져오는 중...";
"deleting" = "삭제 중...";
"export_failed" = "내보내기 실패";
"import_failed" = "가져오기 실패";
"delete_failed" = "삭제 실패";
"import_success" = "가져오기 성공";
"import_success_message" = "데이터를 성공적으로 가져왔습니다";
"delete_success" = "데이터 삭제됨";
"delete_success_message" = "모든 데이터가 영구적으로 삭제되었습니다. 메인 화면으로 돌아갑니다.";
"import_error" = "가져오기 오류";
"export_error" = "내보내기 오류";
"delete_error" = "삭제 오류";

"select_gender_title" = "성별 선택";

// MARK: - Activity Stats View
"act_dist" = "운동 거리";
"act_time" = "운동 시간";
"act_cal" = "운동 칼로리";
"performance" = "퍼포먼스";
"best_pace" = "베스트 페이스";
"avg_speed" = "평균 스피드";
"max_speed" = "최고 스피드";
"time_analysis" = "시간 분석";
"total_time" = "총 시간";
"paused_time" = "휴식 시간";
"active_calories" = "액티브";
"resting_calories" = "안정시";
"rate" = "연소율";
"kcal" = "kcal";
"cal_per_min" = "칼/분";
