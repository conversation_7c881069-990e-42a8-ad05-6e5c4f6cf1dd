// ActivityControls
"activities" = "Treinos";
"metronome" = "Metrônomo";

// StatsView
"distance" = "Distância";
"active" = "Ativo";
"time" = "Tempo";
"current" = "Atual";
"pace" = "Ritmo";
"km" = "km";
"mi" = "mi";

// ActivityView
"activities" = "Treinos";
"no_activities" = "Sem Treinos";
"complete_first_run" = "Complete sua primeira corrida para vê-la aqui";

// ActivityRowView
"distance" = "Distância";
"duration" = "Duração";
"avg_pace" = "Ritmo Médio";
"calories" = "Calorias";
"start" = "Largada";
"end" = "Chegada";

// ActivityDetailView
"delete_activity" = "Excluir Treino";
"are_you_sure_deletion" = "Tem certeza? A exclusão não pode ser desfeita.";

// CountdownView
"go" = "ACELERA!";

// LanguageSettingView
"language" = "Idioma";
"done" = "Concluído";

// AuthView
"runapp" = "RunApp";
"email" = "E-mail";
"sign_in_with_email" = "Entrar com E-mail";
"check_inbox_login_link" = "Verifique sua caixa de entrada para o link de login.";

// AnalysisView
"improve_with_personalized_data" = "Melhore com Dados Personalizados";
"run" = "Corrida";
"walk" = "Caminhada Atlética";
"hike" = "Trekking";
"bike" = "Ciclismo";
"distance" = "Distância";
"duration" = "Duração";
"calories" = "Calorias";
"current_week" = "Semana atual";
"current_month" = "Mês atual";
"current_year" = "Ano atual";
"per_day" = "por dia";
"per_month" = "por mês";
"generate_test_data" = "Gerar Dados de Teste";

// MetronomeSettingsView
"metronome_settings" = "Metrônomo";
"tempo" = "Tempo";
"tempo_footer" = "Ajuste o tempo entre 40-240 batidas por minuto";
"enable_metronome" = "Ativar Metrônomo";
"sound" = "Som";
"sound_type" = "Tipo de Som";
"vibration" = "Vibração";
"vibration_strength" = "Força da Vibração";
"alert_frequency" = "Frequência de Alerta";
"largo" = "Largo";
"adagio" = "Adagio";
"andante" = "Andante";
"moderato" = "Moderato";
"allegro" = "Allegro";
"presto" = "Presto";
"prestissimo" = "Prestissimo";
"default_beat" = "Batida Padrão";
"beat_2" = "Batida 2";
"beat_3" = "Batida 3";
"beat_1" = "Batida 1";
"beat_4" = "Batida 4";
"beat_5" = "Batida 5";
"beat_6" = "Batida 6";
"feedback" = "Som e Vibração";
"feedback_footer" = "Escolha entre diferentes tipos de som quando o som estiver ativado";

// NameSettingView
"name" = "Nome";
"enter_your_name" = "Digite seu nome";
"cancel" = "Cancelar";
"save" = "Salvar";
"invalid_name" = "Nome Inválido";
"please_enter_valid_name" = "Por favor, digite um nome válido";

// WeightSettingView
"weight" = "Peso";
"unit" = "Unidade";
"calorie_calculations_required" = "Necessário para cálculos precisos de calorias";
"invalid_weight" = "Peso Inválido";
"please_enter_valid_weight" = "Por favor, digite um peso válido entre %d e %d %@";

// GenderSettingView
"gender" = "Gênero";
"gender_footer" = "Opcional - Usado para cálculos mais precisos de calorias";

// AgeSettingView
"age" = "Idade";
"birth_date" = "Data de Nascimento";
"years_old" = "anos";
"invalid_age" = "Idade Inválida";
"age_requirement" = "Você precisa ter pelo menos 13 anos para usar este aplicativo";

// HeightSettingView
"height" = "Altura";
"height_cm" = "Altura (cm)";
"feet" = "Pés";
"inches" = "Polegadas";
"bmi_calculations" = "Opcional - Usado para cálculos de IMC";
"invalid_height" = "Altura Inválida";
"please_enter_valid_height" = "Por favor, digite uma altura válida.";

// SettingsView
"settings" = "Configurações";
"profile" = "Perfil";
"not_set" = "Não definido";
"age" = "Idade";
"years" = "anos";
"audio" = "Áudio";
"preferences" = "Preferências";
"metronome" = "Metrônomo";
"bpm" = "bpm";
"units" = "Unidades";
"metric" = "Métrico";
"imperial" = "Imperial";
"theme" = "Tema";
"language" = "Idioma";
"data" = "Dados";
"all_activities" = "Todos os Treinos";
"export_activities" = "Exportar Treinos";
"exporting" = "Exportando...";
"error" = "Erro";
"ok" = "OK";
"unknown_error" = "Ocorreu um erro desconhecido";
"system_default" = "Padrão do Sistema";
"light" = "Claro";
"dark" = "Escuro";
"male" = "Masculino";
"female" = "Feminino";
"other" = "Outro";
"prefer_not_to_say" = "Prefiro não dizer";

// UnitsSettingView
"unit_system" = "Sistema de Unidades";

// SportTypeSelector
"run" = "Corrida";
"walk" = "Caminhada Atlética";
"hike" = "Trekking";
"bike" = "Ciclismo";

// ProfileHeaderView
"your_name" = "Seu Nome";
"email_example" = "<EMAIL>";
"change" = "Alterar";
"delete_photo" = "Excluir foto?";
"delete" = "Excluir";

// ActivitySummaryComponents
"total" = "Total";
"avg" = "Média";
"per_day" = "por dia";
"per_month" = "por mês";

// PeriodIndicatorView
"current_week" = "Semana atual";
"week_ago_format" = "%d semana atrás (%d)";
"weeks_ago_format" = "%d semanas atrás (%d)";
"current_month" = "Mês atual";
"month_ago_format" = "%d mês atrás (%d)";
"months_ago_format" = "%d meses atrás (%d)";
"current_year" = "Ano atual";
"year_ago_format" = "%d ano atrás (%d)";
"years_ago_format" = "%d anos atrás (%d)";

// TimePeriodPicker
"time_period" = "Período";
"seven_days" = "1 semana";
"one_month" = "1 mês";
"one_year" = "1 ano";
"all_time" = "Todo o Período";

// For timeOfDay values
"morning" = "Treino Matinal";
"afternoon" = "Treino da Tarde";
"evening" = "Corrida Noturna";
"night" = "Treino Noturno";

"every_beat" = "Cada Batida";
"every_other_beat" = "A cada Duas Batidas";
"every_4th_beat" = "A cada 4 Batidas";
"every_6th_beat" = "A cada 6 Batidas";

"per_day" = "por dia";
"per_month" = "por mês";

// MARK: - Welcome View
"welcome.title" = "Bem-vindo ao RunApp";
"welcome.subtitle" = "Prepare-se, cada passo conta!";
"welcome.button.start" = "Começar";

// MARK: - Basic Info View
"basicInfo.header.title" = "Personalize sua Experiência";
"basicInfo.header.subtitle" = "para cálculo de calorias";

// MARK: - Gender Selection
"basicInfo.gender.title" = "Qual é o seu gênero?";
"basicInfo.gender.male" = "Masculino";
"basicInfo.gender.female" = "Feminino";
"basicInfo.gender.preferNotToSay" = "Prefiro não dizer";
"basicInfo.button.skip" = "Pular";

// MARK: - Weight Input
"basicInfo.weight.title" = "Qual é o seu peso?";
"basicInfo.weight.unit.kg" = "kg";
"basicInfo.weight.unit.lbs" = "lbs";

// MARK: - Height Input
"basicInfo.height.title" = "Qual é a sua altura?";
"basicInfo.height.unit.cm" = "cm";
"basicInfo.height.unit.ftIn" = "ft";
"basicInfo.height.placeholder.cm" = "CM";
"basicInfo.height.placeholder.ft" = "PÉS";
"basicInfo.height.placeholder.in" = "POL";

"basicInfo.button.continue" = "Continuar";

// MARK: - All Set View
"allSet.title" = "Tudo Pronto!";
"allSet.subtitle.first" = "Hora de começar";
"allSet.subtitle.second" = "Sem desculpas, apenas ação.";
"allSet.button.go" = "Vamos lá!";

// MARK: - ContentView Alerts
"alert.location.title" = "Permissão de Localização Necessária";
"alert.location.message" = "Ative 'Permitir Sempre' para localização para rastrear rotas perfeitamente e manter seu progresso preciso. Por favor, atualize suas configurações.";
"alert.location.settings" = "Configurações";
"alert.location.later" = "Depois";

"alert.complete.title" = "Completar Atividade?";
"alert.complete.message" = "Isso encerrará sua atividade atual.\nVocê não poderá retomá-la depois de completar.";
"alert.complete.confirm" = "Completar";
"alert.complete.cancel" = "Cancelar";

"alert.shortWorkout.title" = "Confirmar Treino Curto";
"alert.shortWorkout.message" = "Seu treino é menor que 30 segundos. Tem certeza que quer salvá-lo?";
"alert.shortWorkout.save" = "Salvar";
"alert.shortWorkout.discard" = "Descartar";

// MARK: - Map
"map.marker.start" = "Início";
"map.marker.end" = "Fim";
"map.location.unknown" = "Local Desconhecido";

// MARK: - Metronome Status
"metronome.status.on" = "Metrônomo Ativado";
"metronome.status.off" = "Metrônomo Desativado";

"activity.summary.title" = "Resumo de atividade";

// GPS Status
"gps.searching" = "Procurando GPS...";

// MARK: - Settings Subscription Status
"settings.subscription.status.unlimited" = "Acesso ilimitado ativado — treine sem limites!";
"settings.subscription.status.exceeded" = "Você atingiu o limite da versão gratuita. Assine agora e continue com tudo!";
"settings.subscription.status.oneRemaining" = "Você tem 1 atividade grátis restante — assine agora para manter o ritmo!";
"settings.subscription.status.remaining" = "Você tem %d atividades grátis restantes — assine agora e não pare!";

// MARK: - Settings Subscription Section
"settings.subscription.title" = "Assinatura";
"settings.subscription.unlimitedAccess" = "Acesso Ilimitado";
"settings.subscription.checkingStatus" = "Verificando...";
"settings.subscription.alert.alreadySubscribed.title" = "Já Assinado";
"settings.subscription.alert.alreadySubscribed.message" = "Você já tem acesso ilimitado.";
"settings.subscription.alert.checkFailed.format" = "Falha ao verificar status da assinatura: %@";

// AudioAlertSettingsView
"audio_prompts" = "Avisos de Áudio";
"enable_audio_prompts" = "Ativar Avisos de Áudio";
"audio_prompts_footer" = "Receber anúncios de voz durante suas corridas";
"distance_alerts" = "Alertas de Distância";
"distance_alerts_footer" = "Anunciar cada marco de distância";
"time_alerts" = "Alertas de Tempo";
"time_alerts_footer" = "Anunciar cada intervalo de tempo";
"calorie_alerts" = "Alertas de Calorias";
"calorie_alerts_footer" = "Anunciar marcos de calorias";
"pace_alerts" = "Alertas de Ritmo";
"pace_alerts_footer" = "Anunciar ritmo atual em intervalos";
"custom" = "Personalizado";
"value" = "Valor";
"min" = "min";
"cal" = "cal";
"enabled" = "Ativado";
"disabled" = "Desativado";
"hour" = "hora";
"h" = "h";
"m" = "m";
"distance_short" = "Distância";
"time_short" = "Tempo";
"calorie_short" = "Calorias";
"pace_short" = "Ritmo";
"no_alerts_configured" = "Nenhum configurado";
"all_alerts" = "Todos ativados";
"enter_custom_value" = "Inserir valor personalizado";
"integers_only" = "Apenas números inteiros";
"invalid_input" = "Entrada Inválida";
"please_enter_positive_number" = "Por favor, digite um número positivo";
"please_enter_whole_number" = "Por favor, digite um número inteiro (sem decimais)";
"please_enter_value_between" = "Por favor, digite um valor entre %@ e %@";
"invalid" = "Inválido";

"per_day" = "por dia";

// MARK: - Custom Value Interface
"custom_interval" = "Intervalo Personalizado";
"interval_value" = "Valor do Intervalo";
"enter_value_placeholder" = "Digite o valor";
"whole_numbers_only" = "Apenas números inteiros";
"valid_range" = "Faixa válida: %@ - %@";
"preset_options" = "Opções rápidas:";
"custom_value" = "Valor Personalizado";

// MARK: - Audio Volume Settings
"audio_volume" = "Volume do Áudio";
"audio_volume_footer" = "Ajustar o volume do metrônomo";
"volume" = "Volume";
"voice_volume" = "Volume da Voz";
"audio_settings" = "Configurações de Áudio";
"audio_settings_footer" = "Ajustar o volume e velocidade dos anúncios de voz";
"test_voice" = "Testar Voz";

// MARK: - Smart Voice Messages
"smart_voice" = "Voz Inteligente";
"smart_voice_footer" = "Ativa anúncios de voz naturais e motivadores ao invés de anúncios padrão";

// Test messages
"audio.test.standard" = "Os anúncios de áudio estão funcionando corretamente";
"audio.test.smart" = "Perfeito! Seu áudio está excelente, pronto para te motivar!";

// Distance messages
"audio.distance.standard" = "%@ %@";
"audio.distance.smart" = "Muito bem! Você completou %@ %@";
"audio.distance.achievement.smart" = "Incrível! Você alcançou a marca de %@ %@! Você é imparável!";

// Time messages
"audio.time.standard" = "%@";
"audio.time.smart" = "Você está em movimento há %@. Continue com esse ritmo fantástico!";

// Calorie messages
"audio.calories.standard" = "%@ calorias";
"audio.calories.smart" = "Você queimou %@ calorias! Seu esforço está valendo a pena!";
"audio.calories.achievement.smart" = "Espetacular! %@ calorias queimadas! Você é uma máquina queima-calorias!";

// Pace messages
"audio.pace.standard" = "Ritmo atual: %@";
"audio.pace.smart" = "Você está mantendo um ritmo de %@. Você está muito forte!";

// Unit strings for audio
"audio.unit.km.singular" = "quilômetro";
"audio.unit.km.plural" = "quilômetros";
"audio.unit.mile.singular" = "milha";
"audio.unit.mile.plural" = "milhas";

// Time formatting for audio
"audio.time.minute.singular" = "1 minuto";
"audio.time.minutes" = "%d minutos";
"audio.time.hour.singular" = "1 hora";
"audio.time.hours" = "%d horas";
"audio.time.hours.minutes" = "%d horas e %d minutos";

// Pace formatting for audio
"audio.pace.per.km" = "por quilômetro";
"audio.pace.per.mile" = "por milha";
"audio.pace.seconds" = "%d segundos %@";
"audio.pace.minutes" = "%d minutos %@";
"audio.pace.minutes.seconds" = "%d minutos e %d segundos %@";

// MARK: - Voice Quality and Selection
"voice.quality.standard" = "Padrão";
"voice.quality.enhanced" = "Aprimorada";
"voice.quality.premium" = "Premium";
"voice.manager.test.text" = "Este é um teste da voz selecionada para seus alertas de corrida";
"voice.test.sample" = "Este é um teste da voz selecionada para seus alertas de corrida";
"voice.selection.title" = "Seleção de Voz";
"voice.current" = "Voz Atual";
"voice.preview" = "Visualizar";
"voice.none.available" = "Nenhuma voz disponível para este idioma";
"voice.loading" = "Carregando vozes...";

// MARK: - Voice Selection UI
"voice.download.required" = "Download Necessário";
"voice.loading.voices" = "Carregando vozes...";
"voice.empty.title" = "Nenhuma Voz Disponível";
"voice.empty.message" = "Nenhuma voz aprimorada ou premium está disponível para este idioma. Baixe vozes de alta qualidade das Configurações para melhorar sua experiência.";
"voice.refresh.voices" = "Atualizar Vozes";
"voice.refresh.footer" = "Toque para atualizar a lista de vozes disponíveis";
"voice.current.language" = "Idioma Atual";
"voice.selection.description" = "Selecione sua voz preferida para alertas de áudio.\nAs opções aprimoradas e premium oferecem a mais alta qualidade.";
"voice.preview.error.title" = "Falha na Visualização";
"voice.preview.error.message" = "Não foi possível visualizar a voz '%@'. Tente novamente.";
"voice.add.new" = "Adicionar Nova Voz";
"voice.manage.description" = "Vá para Configurações > Acessibilidade > Conteúdo Falado > Vozes para baixar e gerenciar vozes";
"voice.system.default" = "Padrão do Sistema";

// MARK: - Voice Instructions
"voice.instructions.title" = "Baixar Novas Vozes";
"voice.instructions.message" = "Siga estes passos para baixar vozes de alta qualidade:\n\n1. Toque em 'Abrir Configurações' abaixo\n2. Navegue para: Acessibilidade\n3. Selecione: Conteúdo Falado\n4. Escolha: Vozes\n5. Baixe suas vozes preferidas\n6. Volte ao RunApp e atualize";
"voice.instructions.open.settings" = "Abrir Configurações";
"voice.instructions.steps.title" = "Como adicionar vozes:";
"voice.instructions.steps.detail" = "Configurações → Acessibilidade → Conteúdo Falado → Vozes";
"voice.instructions.footer" = "Toque no botão acima para ver instruções passo a passo para baixar novas vozes";
"voice.add.new.subtitle" = "Obter instruções passo a passo";

// MARK: - Voice Selection for Audio Settings
"voice.selection" = "Seleção de Voz";
"voice.selection.footer" = "Escolha sua voz preferida para anúncios de áudio";

// Common buttons
"common.cancel" = "Cancelar";
"common.close" = "Fechar";
"common.done" = "Concluído";
"common.ok" = "OK";

// MARK: - Voice Management
"voice.delete.success" = "Voz excluída com sucesso";
"voice.delete.failed" = "Falha ao excluir voz";
"voice.delete.confirm.title" = "Excluir Voz?";
"voice.delete.confirm.message" = "Isso removerá '%@' do seu dispositivo. Você pode baixá-la novamente mais tarde nas Configurações.";
"voice.delete.confirm.delete" = "Excluir";
"voice.delete.unavailable" = "Esta voz não pode ser excluída pois é uma voz padrão do sistema.";
"voice.manage.voices" = "Gerenciar Vozes";
"voice.download.status" = "Baixada";
"voice.builtin.status" = "Integrada";

// MARK: - Smart Voice Prompt Variations (Portuguese)
// Distance Smart Variations - Português motivacional
"audio.distance.smart.1" = "Incrível! Você já correu %@ %@! Você é o cara!";
"audio.distance.smart.2" = "Massa! %@ %@ conquistados! Segue firme, campeão!";
"audio.distance.smart.3" = "Irado! %@ %@ no bolso! Que máquina!";
"audio.distance.smart.4" = "Sensacional! %@ %@ completados! Vai que vai!";
"audio.distance.smart.5" = "Fantástico! %@ %@ realizados! Você é fera!";
"audio.distance.smart.6" = "Arrasou! %@ %@ dominados! Não para!";
"audio.distance.smart.7" = "Show! %@ %@ superados! Que potência!";
"audio.distance.smart.8" = "Demais! %@ %@ desbloqueados! Você é fogo!";
"audio.distance.smart.9" = "Espetacular! %@ %@ trucidados! Pura força!";
"audio.distance.smart.10" = "Lenda! %@ %@ aniquilados! Você é o rei!";

// Time Smart Variations - Português motivacional
"audio.time.smart.1" = "Já são %@ correndo! Que resistência!";
"audio.time.smart.2" = "Eita! %@ de pura energia! Segue mandando!";
"audio.time.smart.3" = "Irado! %@ sem parar! Você é uma máquina!";
"audio.time.smart.4" = "Incrível! %@ de determinação! Vai com tudo!";
"audio.time.smart.5" = "Fantástico! %@ concentrado! Vai que vai!";
"audio.time.smart.6" = "Arrasou! %@ de esforço! Que guerreiro!";
"audio.time.smart.7" = "Show! %@ implacável! Segue assim!";
"audio.time.smart.8" = "Demais! %@ lutando! Você é fogo!";
"audio.time.smart.9" = "Espetacular! %@ de paixão! Você é o melhor!";
"audio.time.smart.10" = "Lenda! %@ destruindo! Pura glória!";

// Calories Smart Variations - Português motivacional
"audio.calories.smart.1" = "Eita! Você queimou %@ calorias! Você é fogo!";
"audio.calories.smart.2" = "Irado! %@ calorias eliminadas! Segue queimando!";
"audio.calories.smart.3" = "Incrível! %@ calorias dominadas! Que máquina!";
"audio.calories.smart.4" = "Fantástico! %@ calorias conquistadas! Vai!";
"audio.calories.smart.5" = "Arrasou! %@ calorias demolidas! Não para!";
"audio.calories.smart.6" = "Show! %@ calorias trucidadas! Você é fera!";
"audio.calories.smart.7" = "Demais! %@ calorias destruídas! Segue!";
"audio.calories.smart.8" = "Espetacular! %@ calorias vaporizadas! Implacável!";
"audio.calories.smart.9" = "Sensacional! %@ calorias aniquiladas! Modo fera!";
"audio.calories.smart.10" = "Lenda! %@ calorias pulverizadas! Você é o rei!";

// Pace Smart Variations - Português motivacional
"audio.pace.smart.1" = "Perfeito! Ritmo de %@! Que controle!";
"audio.pace.smart.2" = "Eita! %@ de velocidade! Pura potência!";
"audio.pace.smart.3" = "Irado! %@ controlado! Você é uma máquina!";
"audio.pace.smart.4" = "Incrível! %@ dominado! Que técnica!";
"audio.pace.smart.5" = "Fantástico! %@ perfeito! Segue assim!";
"audio.pace.smart.6" = "Arrasou! %@ fluido! Você é fera!";
"audio.pace.smart.7" = "Show! %@ profissional! Que nível!";
"audio.pace.smart.8" = "Demais! %@ implacável! Você é fogo!";
"audio.pace.smart.9" = "Espetacular! %@ dos sonhos! Modo fera!";
"audio.pace.smart.10" = "Lenda! %@ destruidor! Você é o rei!";

// MARK: - Workout Settings
"workout.settings.title" = "Configurações de Treino";
"workout.settings.notice" = "Treino em Andamento";
"workout.settings.safe.only" = "Apenas configurações seguras para treino disponíveis";

// MARK: - Voice Premium Features
"voice.selection.simple.description" = "Escolha sua voz preferida para instruções de áudio durante suas corridas.";
"voice.premium.benefits.recommendation" = "Recomendamos adicionar vozes PREMIUM para a melhor experiência";
"voice.premium.benefit.clarity" = "Clareza de áudio superior para treinos ao ar livre";
"voice.premium.benefit.noise" = "Melhor desempenho em ambientes barulhentos";
"voice.premium.benefit.motivation" = "Tom de voz mais natural e motivador";
"voice.management.footer" = "Baixe novas vozes das Configurações do iOS e atualize para ver vozes recém-instaladas";
"voice.selection.workout.description" = "Escolha sua voz para instruções de áudio de treino. Vozes premium oferecem a experiência mais clara e motivadora durante as corridas.";

// MARK: - Speech Speed Settings
"speech_speed" = "Velocidade da Fala";
"speech_speed_footer" = "Controle a velocidade da fala das instruções de voz. Velocidades mais lentas ajudam na compreensão durante treinos intensos ou em ambientes barulhentos.";

// MARK: - Data Management
"data_management" = "Gestão de Dados";
"data_privacy" = "Privacidade de Dados";
"export_data" = "Exportar Dados";
"import_data" = "Importar Dados";
"delete_data" = "Excluir Dados";

// Privacy Messages
"privacy_ownership" = "Você mantém a propriedade dos seus dados";
"privacy_icloud_storage" = "Armazenados com segurança no seu iCloud";
"privacy_no_access" = "Nós (como desenvolvedores) não podemos acessar seus dados pessoais";
"privacy_full_control" = "Você mantém controle total dos seus dados";

// Export
"export_all_data" = "Exportar Todos os Dados";
"export_profile_only" = "Exportar Apenas Perfil";
"export_activities_only" = "Exportar Apenas Atividades";
"export_all_description" = "Perfil e atividades em 2 arquivos";
"export_profile_description" = "Configurações pessoais e preferências";
"export_activities_description" = "Todos os dados de treino e estatísticas";
"export_data_description" = "Exporte seus dados como arquivos CSV para backup ou transferência";

// Import
"import_data_files" = "Importar Arquivos de Dados";
"import_data_files_description" = "Selecione arquivos CSV exportados anteriormente";
"import_data_description" = "Restaurar de arquivos exportados anteriormente";

// Delete
"delete_all_data" = "Excluir Todos os Dados";
"delete_all_data_description" = "Remover permanentemente todos os dados";
"delete_data_description" = "Esta ação não pode ser desfeita";
"delete_confirmation" = "Confirmar Exclusão";
"delete_confirmation_message" = "Isso excluirá permanentemente todos os seus dados de perfil e atividades. Esta ação não pode ser desfeita.";
"delete_permanently" = "Excluir Permanentemente";

// Status Messages
"importing" = "Importando...";
"deleting" = "Excluindo...";
"export_failed" = "Exportação falhou";
"import_failed" = "Importação falhou";
"delete_failed" = "Exclusão falhou";
"import_success" = "Importação Bem-sucedida";
"import_success_message" = "Os dados foram importados com sucesso";
"delete_success" = "Dados Excluídos";
"delete_success_message" = "Todos os seus dados foram excluídos permanentemente. Agora você retornará à tela principal.";
"import_error" = "Erro de Importação";
"export_error" = "Erro de Exportação";
"delete_error" = "Erro de Exclusão";

"select_gender_title" = "Selecionar Gênero";

// MARK: - Activity Stats View
"act_dist" = "Dist Treino";
"act_time" = "Tempo Treino";
"act_cal" = "Cal Treino";
"performance" = "Performance";
"best_pace" = "Melhor Ritmo";
"avg_speed" = "Velocidade Média";
"max_speed" = "Velocidade Máx";
"time_analysis" = "Análise Tempo";
"total_time" = "Tempo Total";
"paused_time" = "Tempo Pausa";
"active_calories" = "Ativo";
"resting_calories" = "Repouso";
"rate" = "Taxa";
"kcal" = "kcal";
"cal_per_min" = "cal/min";
