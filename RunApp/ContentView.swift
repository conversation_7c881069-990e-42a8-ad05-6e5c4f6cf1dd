import AudioToolbox
import CoreLocation
import HealthKit
import MapKit
import RevenueCat
import RevenueCatUI
import SwiftData
import SwiftUI

struct ContentView: View {
  @StateObject private var locationManager = LocationManager.shared
  @StateObject private var routeManager = RouteManager.shared
  @StateObject private var healthKitManager = HealthKitManager.shared
  @State private var showingLocationPermissionAlert = false
  @StateObject private var metronome = MetronomeManager.shared
  @StateObject private var audioAlertManager = AudioAlertManager.shared
  @Environment(\.modelContext) private var modelContext
  @Environment(\.scenePhase) private var scenePhase
  @Query private var profile: [UserProfile]

  // Map state
  @State private var position: MapCameraPosition = .automatic
  @State private var selectedMapStyle: MapStyleChoice = .standard
  @State private var isFollowingOrientation = true
  @State private var isScreenAlwaysOn = false
  @State private var showingScreenMessage = false
  @State private var screenMessageTimer: Timer?

  // Activity state
  @State private var isRunning = false
  @State private var runStartTime: Date?
  @State private var activeRunningTime: TimeInterval = 0
  @State private var elapsedTime: TimeInterval = 0
  @State private var lastActiveTime: Date?
  @State private var timer: Timer?
  @State private var selectedSportType: SportType? = nil

  // Fixed time tracking: active time + paused time = total time
  @State private var totalPausedTime: TimeInterval = 0
  @State private var pauseStartTime: Date?

  // UI state
  @State private var selectedDetent: PresentationDetent = .height(100)
  @State private var showingCountdown = false
  @State private var showingCompleteAlert = false
  @State private var showingSettingsView = false
  @State private var showingAnalysisView = false
  @State private var showingShortWorkoutConfirmation = false
  @State private var newlySavedActivity: RunActivity?  // Added for post-save detail view
  @State private var showPostSaveDetail = false  // Added to trigger post-save detail view
  @State private var showGpsSearchMessage = false  // Added for GPS status overlay
  @State private var showPaywall = false  // Added for paywall presentation
  @State private var lastHealthKitWorkoutData: HealthKitWorkoutData?  // Phase 2: HealthKit workout data

  // Phase 4: Post-workout processing states
  @State private var isProcessingWorkout = false
  @State private var processingProgress: Double = 0.0
  @State private var processingMessage = ""

  // Pace caching for audio announcements
  @State private var lastValidPace: Double = 0
  @State private var lastValidPaceTime: Date = Date.distantPast
  private let paceValidityWindow: TimeInterval = 3.0  // 3 seconds

  // Real-time calorie tracking (incremental system)
  @State private var cumulativeCalories: Double = 0  // Active calories
  @State private var cumulativeRestingCalories: Double = 0  // Resting calories
  @State private var lastCalorieUpdateTime = Date()
  @State private var calorieUpdateTimer: Timer?

  // PHASE 3: Enhanced GPS Accuracy Properties
  @State private var recentPaces: [Double] = []
  @State private var currentPaceState: PaceState = .unknown
  @State private var enablePaceSmoothing = true

  // Phase 3 Constants
  private let paceHistorySize = 9  // 45 seconds at 5-second intervals
  private let hysteresisBuffer: Double = 1.0  // ±1 min/km buffer around 18 min/km threshold

  // Screen management and state handling
  @State private var scenePhaseDebounceTimer: Timer?
  @StateObject private var appStateManager = AppStateManager.shared
  // MARK: - Timer Cleanup

  init() {
    // Add cleanup for when view is deallocated
    // This ensures timers are properly invalidated to prevent memory leaks
  }

  // This method is called when the view is about to be deallocated
  // We use onDisappear to simulate deinit behavior for SwiftUI views
  private func cleanupTimers() {
    print("ContentView: Cleaning up timers to prevent memory leaks")

    // Clean up new timers first
    scenePhaseDebounceTimer?.invalidate()
    scenePhaseDebounceTimer = nil

    // Invalidate main timer
    timer?.invalidate()
    timer = nil

    // Invalidate calorie update timer
    calorieUpdateTimer?.invalidate()
    calorieUpdateTimer = nil

    // Invalidate screen message timer
    screenMessageTimer?.invalidate()
    screenMessageTimer = nil

    // Screen management - respect current state
    // Don't force off if user has it enabled
    if !isScreenAlwaysOn {
      UIApplication.shared.isIdleTimerDisabled = false
    }

    print("ContentView: Timer cleanup completed")
  }

  var body: some View {
    ZStack {
      // Map View
      Map(position: $position) {
        UserAnnotation()

        // REAL-TIME OPTIMIZATION: Use filtered segments directly (no simplification overhead)
        // The smart adaptive filtering system already optimizes these for real-time performance
        ForEach(0..<routeManager.completedSegments.count, id: \.self) { index in
          let segment = routeManager.completedSegments[index]
          MapPolyline(coordinates: segment.coordinates.map(\.coordinate))
            .stroke(
              segment.isPaused ? .orange.opacity(0.2) : .blue,
              style: StrokeStyle(
                lineWidth: 6))
        }

        // Draw current segment using filtered locations for instant updates
        if let currentSegment = routeManager.currentSegment {
          MapPolyline(coordinates: currentSegment.coordinates.map(\.coordinate))
            .stroke(
              routeManager.isPaused ? .orange.opacity(0.2) : .blue,
              style: StrokeStyle(
                lineWidth: 6))
        }

        // Start marker
        if let originalStart = routeManager.startLocation {
          Marker(
            NSLocalizedString("map.marker.start", comment: "Label for the start marker on map"),
            coordinate: originalStart.coordinate
          )
          .tint(.green)
        } else if let firstLocation = routeManager.completedSegments.first?.coordinates.first
          ?? routeManager.currentSegment?.coordinates.first
        {
          Marker(
            NSLocalizedString("map.marker.start", comment: "Label for the start marker on map"),
            coordinate: firstLocation.coordinate
          )
          .tint(.green)
        }
      }
      .id(
        "map_\(routeManager.completedSegments.count)_\(routeManager.currentSegment != nil)_\(routeManager.startLocation != nil)"
      )  // Force view to re-evaluate when route data changes
      .mapStyle(selectedMapStyle.mapStyle)
      .mapControls {
        MapCompass()
          .mapControlVisibility(.visible)
      }

      // Overlays
      MapControls(
        selectedMapStyle: $selectedMapStyle,
        isFollowingOrientation: $isFollowingOrientation,
        onCenterLocation: {
          // Request immediate location update
          locationManager.requestImmediateLocation()
          // Center map on current location if available
          if let location = locationManager.location {
            updateMapPosition(for: location)
          }
        }
      )

      // Main Controls
      VStack {
        Spacer()

        ControlsOverlay(
          isRunning: $isRunning,
          isPaused: .init(
            get: { routeManager.isPaused },
            set: { isPaused in
              if isPaused {
                pauseRun()
              } else {
                resumeRun()
              }
            }
          ),
          selectedDetent: $selectedDetent,
          selectedSportType: $selectedSportType,
          showingCountdown: $showingCountdown,
          showingAnalysisView: $showingAnalysisView,
          showingCompleteAlert: $showingCompleteAlert,
          distance: calculateDistance(),
          elapsedTime: elapsedTime,  // ✅ TIMER FIX: Use timer-based elapsed time for consistent UI
          activeTime: calculateActiveTime(),
          recentPace: calculateRecentPace(),
          calories: cumulativeCalories,  // Fixed: show real-time calories
          onPause: pauseRun,
          onResume: resumeRun,
          onShowSettings: { showingSettingsView = true }
        )
      }

      // Settings and Screen Control Buttons
      VStack {
        Spacer()
        HStack {
          // Screen Always On Toggle
          Button(action: handleScreenToggle) {
            Circle()
              .fill(.black)
              .frame(width: 35, height: 35)
              .shadow(color: Color.black.opacity(0.5), radius: 5, x: 0, y: 3)
              .overlay(
                Image(systemName: isScreenAlwaysOn ? "lightbulb.max.fill" : "lightbulb.slash")
                  .font(.system(size: 18, weight: .bold))
                  .foregroundColor(isScreenAlwaysOn ? .yellow : .white)
              )
          }
          .padding(.leading, 20)
          .padding(.bottom, -10)

          Spacer()

          // Settings Button
          if !isRunning {
            Button(action: { showingSettingsView = true }) {
              Circle()
                .fill(.black)
                .frame(width: 35, height: 35)
                .shadow(color: Color.black.opacity(0.5), radius: 5, x: 0, y: 3)
                .overlay(
                  Image(systemName: "gearshape.fill")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                )
            }
            .padding(.trailing, 20)
            .padding(.bottom, -10)
          }
        }
      }

      // Metronome Overlay Button (only shown during activity)
      if isRunning {
        VStack {
          Spacer()
          HStack {
            Spacer()
            MetronomeButton(inWorkoutMode: true, isPaused: routeManager.isPaused)
              .padding(.trailing, 20)
              .padding(.bottom, -10)
          }
        }
      }

      if showingCountdown {
        CountdownView(isPresented: $showingCountdown) {
          checkAndStartRun()
        }
      }

      MetronomeMessageOverlay(metronome: metronome)
      ScreenMessageOverlay(isVisible: showingScreenMessage, isScreenOn: isScreenAlwaysOn)
      GpsStatusOverlay(
        isVisible: $showGpsSearchMessage, hasStartLocation: routeManager.startLocation != nil
      )  // Added GPS status overlay
      .zIndex(1)  // Ensure it's above the map

      // Phase 4: Post-workout processing overlay
      if isProcessingWorkout {
        WorkoutProcessingOverlay(
          progress: processingProgress,
          message: processingMessage
        )
        .zIndex(2)  // Above all other overlays
      }
    }
    .animation(.easeInOut, value: routeManager.isPaused)
    .task {
      // Clear any lingering route data from previous sessions
      routeManager.resetAllRouteData()

      // Start basic location tracking immediately to improve startup time
      locationManager.startTracking()

      // Also start preloading high-accuracy locations for quick start
      locationManager.startLocationPreloading()

      // Initialize metronome manager with user profile (one-time setup)
      if let userProfile = profile.first {
        metronome.loadSettingsFromProfile(userProfile)
      }

      // Initialize audio alert manager with user profile
      if let userProfile = profile.first {
        audioAlertManager.updateUserProfile(userProfile)
      }

      // Check location permission status on app start
      locationManager.checkLocationPermission()
      if locationManager.needsAlwaysPermission {
        showingLocationPermissionAlert = true
      }
    }
    .onChange(of: locationManager.needsAlwaysPermission) { _, needsPermission in
      showingLocationPermissionAlert = needsPermission
    }
    .onChange(of: locationManager.isTracking) { oldValue, newValue in
      // If tracking stops unexpectedly during a workout, try to recover it
      if oldValue && !newValue && isRunning && !routeManager.isPaused {
        recoverWorkoutIfNeeded()
      }
    }
    .onChange(of: locationManager.isInWorkoutMode) { oldValue, newValue in
      // Similar recovery logic for workout mode changes
      if oldValue && !newValue && isRunning && !routeManager.isPaused {
        recoverWorkoutIfNeeded()
      }
    }
    .onChange(of: isRunning) { _, newIsRunning in
      // Existing logic first - preserve sport type handling
      if !newIsRunning && !showingShortWorkoutConfirmation {
        selectedSportType = nil
      }

      // New logic - notify AppStateManager about workout mode changes
      if newIsRunning {
        appStateManager.enterWorkoutMode()
      }
    }
    .onChange(of: locationManager.location) { _, newLocation in
      if let newLocation = newLocation {
        handleLocationUpdate(newLocation)
      }
    }
    .onChange(of: profile) { _, newProfile in
      if let userProfile = newProfile.first {
        // Update audio alert manager when profile changes
        audioAlertManager.updateUserProfile(userProfile)
        // Update metronome manager when profile changes
        metronome.updateUserProfile(userProfile)
      }
    }
    .onChange(of: routeManager.startLocation) { _, newStartLocation in  // Added listener for start location
      if newStartLocation != nil {
        // Once we have a valid start location, hide the message
        showGpsSearchMessage = false
      }
    }
    .onChange(of: locationManager.location) { _, newLocation in
      // ✅ Enhanced GPS status logic
      if isRunning && !routeManager.isPaused {
        let hasGoodGPS = checkGPSQuality()
        let hasStartLocation = routeManager.startLocation != nil

        if !hasGoodGPS && !hasStartLocation {
          // Show "Searching for GPS..." when no start location and poor GPS
          if !showGpsSearchMessage {
            print("📍 GPS STATUS: Showing 'Searching for GPS...' - no start location, poor GPS")
          }
          showGpsSearchMessage = true
        } else if !hasGoodGPS && hasStartLocation {
          // Show "Poor GPS Signal" when we have start location but current GPS is poor
          if !showGpsSearchMessage {
            print("📍 GPS STATUS: Showing 'Poor GPS Signal' - has start location, poor GPS")
          }
          showGpsSearchMessage = true
        } else {
          // Hide message when GPS is good
          if showGpsSearchMessage {
            print("📍 GPS STATUS: Hiding GPS message - GPS quality is good")
          }
          showGpsSearchMessage = false
        }
      } else {
        // Hide GPS message when not running
        if showGpsSearchMessage {
          print("📍 GPS STATUS: Hiding GPS message - not running or paused")
        }
        showGpsSearchMessage = false
      }
    }
    .onChange(of: scenePhase) { _, newPhase in
      // Cancel any pending debounce timer
      scenePhaseDebounceTimer?.invalidate()

      if newPhase == .active {
        // App came to foreground
        handleForegroundTransition()
      } else if newPhase == .background {
        // App went to background
        handleBackgroundTransition()
      } else if newPhase == .inactive {
        // Screen turned off or app became inactive - debounce to avoid rapid fire
        scenePhaseDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
          handleScreenOffTransition()
        }
      }
    }
    .alert(
      NSLocalizedString("alert.location.title", comment: "Title for location permission alert"),
      isPresented: $showingLocationPermissionAlert
    ) {
      Button(NSLocalizedString("alert.location.settings", comment: "Button to open settings")) {
        if let url = URL(string: UIApplication.openSettingsURLString) {
          UIApplication.shared.open(url)
        }
      }
      Button(
        NSLocalizedString("alert.location.later", comment: "Button to dismiss alert"), role: .cancel
      ) {}
    } message: {
      Text(
        NSLocalizedString(
          "alert.location.message", comment: "Message explaining why location permission is needed")
      )
    }
    .alert(
      NSLocalizedString("alert.complete.title", comment: "Title for activity completion alert"),
      isPresented: $showingCompleteAlert
    ) {
      Button(
        NSLocalizedString(
          "alert.complete.confirm", comment: "Button to confirm activity completion"),
        role: .destructive
      ) {
        Task {
          await completeRun()
        }
      }
      Button(
        NSLocalizedString("alert.complete.cancel", comment: "Button to cancel activity completion"),
        role: .cancel
      ) {}
    } message: {
      Text(
        NSLocalizedString(
          "alert.complete.message", comment: "Message explaining activity completion"))
    }
    .sheet(isPresented: $showingSettingsView) {
      if appStateManager.isInWorkoutMode {
        WorkoutSettingsView()
      } else {
        SettingsView()
      }
    }
    .sheet(isPresented: $showingAnalysisView) {
      AnalysisView()
    }
    .alert(
      NSLocalizedString(
        "alert.shortWorkout.title", comment: "Title for short workout confirmation"),
      isPresented: $showingShortWorkoutConfirmation
    ) {
      Button(
        NSLocalizedString("alert.shortWorkout.save", comment: "Button to save short workout"),
        role: .destructive
      ) {
        saveActivity()
        resetState()
      }
      Button(
        NSLocalizedString("alert.shortWorkout.discard", comment: "Button to discard short workout"),
        role: .cancel
      ) {
        resetState()
      }
    } message: {
      Text(
        NSLocalizedString(
          "alert.shortWorkout.message",
          comment: "Message asking user to confirm saving short workout"))
    }
    .sheet(
      isPresented: $showPostSaveDetail,
      onDismiss: {
        resetState()  // Reset state AFTER sheet is dismissed
        newlySavedActivity = nil  // Clear the temporary holder
      }
    ) {
      if let activityToShow = newlySavedActivity {
        NavigationView {
          ActivityDetailView(activity: activityToShow)
            .navigationTitle(
              NSLocalizedString(
                "activity.summary.title", comment: "Title for the activity summary view")
            )
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
              ToolbarItem(placement: .navigationBarLeading) {
                Button {
                  showPostSaveDetail = false
                } label: {  // Dismiss button
                  Image(systemName: "xmark")
                    .foregroundColor(.primary)  // Ensure icon uses appropriate color
                }
              }
            }
        }
      } else {
        // Optional fallback if activity is nil
        Text("Loading details...")
      }
    }
    .fullScreenCover(isPresented: $showPaywall) {
      // Use the standard RevenueCat PaywallView or your custom one
      PaywallView()
      // Optional: Add environment objects if PaywallView needs them
    }
    .onDisappear {
      // Clean up timers when view disappears to prevent memory leaks
      cleanupTimers()
    }
  }

  // MARK: - Private Methods

  private func handleLocationUpdate(_ newLocation: CLLocation?) {
    // Only update map position if in foreground
    if let newLocation = newLocation, scenePhase == .active {
      updateMapPosition(for: newLocation)
    }
  }

  private func updateMapPosition(for location: CLLocation) {
    withAnimation {
      position = .camera(
        MapCamera(
          centerCoordinate: location.coordinate,
          distance: 1000,
          heading: isFollowingOrientation ? (locationManager.orientation ?? 0) : 0,
          pitch: 0
        ))
    }
  }

  private func checkAndStartRun() {
    // --- Paywall Check START ---
    let userProfile = profile.first  // Get the user profile
    let hasExceeded = userProfile?.hasExceededTrialLimit ?? false

    Task {  // Use Task for async operation
      do {
        let customerInfo = try await Purchases.shared.customerInfo()
        // Ensure "Pro" matches your actual RevenueCat entitlement identifier
        let hasProEntitlement = customerInfo.entitlements.active.keys.contains("Pro")

        // --- DEBUG LOGGING START ---
        print(
          "[Debug Paywall Check] hasExceeded: \(hasExceeded), hasProEntitlement: \(hasProEntitlement)"
        )
        // --- DEBUG LOGGING END ---

        if hasExceeded && !hasProEntitlement {
          print(
            "Paywall triggered: Trial exceeded (\(hasExceeded)) and no Pro entitlement (\(!hasProEntitlement))."
          )
          showPaywall = true
          showingCountdown = false  // Ensure countdown is hidden if paywall is shown
          return  // Stop execution if paywall needs to be shown
        } else {
          // Proceed with starting the run if check passes
          proceedWithRunStart()
        }
      } catch {
        print("Error fetching customer info: \(error)")
        // Handle error: Prevent starting if entitlement check fails after trial exceeded.
        if hasExceeded {
          showingCountdown = false
          // Consider adding an alert here to inform the user about the check failure.
          // For now, just prevent the run start.
          return
        } else {
          // If trial not exceeded, allow run start even if check fails
          proceedWithRunStart()
        }
      }
    }
    // --- Paywall Check END ---
  }

  // Extracted original run start logic into a separate function
  private func proceedWithRunStart() {
    // --- DEBUG LOGGING START ---
    print("[Debug Paywall Check] Entering proceedWithRunStart()")
    // --- DEBUG LOGGING END ---

    // Recheck permission status
    locationManager.checkLocationPermission()

    // Show permission alert if needed
    if locationManager.needsAlwaysPermission {
      showingLocationPermissionAlert = true
      showingCountdown = false  // Ensure countdown is hidden
      return
    }

    // Show GPS message if start location isn't ready yet
    if routeManager.startLocation == nil {
      showGpsSearchMessage = true
    }

    // Start HealthKit workout session first (provides background priority)
    Task { @MainActor in
      do {
        let activityType = selectedSportType ?? .run

        // Phase 4: Monitor HealthKit workout start performance
        PerformanceMonitor.shared.startTiming("healthkit_workout_start")
        try await healthKitManager.startWorkout(activityType: activityType)
        PerformanceMonitor.shared.endTiming("healthkit_workout_start")

        print("HealthKit workout session started successfully")
      } catch {
        PerformanceMonitor.shared.endTiming("healthkit_workout_start")
        print("Failed to start HealthKit workout session: \(error)")
        // Continue with workout even if HealthKit fails
      }
    }

    // Start run if permissions are granted
    isRunning = true
    runStartTime = Date()
    lastActiveTime = Date()
    activeRunningTime = 0
    elapsedTime = 0

    // Fixed time tracking: initialize pause tracking variables
    totalPausedTime = 0
    pauseStartTime = nil

    // Get the best preloaded location if available, otherwise use current location
    let bestStartLocation = locationManager.getBestPreloadedLocation() ?? locationManager.location

    // Stop preloading now that we're starting the workout
    locationManager.stopLocationPreloading()

    // Start route manager with the best available location
    routeManager.startTracking(initialLocation: bestStartLocation)

    // Start tracking in workout mode (now with HealthKit background priority)
    locationManager.startTracking(isWorkout: true)

    startTimer()
    metronome.handleActivityStart()

    // Initialize audio alerts for the workout
    audioAlertManager.resetAlertCounters()

    // Initialize optimized real-time calorie tracking
    resetRealTimeCalories()
    startCalorieUpdateTimer()
  }

  private func pauseRun() {
    print(
      "🔥 PAUSE DEBUG: pauseRun() called - current resting calories: \(cumulativeRestingCalories)")

    // Pause HealthKit workout session
    Task { @MainActor in
      do {
        try await healthKitManager.pauseWorkout()
        print("HealthKit workout session paused successfully")
      } catch {
        print("Failed to pause HealthKit workout session: \(error)")
      }
    }

    locationManager.pauseTracking()
    timer?.invalidate()
    timer = nil

    // FIXED: Keep calorie update timer running during pause to accumulate resting calories
    // stopCalorieUpdateTimer() // REMOVED: Timer must continue during pause for resting calories
    print("🔥 PAUSE DEBUG: Calorie timer kept running for resting calorie accumulation")

    // Fixed time tracking: accumulate active time and start tracking pause time
    if let lastActive = lastActiveTime {
      activeRunningTime += Date().timeIntervalSince(lastActive)
    }
    lastActiveTime = nil

    // Start tracking pause time
    pauseStartTime = Date()
    print("🔥 PAUSE DEBUG: Pause tracking started at \(pauseStartTime!)")

    // Pause route tracking
    routeManager.pauseTracking()

    metronome.handleActivityPause()
    // Audio alerts automatically pause when isRunning or !routeManager.isPaused in checkAudioAlerts()
  }

  private func resumeRun() {
    print(
      "🔥 RESUME DEBUG: resumeRun() called - current resting calories: \(cumulativeRestingCalories)")

    // Resume HealthKit workout session
    Task { @MainActor in
      do {
        try await healthKitManager.resumeWorkout()
        print("HealthKit workout session resumed successfully")
      } catch {
        print("Failed to resume HealthKit workout session: \(error)")
      }
    }

    // Fixed time tracking: accumulate paused time before resuming
    if let pauseStart = pauseStartTime {
      let pauseDuration = Date().timeIntervalSince(pauseStart)
      totalPausedTime += pauseDuration
      print(
        "🔥 RESUME DEBUG: Accumulated pause duration: \(String(format: "%.1f", pauseDuration))s, total paused time: \(String(format: "%.1f", totalPausedTime))s"
      )
    }
    pauseStartTime = nil

    locationManager.resumeTracking()
    lastActiveTime = Date()

    // Resume route tracking
    routeManager.resumeTracking()

    startTimer()

    // Reset calorie update time to prevent big jump after pause
    lastCalorieUpdateTime = Date()
    // FIXED: Don't restart calorie timer since it's already running during pause
    // startCalorieUpdateTimer() // REMOVED: Timer is already running
    print(
      "🔥 RESUME DEBUG: Calorie timer continues running - resting calories preserved: \(cumulativeRestingCalories)"
    )

    metronome.handleActivityResume()
    // Audio alerts automatically resume when timer starts and checkAudioAlerts() is called
  }

  private func completeRun() async {
    isRunning = false

    // Exit workout mode immediately to ensure proper settings view selection
    appStateManager.exitWorkoutMode()

    timer?.invalidate()
    timer = nil

    // Stop calorie update timer
    stopCalorieUpdateTimer()

    metronome.handleActivityEnd()
    // Audio alerts automatically stop when timer is invalidated and isRunning = false

    // Phase 4: Show processing overlay
    await MainActor.run {
      isProcessingWorkout = true
      processingProgress = 0.1
      processingMessage = "Finalizing workout session..."
    }

    // End HealthKit workout session with enhanced post-workout processing
    do {
      let totalDistance = calculateDistance()
      let totalDistanceKm = totalDistance / 1000
      let totalMinutes = activeRunningTime / 60
      let averagePace = totalDistanceKm > 0 ? totalMinutes / totalDistanceKm : 0

      // Update progress
      await MainActor.run {
        processingProgress = 0.3
        processingMessage = "Calculating workout metrics..."
      }

      // Build route coordinates for HealthKit
      let routeCoordinates = routeManager.completedSegments.flatMap { segment in
        segment.coordinates.map { location in
          Coordinate(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude,
            altitude: location.altitude,
            timestamp: location.timestamp,
            isPaused: segment.isPaused,
            speed: location.speed >= 0 ? location.speed : nil
          )
        }
      }

      // Update progress
      await MainActor.run {
        processingProgress = 0.5
        processingMessage = "Syncing with Apple Health..."
      }

      // Phase 2: Enhanced workout completion with HealthKit data retrieval
      PerformanceMonitor.shared.startTiming("healthkit_workout_end")
      let healthKitData = try await healthKitManager.endWorkout(
        distance: totalDistance, averagePace: averagePace, route: routeCoordinates)
      PerformanceMonitor.shared.endTiming("healthkit_workout_end")
      print("HealthKit workout session ended successfully")
      print(
        "HealthKit provided: \(healthKitData.totalEnergyBurned) calories, \(healthKitData.duration) seconds duration"
      )

      // Update progress
      await MainActor.run {
        processingProgress = 0.8
        processingMessage = "Processing route data..."
      }

      // Store HealthKit data for enhanced activity saving
      self.lastHealthKitWorkoutData = healthKitData

    } catch {
      print("Failed to end HealthKit workout session: \(error)")
      // Continue with workout completion even if HealthKit fails
      self.lastHealthKitWorkoutData = nil

      await MainActor.run {
        processingProgress = 0.7
        processingMessage = "Completing workout without HealthKit sync..."
      }
    }

    // Final progress update
    await MainActor.run {
      processingProgress = 1.0
      processingMessage = "Workout completed!"
    }

    // Small delay to show completion
    try? await Task.sleep(nanoseconds: 500_000_000)  // 0.5 seconds

    // Hide processing overlay
    await MainActor.run {
      isProcessingWorkout = false
    }

    // End workout mode but keep tracking in standard mode
    locationManager.endWorkoutMode()

    // Stop route tracking
    routeManager.stopTracking()

    // Update final active running time
    if !routeManager.isPaused, let lastActive = lastActiveTime {
      activeRunningTime += Date().timeIntervalSince(lastActive)
    }

    // Check if workout is less than 30 seconds
    if activeRunningTime < 30 {
      // Show confirmation dialog instead of immediately saving
      showingShortWorkoutConfirmation = true
    } else {
      // Save activity without confirmation if longer than 30 seconds
      saveActivity()
      // resetState() // Removed: Will be called on sheet dismiss
    }
  }

  private func resetState() {
    runStartTime = nil
    lastActiveTime = nil
    activeRunningTime = 0
    elapsedTime = 0

    // Fixed time tracking: reset pause tracking variables
    totalPausedTime = 0
    pauseStartTime = nil

    // Ensure workout mode is exited when state is reset
    appStateManager.exitWorkoutMode()

    // Reset real-time calorie tracking
    resetRealTimeCalories()
    stopCalorieUpdateTimer()

    // Clean up screen message timer
    screenMessageTimer?.invalidate()
    screenMessageTimer = nil
    showingScreenMessage = false

    // Ensure route data is cleared when workout is completed
    routeManager.resetAllRouteData()

    // Hide GPS message on reset
    showGpsSearchMessage = false
  }

  private func startTimer() {
    // ✅ CRITICAL FIX: Ensure timer runs on main thread and schedule on main run loop
    timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
      // ✅ SWIFT 6 FIX: Use Task wrapper to safely access main actor properties
      Task { @MainActor in
        // ✅ CRITICAL FIX: Always increment elapsed time consistently when not paused
        // This ensures smooth 1-second progression regardless of GPS quality
        if !routeManager.isPaused {
          elapsedTime += 1

          // Log timer progression for debugging (every 5 seconds to track more closely)
          if Int(elapsedTime) % 5 == 0 {
            let hasGoodGPS = checkGPSQuality()
            print(
              "⏱️ TIMER: Consistent increment - elapsed=\(String(format: "%.0f", elapsedTime))s, GPS=\(hasGoodGPS ? "GOOD" : "POOR")"
            )
          }

          // Log every second for first 15 seconds to verify smooth progression
          if elapsedTime <= 15 {
            print("⏱️ TIMER: Second-by-second - elapsed=\(String(format: "%.0f", elapsedTime))s")
          }
        } else {
          if Int(elapsedTime) % 10 == 0 {  // Log every 10 seconds during pause
            print(
              "⏱️ TIMER: Paused - elapsed=\(String(format: "%.0f", elapsedTime))s (no increment)")
          }
        }

        // Check and trigger audio alerts every second
        checkAudioAlerts()
      }
    }

    // ✅ CRITICAL FIX: Ensure timer is added to main run loop with common modes
    RunLoop.main.add(timer!, forMode: .common)
    print(
      "⏱️ TIMER: Started with 1-second interval - FIXED for consistent progression on main run loop")
  }

  private func calculateDistance() -> Double {
    var distance = 0.0
    for segment in routeManager.completedSegments where !segment.isPaused {
      distance += calculateSegmentDistance(segment.coordinates)
    }
    if isRunning && !routeManager.isPaused, let currentSegment = routeManager.currentSegment {
      distance += calculateSegmentDistance(currentSegment.coordinates)
    }
    return distance
  }

  private func calculateSegmentDistance(_ coordinates: [CLLocation]) -> Double {
    guard coordinates.count > 1 else { return 0 }
    var distance = 0.0
    for i in 0..<(coordinates.count - 1) {
      distance += coordinates[i + 1].distance(from: coordinates[i])
    }
    return distance
  }

  private func calculateActiveTime() -> TimeInterval {
    var totalTime = activeRunningTime
    if !routeManager.isPaused, let lastActive = lastActiveTime {
      totalTime += Date().timeIntervalSince(lastActive)
    }
    return totalTime
  }

  // Fixed time tracking: calculate total elapsed time (active + paused)
  private func calculateTotalElapsedTime() -> TimeInterval {
    guard let startTime = runStartTime else { return 0 }

    let activeTime = calculateActiveTime()
    var pausedTime = totalPausedTime

    // Add current pause time if currently paused
    if routeManager.isPaused, let pauseStart = pauseStartTime {
      pausedTime += Date().timeIntervalSince(pauseStart)
    }

    return activeTime + pausedTime
  }

  private func calculateRecentPace() -> TimeInterval {
    let logManager = LogManager.shared

    // Use RAW GPS data from LocationManager instead of filtered data from RouteManager
    guard let lastLocation = locationManager.location,
      routeManager.isTracking && !routeManager.isPaused
    else {
      logManager.logPace(
        rawSpeed: -1, calculatedPace: 0, isValid: false, source: "calculateRecentPace",
        additionalInfo: "No location data or not tracking")
      return 0
    }

    let now = Date()

    // Check if location data is too old (accounts for location filtering system)
    let locationAge = now.timeIntervalSince(lastLocation.timestamp)
    let maxLocationAge: TimeInterval = 3.0

    if locationAge > maxLocationAge {
      // Location is too old, return 0 for "--:--" display
      logManager.logPace(
        rawSpeed: lastLocation.speed, calculatedPace: 0, isValid: false,
        source: "calculateRecentPace",
        additionalInfo: "Location too old (\(String(format: "%.1f", locationAge))s)")
      return 0
    }

    // Use CoreLocation's speed when available and valid
    let speed = lastLocation.speed
    if speed > 0 {  // CoreLocation returns negative values when speed is invalid
      let paceMinPerKm = (1000.0 / speed) / 60.0  // Convert m/s to min/km

      // Validate pace range (0.3 to 30 min/km for realistic running/walking paces)
      let isValid = paceMinPerKm >= 0.3 && paceMinPerKm <= 30.0

      if isValid {
        // Cache the valid pace with timestamp
        lastValidPace = paceMinPerKm
        lastValidPaceTime = Date()

        logManager.logPace(
          rawSpeed: speed, calculatedPace: paceMinPerKm, isValid: true,
          source: "calculateRecentPace", additionalInfo: "Fresh GPS pace")
        return paceMinPerKm
      } else {
        logManager.logPace(
          rawSpeed: speed, calculatedPace: paceMinPerKm, isValid: false,
          source: "calculateRecentPace", additionalInfo: "Pace out of valid range")
      }
    } else {
      logManager.logPace(
        rawSpeed: speed, calculatedPace: 0, isValid: false, source: "calculateRecentPace",
        additionalInfo: "Invalid GPS speed")
    }

    // Try to use cached pace if it's recent (within 3 seconds)
    let timeSinceLastValidPace = now.timeIntervalSince(lastValidPaceTime)

    if timeSinceLastValidPace <= paceValidityWindow && lastValidPace > 0 {
      logManager.logPace(
        rawSpeed: speed, calculatedPace: lastValidPace, isValid: true,
        source: "calculateRecentPace",
        additionalInfo: "Using cached pace (\(String(format: "%.1f", timeSinceLastValidPace))s old)"
      )
      return lastValidPace
    }

    // No valid current or cached pace available
    logManager.logPace(
      rawSpeed: speed, calculatedPace: 0, isValid: false, source: "calculateRecentPace",
      additionalInfo:
        "No valid pace available (cache age: \(String(format: "%.1f", timeSinceLastValidPace))s)")
    return 0
  }

  private func checkAudioAlerts() {
    // ✅ ENHANCED TIMER VALIDATION: Log timer execution for first 15 seconds
    if elapsedTime <= 15 {
      print(
        "⏱️ TIMER EXECUTION: checkAudioAlerts called at elapsed=\(String(format: "%.0f", elapsedTime))s, isRunning=\(isRunning), isPaused=\(routeManager.isPaused)"
      )
    }

    guard isRunning && !routeManager.isPaused else {
      // Only log this occasionally to avoid spam
      if Int(elapsedTime) % 30 == 0 {  // Log every 30 seconds
        LogManager.shared.log(
          "checkAudioAlerts skipped - isRunning: \(isRunning), isPaused: \(routeManager.isPaused)",
          category: .audio)
      }
      return
    }

    let currentDistance = calculateDistance()
    let currentTime = calculateActiveTime()
    let currentCalories = cumulativeCalories  // Use optimized cumulative calories
    let currentPace = calculateRecentPace()

    // ✅ NEW: Comprehensive status summary every 30 seconds for validation
    if Int(elapsedTime) % 30 == 0 {
      let hasGoodGPS = checkGPSQuality()
      let totalCalories = cumulativeCalories + cumulativeRestingCalories
      let calorieRate = totalCalories / max(currentTime / 3600.0, 0.01)  // cal/hour

      print(
        "📊 STATUS SUMMARY (30s): elapsed=\(String(format: "%.0f", elapsedTime))s, active=\(String(format: "%.0f", currentTime))s, distance=\(String(format: "%.0f", currentDistance))m, pace=\(String(format: "%.1f", currentPace)), GPS=\(hasGoodGPS ? "GOOD" : "POOR")"
      )
      print(
        "📊 CALORIE SUMMARY: active=\(String(format: "%.1f", cumulativeCalories)), resting=\(String(format: "%.1f", cumulativeRestingCalories)), total=\(String(format: "%.1f", totalCalories)), rate=\(String(format: "%.0f", calorieRate))cal/h"
      )

      // ✅ Run validation to check for anomalous patterns
      validateCalorieAccumulation()

      LogManager.shared.log(
        "Status Summary - elapsed: \(String(format: "%.0f", elapsedTime))s, active: \(String(format: "%.0f", currentTime))s, calories: \(String(format: "%.1f", totalCalories)), GPS: \(hasGoodGPS ? "GOOD" : "POOR")",
        category: .audio)
    }

    // Log every 10 seconds to track timer execution without spam
    if Int(elapsedTime) % 10 == 0 {
      LogManager.shared.log(
        "checkAudioAlerts timer tick - elapsed: \(String(format: "%.1f", elapsedTime))s, active: \(String(format: "%.1f", currentTime))s, calories: \(String(format: "%.1f", currentCalories))",
        category: .audio)
    }

    audioAlertManager.checkAndTriggerAlerts(
      distance: currentDistance,
      duration: currentTime,
      calories: currentCalories,
      pace: currentPace
    )
  }

  private func calculateCalories() -> Double {
    return CalorieCalculator.calculateCalories(
      weight: profile.first?.weight,
      sportType: selectedSportType ?? .run,
      activeTime: calculateActiveTime(),
      distance: calculateDistance()
    )
  }

  private func saveActivity() {
    guard let startTime = runStartTime else { return }

    // === CALORIE DEBUG LOGGING START ===
    print("🔥 CALORIE DEBUG: Starting saveActivity()")
    print("🔥 cumulativeCalories (active): \(cumulativeCalories)")
    print("🔥 cumulativeRestingCalories (resting): \(cumulativeRestingCalories)")
    print("🔥 Total real-time calories: \(cumulativeCalories + cumulativeRestingCalories)")
    print("🔥 activeRunningTime: \(activeRunningTime)")
    // === CALORIE DEBUG LOGGING END ===

    let locationName =
      routeManager.completedSegments.first?.coordinates.first.map { location in
        // For simplicity, just using a default name. In practice, you'd use geocoding.
        NSLocalizedString(
          "map.location.unknown", comment: "Default location name when geocoding is not available")
      }
      ?? NSLocalizedString(
        "map.location.unknown", comment: "Default location name when geocoding is not available")

    let totalDistance = calculateDistance()
    let totalDistanceKm = totalDistance / 1000
    let totalMinutes = activeRunningTime / 60
    let _ = totalDistanceKm > 0 ? totalMinutes / totalDistanceKm : 0

    // --- ENHANCED OPTIMIZATION: Build raw coordinates with altitude data ---
    // BUGFIX: Handle case where user immediately finishes workout with no route data
    var rawCoordinates = routeManager.completedSegments.flatMap { segment in
      segment.coordinates.map { location in
        Coordinate(
          latitude: location.coordinate.latitude,
          longitude: location.coordinate.longitude,
          altitude: location.altitude,  // ✅ NOW INCLUDES ALTITUDE
          timestamp: location.timestamp,
          isPaused: segment.isPaused,
          speed: location.speed >= 0 ? location.speed : nil
        )
      }
    }

    // BUGFIX: If no coordinates from segments, check current segment and create minimal data
    if rawCoordinates.isEmpty {
      // Try to get at least one coordinate from current segment or start location
      if let currentSegment = routeManager.currentSegment, !currentSegment.coordinates.isEmpty {
        rawCoordinates = currentSegment.coordinates.map { location in
          Coordinate(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude,
            altitude: location.altitude,
            timestamp: location.timestamp,
            isPaused: false,  // Current segment is typically active
            speed: location.speed >= 0 ? location.speed : nil
          )
        }
        print("🔧 BUGFIX: Using current segment coordinates (\(rawCoordinates.count) points)")
      } else if let startLocation = routeManager.startLocation {
        // Last resort: create a single coordinate from start location
        rawCoordinates = [
          Coordinate(
            latitude: startLocation.coordinate.latitude,
            longitude: startLocation.coordinate.longitude,
            altitude: startLocation.altitude,
            timestamp: startLocation.timestamp,
            isPaused: false,
            speed: startLocation.speed >= 0 ? startLocation.speed : nil
          )
        ]
        print("🔧 BUGFIX: Using single start location coordinate")
      } else {
        // Ultimate fallback: create a dummy coordinate to prevent crashes
        let now = Date()
        rawCoordinates = [
          Coordinate(
            latitude: 0.0,
            longitude: 0.0,
            altitude: 0.0,
            timestamp: now,
            isPaused: false,
            speed: nil
          )
        ]
        print("🔧 BUGFIX: Using dummy coordinate to prevent crash - zero distance workout")
      }
    }

    // --- ENHANCED OPTIMIZATION: Use new optimized initializer ---
    // This automatically:
    // 1. Calculates comprehensive stats from raw data
    // 2. Applies Douglas-Peucker simplification
    // 3. Stores simplified coordinates instead of raw GPS data
    // 4. Pre-calculates ALL expensive operations

    // BUGFIX: Ensure weight is valid to prevent calorie calculation crashes
    let userWeight = profile.first?.weight ?? 70.0
    let safeWeight = userWeight > 0 ? userWeight : 70.0  // Fallback to 70kg if invalid

    // BUGFIX: Ensure activeRunningTime is non-negative
    let safeActiveTime = max(activeRunningTime, 0.0)

    print(
      "🔧 BUGFIX: Using safe weight: \(safeWeight)kg, activeTime: \(safeActiveTime)s, coordinates: \(rawCoordinates.count)"
    )

    let activity = RunActivity(
      rawCoordinates: rawCoordinates,
      simplificationTolerance: 0.0001,  // ~11 meters tolerance
      startTime: startTime,
      endTime: Date(),
      location: locationName,
      weight: safeWeight,
      activeRunningTime: safeActiveTime,
      sportType: selectedSportType ?? .run
    )

    // Fixed: Use real-time tracked calories instead of auto-calculated ones
    // Override the workout stats with our real-time tracked active + resting calories
    if let workoutStats = activity.storedWorkoutStats {
      // Check if real-time calories are meaningful (> 1 calorie total)
      let realTimeTotal = cumulativeCalories + cumulativeRestingCalories

      let finalActiveCalories: Double
      let finalRestingCalories: Double
      let finalTotalCalories: Double

      if realTimeTotal > 1.0 {
        // Use real-time tracked calories
        finalActiveCalories = cumulativeCalories
        finalRestingCalories = cumulativeRestingCalories
        finalTotalCalories = realTimeTotal
        print(
          "Using real-time calories - Active: \(finalActiveCalories), Resting: \(finalRestingCalories), Total: \(finalTotalCalories)"
        )
      } else {
        // Fallback to coordinate-based calculation if real-time is too small
        finalActiveCalories = workoutStats.activeCalories
        finalRestingCalories = workoutStats.pauseCalories
        finalTotalCalories = workoutStats.totalCalories
        print(
          "Using coordinate-based calories (real-time too small) - Active: \(finalActiveCalories), Resting: \(finalRestingCalories), Total: \(finalTotalCalories)"
        )
      }

      // Create updated workout stats with final calorie values
      let updatedStats = WorkoutStats(
        averagePace: workoutStats.averagePace,
        bestPace: workoutStats.bestPace,
        averageSpeed: workoutStats.averageSpeed,
        maxSpeed: workoutStats.maxSpeed,
        totalTime: workoutStats.totalTime,
        activeTime: workoutStats.activeTime,
        pauseTime: workoutStats.pauseTime,
        activeCalories: finalActiveCalories,
        pauseCalories: finalRestingCalories,
        totalCalories: finalTotalCalories,
        metricLaps: workoutStats.metricLaps,
        imperialLaps: workoutStats.imperialLaps
      )
      activity.storedWorkoutStats = updatedStats
      activity.storedCalories = finalTotalCalories  // Store total calories
    } else {
      // Fallback if no workout stats exist
      let fallbackTotal = max(cumulativeCalories + cumulativeRestingCalories, 1.0)
      activity.storedCalories = fallbackTotal
      print("No workout stats available, using fallback total: \(fallbackTotal)")
    }

    // Phase 2: Enhanced activity saving with HealthKit data
    if let healthKitData = lastHealthKitWorkoutData {
      print("Phase 2: Using HealthKit enhanced data for activity")

      // Use HealthKit's total calories but keep our active/resting split
      if let workoutStats = activity.storedWorkoutStats {
        // Use the already updated workout stats that have the correct active/resting split
        let currentActiveCalories = workoutStats.activeCalories
        let currentRestingCalories = workoutStats.pauseCalories
        let currentTotal = currentActiveCalories + currentRestingCalories

        // Scale our split to match HealthKit's total, but only if HealthKit has meaningful data
        let scaleFactor =
          (currentTotal > 0 && healthKitData.totalEnergyBurned > 1.0)
          ? healthKitData.totalEnergyBurned / currentTotal : 1.0

        let updatedStats = WorkoutStats(
          averagePace: workoutStats.averagePace,
          bestPace: workoutStats.bestPace,
          averageSpeed: workoutStats.averageSpeed,
          maxSpeed: workoutStats.maxSpeed,
          totalTime: workoutStats.totalTime,
          activeTime: workoutStats.activeTime,
          pauseTime: workoutStats.pauseTime,
          activeCalories: currentActiveCalories * scaleFactor,  // Scaled active calories
          pauseCalories: currentRestingCalories * scaleFactor,  // Scaled resting calories
          totalCalories: healthKitData.totalEnergyBurned,  // HealthKit total
          metricLaps: workoutStats.metricLaps,
          imperialLaps: workoutStats.imperialLaps
        )
        activity.storedWorkoutStats = updatedStats
        print(
          "🔥 CALORIE DEBUG: HealthKit calories: \(healthKitData.totalEnergyBurned), Current total: \(currentTotal)"
        )
        print(
          "🔥 CALORIE DEBUG: Scale factor: \(scaleFactor), Will scale: \(healthKitData.totalEnergyBurned > 1.0)"
        )
        print(
          "HealthKit scaling applied - Factor: \(scaleFactor), Final Active: \(currentActiveCalories * scaleFactor), Final Resting: \(currentRestingCalories * scaleFactor)"
        )

        // Only use HealthKit calories if they're meaningful (> 1.0), otherwise keep our real-time data
        activity.storedCalories =
          healthKitData.totalEnergyBurned > 1.0 ? healthKitData.totalEnergyBurned : currentTotal
      }
      // Keep our correctly tracked active time, don't overwrite with HealthKit's total duration
      // activity.activeRunningTime is already set correctly from our manual tracking

      // Use HealthKit route data if available and more comprehensive
      if let healthKitRoute = healthKitData.routeCoordinates,
        healthKitRoute.count > rawCoordinates.count
      {
        print(
          "Phase 2: Using HealthKit route data (\(healthKitRoute.count) points vs \(rawCoordinates.count) local points)"
        )

        // Create enhanced activity with HealthKit route data
        let enhancedActivity = RunActivity(
          rawCoordinates: healthKitRoute,
          simplificationTolerance: 0.0001,
          startTime: healthKitData.startDate,
          endTime: healthKitData.endDate,
          location: locationName,
          weight: profile.first?.weight ?? 70.0,
          activeRunningTime: activeRunningTime,  // Use our correctly tracked active time
          sportType: selectedSportType ?? .run
        )

        // Apply the same calorie logic to enhanced activity
        if let workoutStats = enhancedActivity.storedWorkoutStats {
          // Check if real-time calories are meaningful for enhanced activity
          let realTimeTotal = cumulativeCalories + cumulativeRestingCalories

          let baseActiveCalories: Double
          let baseRestingCalories: Double

          if realTimeTotal > 1.0 {
            // Use real-time tracked calories
            baseActiveCalories = cumulativeCalories
            baseRestingCalories = cumulativeRestingCalories
          } else {
            // Use coordinate-based calculation from enhanced activity
            baseActiveCalories = workoutStats.activeCalories
            baseRestingCalories = workoutStats.pauseCalories
          }

          let baseTotal = baseActiveCalories + baseRestingCalories
          let scaleFactor =
            (baseTotal > 0 && healthKitData.totalEnergyBurned > 1.0)
            ? healthKitData.totalEnergyBurned / baseTotal : 1.0

          let updatedStats = WorkoutStats(
            averagePace: workoutStats.averagePace,
            bestPace: workoutStats.bestPace,
            averageSpeed: workoutStats.averageSpeed,
            maxSpeed: workoutStats.maxSpeed,
            totalTime: workoutStats.totalTime,
            activeTime: workoutStats.activeTime,
            pauseTime: workoutStats.pauseTime,
            activeCalories: baseActiveCalories * scaleFactor,
            pauseCalories: baseRestingCalories * scaleFactor,
            totalCalories: healthKitData.totalEnergyBurned,
            metricLaps: workoutStats.metricLaps,
            imperialLaps: workoutStats.imperialLaps
          )
          enhancedActivity.storedWorkoutStats = updatedStats
          print(
            "Enhanced activity HealthKit scaling - Base Active: \(baseActiveCalories), Base Resting: \(baseRestingCalories), Scale Factor: \(scaleFactor)"
          )

          // Only use HealthKit calories if they're meaningful (> 1.0), otherwise keep our real-time data
          enhancedActivity.storedCalories =
            healthKitData.totalEnergyBurned > 1.0 ? healthKitData.totalEnergyBurned : baseTotal
        }
        self.finalizeActivitySave(enhancedActivity)
      } else {
        print("Phase 2: Using local route data with HealthKit metrics")
        self.finalizeActivitySave(activity)
      }

      // Clear HealthKit data after use
      lastHealthKitWorkoutData = nil

    } else {
      print("Phase 2: No HealthKit data available, using calculated values")
      self.finalizeActivitySave(activity)
    }
  }

  private func finalizeActivitySave(_ activity: RunActivity) {
    // === CALORIE DEBUG LOGGING START ===
    print("🔥 CALORIE DEBUG: finalizeActivitySave() called")
    print("🔥 activity.storedCalories: \(activity.storedCalories ?? -1)")
    if let workoutStats = activity.storedWorkoutStats {
      print("🔥 workoutStats.activeCalories: \(workoutStats.activeCalories)")
      print("🔥 workoutStats.pauseCalories: \(workoutStats.pauseCalories)")
      print("🔥 workoutStats.totalCalories: \(workoutStats.totalCalories)")
    } else {
      print("🔥 No workoutStats available")
    }
    print("🔥 activity.activeCalories (computed): \(activity.activeCalories)")
    print("🔥 activity.calories (computed): \(activity.calories)")
    // === CALORIE DEBUG LOGGING END ===

    // Phase 4: Monitor activity save performance
    PerformanceMonitor.shared.startTiming("activity_save")

    // Hold the activity temporarily before saving
    self.newlySavedActivity = activity

    modelContext.insert(activity)

    // --- Update Trial Limit Flag START ---
    // Fetch the user profile (should exist)
    if let userProfile = profile.first {
      // Performance optimization: Track last activity sport type for AnalysisView
      userProfile.lastActivitySportType = selectedSportType ?? .run
      userProfile.lastModifiedDate = Date()  // Update sync timestamp

      // Check if the flag isn't already set
      if !userProfile.hasExceededTrialLimit {
        // Fetch the total count *after* potential insertion
        // Note: This fetch might include the newly inserted activity depending on context timing.
        // A safer approach might be to fetch count *before* insert and add 1,
        // but let's try this first as it's simpler.
        let activityDescriptor = FetchDescriptor<RunActivity>()
        do {
          let currentActivityCount = try modelContext.fetchCount(activityDescriptor)
          print(
            "[Trial Check in saveActivity] Current count: \(currentActivityCount), Limit: \(AppConstants.freeTrialLimit)"
          )
          if currentActivityCount >= AppConstants.freeTrialLimit {
            print(
              "[Trial Check in saveActivity] Limit reached! Setting hasExceededTrialLimit flag.")
            userProfile.hasExceededTrialLimit = true
            // The flag will be saved along with the activity below
          }
        } catch {
          print("Error fetching activity count in saveActivity: \(error)")
          // Decide how to handle - maybe log and continue?
        }
      }
    } else {
      print("Error: UserProfile not found in saveActivity.")
    }
    // --- Update Trial Limit Flag END ---

    // Explicitly save the model context to ensure data persistence
    // This now saves both the new activity and the potentially updated profile flag
    do {
      try modelContext.save()
      PerformanceMonitor.shared.endTiming("activity_save")
      print("Activity and potentially updated profile saved successfully: \(activity.id)")
      // Trigger the detail view sheet presentation on successful save
      self.showPostSaveDetail = true
    } catch {
      PerformanceMonitor.shared.endTiming("activity_save")
      print("Error saving activity: \(error)")
      // Reset state if save fails to allow user to retry or discard
      resetState()
      newlySavedActivity = nil  // Clear the holder if save failed
    }

    // Route data clearing is now handled in resetState(), called on sheet dismiss or save failure
  }

  // MARK: - App State Transitions

  private func handleForegroundTransition() {
    // Update UI immediately with latest data when returning to foreground
    updateUIWithLatestData()
  }

  private func handleBackgroundTransition() {
    // App went to background - existing hibernation logic handles this
    // No changes needed here as AppStateManager already handles non-workout hibernation
  }

  private func handleScreenOffTransition() {
    // Validate state before proceeding
    guard scenePhase == .inactive else {
      print("Screen off handler called but scenePhase is not inactive")
      return
    }

    print("Screen turned off - optimizing for battery")

    if isRunning {
      print(
        "Workout active: continuing GPS/metronome, stopping UI updates for battery optimization")
      // During workout: GPS and metronome continue via existing managers
      // UI updates will be automatically paused due to scenePhase checks
    } else {
      print("No workout: existing hibernation system will handle deep sleep")
      // Non-workout mode: AppStateManager hibernation handles this automatically
    }
  }

  private func updateUIWithLatestData() {
    // Force immediate UI update with latest data
    if isRunning {
      // Update map with latest location if available
      if let currentLocation = locationManager.location {
        updateMapPosition(for: currentLocation)
      }
    }
  }

  // MARK: - Workout Recovery

  private func recoverWorkoutIfNeeded() {
    // Only attempt to recover if we're supposed to be running
    guard isRunning else { return }

    print("ContentView: Attempting to recover workout after unexpected tracking stop")

    // Restart location tracking in workout mode
    locationManager.startTracking(isWorkout: true)
  }

  // MARK: - Optimized Real-Time Calorie Calculation

  /// PHASE 3: Pace state for hysteresis threshold management
  private enum PaceState {
    case resting, active, unknown
  }

  /// PHASE 3: Enhanced pace calculation with smoothing for boundary stability
  private func getCurrentGPSPaceWithSmoothing() -> Double {
    let instantPace = getCurrentGPSPace()  // Use existing method

    // Add to history for smoothing
    if instantPace > 0 {
      recentPaces.append(instantPace)
      if recentPaces.count > paceHistorySize {
        recentPaces.removeFirst()
      }
    }

    // Apply smoothing near the 18 min/km threshold (15-21 min/km range)
    if enablePaceSmoothing && instantPace >= 15.0 && instantPace <= 21.0 && recentPaces.count >= 3 {
      // Use weighted average: recent data gets more weight
      let weights = (0..<recentPaces.count).map { i in
        1.0 + Double(i) * 0.2  // Newer values get higher weight
      }

      let weightedSum = zip(recentPaces, weights).reduce(0.0) { $0 + $1.0 * $1.1 }
      let totalWeight = weights.reduce(0.0, +)

      let smoothedPace = weightedSum / totalWeight

      print(
        "🔄 PACE SMOOTHING: instant=\(String(format: "%.1f", instantPace)), smoothed=\(String(format: "%.1f", smoothedPace)), samples=\(recentPaces.count)"
      )

      return smoothedPace
    }

    return instantPace  // No smoothing needed
  }

  /// PHASE 3: Determine pace state with hysteresis to prevent boundary jitter
  private func determinePaceStateWithHysteresis(_ pace: Double) -> PaceState {
    let restingThreshold = 18.0

    switch currentPaceState {
    case .resting:
      // Need to be clearly faster to switch to active
      if pace < (restingThreshold - hysteresisBuffer) {
        currentPaceState = .active
        print("🔄 STATE CHANGE: resting → active at \(String(format: "%.1f", pace)) min/km")
      }
    case .active:
      // Need to be clearly slower to switch to resting
      if pace > (restingThreshold + hysteresisBuffer) {
        currentPaceState = .resting
        print("🔄 STATE CHANGE: active → resting at \(String(format: "%.1f", pace)) min/km")
      }
    case .unknown:
      currentPaceState = pace >= restingThreshold ? .resting : .active
      print("🔄 INITIAL STATE: \(currentPaceState) at \(String(format: "%.1f", pace)) min/km")
    }

    return currentPaceState
  }

  /// Get current pace from GPS speed (more accurate than distance/time calculation)
  /// ✅ ENHANCED: Better zero-pace detection and logging for debugging
  private func getCurrentGPSPace() -> Double {
    guard let currentSegment = routeManager.currentSegment,
      !currentSegment.isPaused,
      let lastLocation = currentSegment.coordinates.last
    else {
      print("🏃 GPS PACE: No segment or paused - returning 0 (expected during pause/stop)")
      return 0
    }

    let speed = lastLocation.speed

    // Check GPS quality - don't use poor quality GPS for pace calculation
    let locationAge = Date().timeIntervalSince(lastLocation.timestamp)
    let isGPSQualityPoor = lastLocation.horizontalAccuracy > 15.0 || locationAge > 8.0

    print(
      "🏃 GPS PACE: speed=\(String(format: "%.2f", speed))m/s, accuracy=\(String(format: "%.1f", lastLocation.horizontalAccuracy))m, age=\(String(format: "%.1f", locationAge))s, poor=\(isGPSQualityPoor)"
    )

    if isGPSQualityPoor {
      // During poor GPS, use cached pace if recent, otherwise return 0
      let timeSinceLastValidPace = Date().timeIntervalSince(lastValidPaceTime)
      if timeSinceLastValidPace <= paceValidityWindow && lastValidPace > 0 {
        print(
          "🏃 GPS PACE: Using cached pace \(String(format: "%.1f", lastValidPace)) min/km (age: \(String(format: "%.1f", timeSinceLastValidPace))s)"
        )
        return lastValidPace
      }
      print("🏃 GPS PACE: Poor GPS, no valid cache - returning 0 (will use resting calories)")
      return 0  // Don't calculate pace from poor GPS
    }

    // ✅ ENHANCED: Better speed validation and logging
    // Use GPS speed when available and valid (>0.3 m/s = ~1 km/h)
    if speed > 0.3 {
      let paceMinPerKm = (1000.0 / speed) / 60.0  // Convert m/s to min/km

      // Validate pace range (0.5 to 30 min/km for realistic running/walking paces)
      if paceMinPerKm >= 0.5 && paceMinPerKm <= 30.0 {
        // Cache valid pace for future use
        lastValidPace = paceMinPerKm
        lastValidPaceTime = Date()
        print(
          "🏃 GPS PACE: Valid GPS pace \(String(format: "%.1f", paceMinPerKm)) min/km from speed \(String(format: "%.2f", speed))m/s (cached)"
        )
        return paceMinPerKm
      } else {
        print(
          "🏃 GPS PACE: Invalid pace range \(String(format: "%.1f", paceMinPerKm)) min/km - out of bounds (0.5-30.0)"
        )
      }
    } else if speed >= 0 {
      print(
        "🏃 GPS PACE: Very slow/stationary - speed \(String(format: "%.2f", speed))m/s < 0.3m/s threshold (returning 0 for resting calories)"
      )
    } else {
      print(
        "🏃 GPS PACE: Invalid GPS speed \(String(format: "%.2f", speed))m/s - negative value indicates GPS error"
      )
    }

    // Fallback: use cached pace if recent, otherwise return 0
    let timeSinceLastValidPace = Date().timeIntervalSince(lastValidPaceTime)
    if timeSinceLastValidPace <= paceValidityWindow && lastValidPace > 0 {
      print(
        "🏃 GPS PACE: Using cached pace \(String(format: "%.1f", lastValidPace)) min/km (age: \(String(format: "%.1f", timeSinceLastValidPace))s)"
      )
      return lastValidPace
    }

    print(
      "🏃 GPS PACE: No valid pace available - returning 0 (stationary/poor GPS → resting calories)")
    return 0
  }

  /// Update real-time calories using incremental calculation (called every 5 seconds)
  /// PHASE 3: Enhanced with pace smoothing and hysteresis for stable boundary detection
  private func updateRealTimeCalories() {
    let now = Date()
    let timeInterval = now.timeIntervalSince(lastCalorieUpdateTime)

    print("🔥 CALORIE UPDATE: Starting - interval=\(String(format: "%.2f", timeInterval))s")

    // Skip if interval is too small (shouldn't happen with 5s timer)
    guard timeInterval > 0.1 else {
      print(
        "🔥 CALORIE UPDATE: Skipped - interval too small (\(String(format: "%.2f", timeInterval))s)")
      return
    }

    // ✅ FIX: Cap time interval to prevent excessive calorie accumulation during GPS gaps
    let cappedTimeInterval = min(timeInterval, 10.0)  // Max 10 seconds per update
    if cappedTimeInterval != timeInterval {
      print(
        "🔥 CALORIE UPDATE: Capped interval from \(String(format: "%.2f", timeInterval))s to \(String(format: "%.2f", cappedTimeInterval))s"
      )
    }

    // ✅ FIX: Check GPS quality before updating calories
    let hasRecentValidGPS = checkGPSQuality()

    if !hasRecentValidGPS {
      // During poor GPS, only accumulate minimal resting calories
      let weight = profile.first?.weight ?? 70.0
      let sportType = selectedSportType ?? .run
      let restingMET = sportType.getMET(forPace: 25.0)  // Very slow pace for minimal calories
      let hoursInterval = cappedTimeInterval / 3600.0
      let minimalCalories = restingMET * weight * hoursInterval

      cumulativeRestingCalories += minimalCalories

      print(
        "🔥 POOR GPS: minimal resting calories +\(String(format: "%.3f", minimalCalories)) cal (MET=\(String(format: "%.1f", restingMET)), weight=\(weight)kg, time=\(String(format: "%.2f", cappedTimeInterval))s), total: \(String(format: "%.3f", cumulativeRestingCalories)) cal"
      )

      lastCalorieUpdateTime = now
      return
    }

    // ✅ CRITICAL FIX: Enhanced pace calculation with proper zero-pace handling
    let rawPace = getCurrentGPSPaceWithSmoothing()  // Enhanced method
    let weight = profile.first?.weight ?? 70.0
    let sportType = selectedSportType ?? .run

    print(
      "🔥 CALORIE UPDATE: Good GPS - rawPace=\(String(format: "%.1f", rawPace)) min/km, weight=\(weight)kg, sport=\(sportType)"
    )

    // ✅ CRITICAL FIX: Handle zero pace (no movement) properly
    let effectivePace: Double
    if rawPace <= 0 {
      // When no movement detected (pace = 0), use resting pace to get minimal MET values
      effectivePace = 25.0  // Very slow pace ensures resting MET (~1.0-1.5)
      print(
        "🔥 CALORIE UPDATE: Zero pace detected - using resting pace \(effectivePace) min/km for minimal calories"
      )
    } else {
      // Apply hysteresis for stable MET decisions only when we have valid pace
      let paceState = determinePaceStateWithHysteresis(rawPace)

      switch paceState {
      case .resting:
        effectivePace = max(rawPace, 18.0)  // Ensure resting MET
      case .active:
        effectivePace = min(rawPace, 17.9)  // Ensure active MET
      case .unknown:
        effectivePace = rawPace  // Use raw pace for unknown state
      }

      print(
        "🔥 CALORIE UPDATE: Valid pace - raw=\(String(format: "%.1f", rawPace)), state=\(paceState), effective=\(String(format: "%.1f", effectivePace))"
      )
    }

    // Use existing MET calculation with corrected pace
    let metValue = sportType.getMET(forPace: effectivePace)
    let hoursInterval = cappedTimeInterval / 3600.0  // ✅ Use capped interval
    let incrementalCalories = metValue * weight * hoursInterval

    print(
      "🔥 CALORIE UPDATE: Calculation - MET=\(String(format: "%.1f", metValue)), hours=\(String(format: "%.4f", hoursInterval)), increment=\(String(format: "%.3f", incrementalCalories))"
    )

    // ✅ VALIDATION: Check for unrealistic MET values that indicate bugs
    if metValue > 15.0 && rawPace <= 0 {
      print(
        "⚠️ BUG DETECTED: High MET (\(String(format: "%.1f", metValue))) with zero pace - this should not happen!"
      )
      LogManager.shared.log(
        "BUG: High MET (\(String(format: "%.1f", metValue))) with zero pace detected",
        category: .audio)
    }

    // ✅ FIXED: Determine final pace state for logging
    let finalPaceState: String
    if rawPace <= 0 {
      finalPaceState = "ZERO_PACE"
    } else if effectivePace >= 18.0 {
      finalPaceState = "RESTING"
    } else {
      finalPaceState = "ACTIVE"
    }

    // Existing calorie categorization logic unchanged
    let isRestingPace = effectivePace >= 18.0

    if isRestingPace {
      // Add to resting calories for UI display
      cumulativeRestingCalories += incrementalCalories

      // Enhanced logging for stability verification
      print(
        "🔥 ENHANCED RESTING: pace=\(String(format: "%.1f", rawPace))→\(String(format: "%.1f", effectivePace)), state=\(finalPaceState), +\(String(format: "%.3f", incrementalCalories)) cal, total: \(String(format: "%.3f", cumulativeRestingCalories)) cal"
      )

      // Also log to LogManager (every 30 seconds to avoid spam)
      if Int(elapsedTime) % 30 == 0 {
        LogManager.shared.log(
          "Enhanced RESTING calorie update: +\(String(format: "%.1f", incrementalCalories)) cal, resting total: \(String(format: "%.1f", cumulativeRestingCalories)) cal, pace: \(String(format: "%.1f", rawPace))→\(String(format: "%.1f", effectivePace)), state: \(finalPaceState)",
          category: .audio)
      }
    } else {
      // Add to active calories for UI display
      cumulativeCalories += incrementalCalories

      // Enhanced logging for stability verification
      print(
        "🔥 ENHANCED ACTIVE: pace=\(String(format: "%.1f", rawPace))→\(String(format: "%.1f", effectivePace)), state=\(finalPaceState), +\(String(format: "%.3f", incrementalCalories)) cal, total: \(String(format: "%.3f", cumulativeCalories)) cal"
      )

      // Log for debugging (every 30 seconds to avoid spam)
      if Int(elapsedTime) % 30 == 0 {
        LogManager.shared.log(
          "Enhanced ACTIVE calorie update: +\(String(format: "%.1f", incrementalCalories)) cal, active total: \(String(format: "%.1f", cumulativeCalories)) cal, pace: \(String(format: "%.1f", rawPace))→\(String(format: "%.1f", effectivePace)), state: \(finalPaceState)",
          category: .audio)
      }
    }

    lastCalorieUpdateTime = now
    print(
      "🔥 CALORIE UPDATE: Completed - active=\(String(format: "%.3f", cumulativeCalories)), resting=\(String(format: "%.3f", cumulativeRestingCalories)), total=\(String(format: "%.3f", cumulativeCalories + cumulativeRestingCalories))"
    )
  }

  /// ✅ NEW: Check GPS quality to determine if we should update calories
  private func checkGPSQuality() -> Bool {
    guard let lastLocation = locationManager.location else {
      print("🔍 GPS QUALITY: No location available")
      return false
    }

    let locationAge = Date().timeIntervalSince(lastLocation.timestamp)
    let hasGoodAccuracy = lastLocation.horizontalAccuracy <= 15.0
    let isRecent = locationAge <= 8.0
    let isGoodQuality = hasGoodAccuracy && isRecent

    // Log GPS quality details for debugging
    print(
      "🔍 GPS QUALITY: accuracy=\(String(format: "%.1f", lastLocation.horizontalAccuracy))m, age=\(String(format: "%.1f", locationAge))s, speed=\(String(format: "%.2f", lastLocation.speed))m/s, quality=\(isGoodQuality ? "GOOD" : "POOR")"
    )

    // Log poor GPS reasons
    if !isGoodQuality {
      var reasons: [String] = []
      if !hasGoodAccuracy { reasons.append("accuracy>\(15.0)m") }
      if !isRecent { reasons.append("age>\(8.0)s") }
      print("🔍 GPS QUALITY: POOR - reasons: \(reasons.joined(separator: ", "))")
    }

    return isGoodQuality
  }

  /// ✅ ENHANCED: Comprehensive validation to ensure all fixes are working
  private func validateCalorieAccumulation() {
    let totalCalories = cumulativeCalories + cumulativeRestingCalories
    let activeTime = calculateActiveTime()

    // Check for anomalous calorie rates that indicate the bug is still present
    if activeTime > 30 {  // Only check after 30 seconds of activity
      let calorieRate = totalCalories / max(activeTime / 3600.0, 0.01)  // cal/hour
      let weight = profile.first?.weight ?? 70.0

      // ✅ ENHANCED: More realistic calorie rate validation
      let expectedMaxRate = weight * 12.0  // 12 MET * weight = very high but realistic max (elite running)
      let expectedMinRate = weight * 1.0  // 1 MET * weight = resting minimum
      let moderateMaxRate = weight * 8.0  // 8 MET * weight = moderate running max

      // Get current pace for context
      let currentPace = getCurrentGPSPace()
      let hasGoodGPS = checkGPSQuality()

      if calorieRate > expectedMaxRate {
        print("🚨 VALIDATION: CRITICAL - Extremely high calorie rate!")
        print(
          "🚨 Rate: \(String(format: "%.0f", calorieRate)) cal/h (max expected: \(String(format: "%.0f", expectedMaxRate)) cal/h)"
        )
        print(
          "🚨 Context: pace=\(String(format: "%.1f", currentPace)), GPS=\(hasGoodGPS ? "GOOD" : "POOR"), active=\(String(format: "%.0f", activeTime))s"
        )
        print(
          "🚨 Calories: active=\(String(format: "%.1f", cumulativeCalories)), resting=\(String(format: "%.1f", cumulativeRestingCalories))"
        )

        LogManager.shared.log(
          "CRITICAL: Calorie rate \(String(format: "%.0f", calorieRate)) cal/h exceeds max \(String(format: "%.0f", expectedMaxRate)) - BUG STILL PRESENT",
          category: .audio)
      } else if calorieRate > moderateMaxRate {
        print("⚠️ VALIDATION: High calorie rate (but within bounds)")
        print(
          "⚠️ Rate: \(String(format: "%.0f", calorieRate)) cal/h (moderate max: \(String(format: "%.0f", moderateMaxRate)) cal/h)"
        )
        print(
          "⚠️ Context: pace=\(String(format: "%.1f", currentPace)), GPS=\(hasGoodGPS ? "GOOD" : "POOR")"
        )
      } else if calorieRate < expectedMinRate {
        print("⚠️ VALIDATION: Unusually low calorie rate")
        print(
          "⚠️ Rate: \(String(format: "%.0f", calorieRate)) cal/h (min expected: \(String(format: "%.0f", expectedMinRate)) cal/h)"
        )
      } else {
        print("✅ VALIDATION: Calorie rate is NORMAL - \(String(format: "%.0f", calorieRate)) cal/h")
        print(
          "✅ Range: \(String(format: "%.0f", expectedMinRate))-\(String(format: "%.0f", moderateMaxRate)) cal/h (moderate), max \(String(format: "%.0f", expectedMaxRate)) cal/h"
        )
        print(
          "✅ Context: pace=\(String(format: "%.1f", currentPace)), GPS=\(hasGoodGPS ? "GOOD" : "POOR"), FIX WORKING ✓"
        )
      }

      // ✅ NEW: Additional validation checks
      // Check for timer consistency
      let expectedElapsedTime = activeTime  // Should be roughly equal when not paused much
      let timeDifference = abs(elapsedTime - expectedElapsedTime)
      if timeDifference > 10 {  // Allow 10 second tolerance
        print(
          "⚠️ TIMER VALIDATION: Time inconsistency - elapsed=\(String(format: "%.0f", elapsedTime))s, active=\(String(format: "%.0f", activeTime))s, diff=\(String(format: "%.0f", timeDifference))s"
        )
      } else {
        print(
          "✅ TIMER VALIDATION: Time consistency OK - elapsed=\(String(format: "%.0f", elapsedTime))s, active=\(String(format: "%.0f", activeTime))s"
        )
      }
    }
  }

  private func resetRealTimeCalories() {
    cumulativeCalories = 0  // Reset active calories
    cumulativeRestingCalories = 0  // Reset resting calories
    lastCalorieUpdateTime = Date()

    // PHASE 3: Reset enhanced GPS state
    recentPaces.removeAll()
    currentPaceState = .unknown
    print("🔄 RESET: Cleared pace history and state for new workout")
  }

  /// Start the optimized calorie update timer (5-second intervals)
  private func startCalorieUpdateTimer() {
    calorieUpdateTimer?.invalidate()
    calorieUpdateTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
      updateRealTimeCalories()
    }
    print("🔥 CALORIE TIMER: Started with 5-second interval")
  }

  /// Stop the calorie update timer
  private func stopCalorieUpdateTimer() {
    calorieUpdateTimer?.invalidate()
    calorieUpdateTimer = nil
    print("🔥 CALORIE TIMER: Stopped")
  }

  private func handleScreenToggle() {
    isScreenAlwaysOn.toggle()
    UIApplication.shared.isIdleTimerDisabled = isScreenAlwaysOn

    // Cancel existing timer if any
    screenMessageTimer?.invalidate()

    // Show message
    withAnimation {
      showingScreenMessage = true
    }

    // Set new timer to hide message
    screenMessageTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: false) { _ in
      withAnimation {
        showingScreenMessage = false
      }
    }
  }
}

// MARK: - Supporting Types

extension ContentView {
  enum MapStyleChoice: String, CaseIterable, Hashable {
    case standard, hybrid, satellite

    var mapStyle: MapStyle {
      switch self {
      case .standard: return .standard
      case .hybrid: return .hybrid
      case .satellite: return .imagery
      }
    }
  }
}

#Preview {
  ContentView()
    .modelContainer(for: UserProfile.self, inMemory: true)
}
