import Foundation
import SwiftData

/// Utility class for exporting user data to CSV format
/// Handles both profile and activity data with comprehensive field coverage
@MainActor
final class DataExporter {
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - Public Export Methods
    
    /// Export user profile data to CSV format
    /// - Returns: CSV content string with all profile fields
    func exportProfileToCSV() async throws -> String {
        return try await Task.detached { [modelContext, self] in
            let backgroundContext = ModelContext(modelContext.container)
            let descriptor = FetchDescriptor<UserProfile>()
            let profiles = try backgroundContext.fetch(descriptor)
            
            guard let userProfile = profiles.first else {
                throw DataManagementError.profileNotFound
            }
            
            return await self.generateProfileCSV(from: userProfile)
        }.value
    }
    
    /// Export all activities to CSV format with comprehensive data
    /// - Returns: CSV content string with all activity fields and coordinates
    func exportActivitiesToCSV() async throws -> String {
        return try await Task.detached { [modelContext, self] in
            let backgroundContext = ModelContext(modelContext.container)
            let descriptor = FetchDescriptor<RunActivity>(
                sortBy: [SortDescriptor(\.startTime, order: .reverse)]
            )
            let activities = try backgroundContext.fetch(descriptor)
            
            guard !activities.isEmpty else {
                throw DataManagementError.noDataToExport
            }
            
            return await self.generateActivitiesCSV(from: activities)
        }.value
    }
    
    /// Save CSV content to file in documents directory
    /// - Parameters:
    ///   - content: CSV content string
    ///   - fileName: Base filename without extension
    /// - Returns: URL of the created file
    func saveCSVToFile(content: String, fileName: String) async throws -> URL {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw DataManagementError.documentsDirectoryNotFound
        }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let timestamp = dateFormatter.string(from: Date())
        let fullFileName = "\(fileName)_\(timestamp).csv"
        let fileURL = documentsPath.appendingPathComponent(fullFileName)
        
        do {
            try content.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            throw DataManagementError.fileCreationFailed
        }
    }
}

// MARK: - Private CSV Generation Methods

private extension DataExporter {
    
    /// Generate comprehensive CSV content for user profile
    func generateProfileCSV(from profile: UserProfile) async -> String {
        var csvRows: [String] = []
        
        // CSV Header
        csvRows.append("Field,Value,Type,Description\n")
        
        // Basic Information
        csvRows.append("Name,\"\(escapeCSVValue(profile.name ?? ""))\",String,User display name\n")
        csvRows.append("Email,\"\(escapeCSVValue(profile.email ?? ""))\",String,User email address\n")
        csvRows.append("Weight,\(profile.weight ?? 0.0),Double,Weight in kg\n")
        csvRows.append("Height,\(profile.height ?? 0.0),Double,Height in cm\n")
        csvRows.append("Gender,\(profile.gender.rawValue),String,User gender\n")
        csvRows.append("Birth Date,\(profile.birthDate?.ISO8601Format() ?? ""),Date,Birth date in ISO8601 format\n")
        csvRows.append("Age,\(profile.age ?? 0),Int,Calculated age in years\n")
        
        // Preferences
        csvRows.append("Units,\(profile.units.rawValue),String,Unit system preference\n")
        csvRows.append("Language,\(profile.language.rawValue),String,Language preference\n")
        csvRows.append("Weight Unit,\"\(escapeCSVValue(profile.weightUnit ?? ""))\",String,Weight unit display\n")
        csvRows.append("Height Unit,\"\(escapeCSVValue(profile.heightUnit ?? ""))\",String,Height unit display\n")
        
        // Metronome Settings
        csvRows.append("Metronome Enabled,\(profile.metronomeEnabled),Bool,Metronome enabled status\n")
        csvRows.append("Metronome BPM,\(profile.metronomeBPM),Int,Metronome beats per minute\n")
        csvRows.append("Metronome Sound,\(profile.metronomeSound),Int,Metronome sound identifier\n")
        csvRows.append("Metronome Sound Enabled,\(profile.metronomeSoundEnabled),Bool,Metronome sound enabled\n")
        csvRows.append("Metronome Vibration Enabled,\(profile.metronomeVibrationEnabled),Bool,Metronome vibration enabled\n")
        csvRows.append("Metronome Volume,\(profile.metronomeVolume),Float,Metronome volume level\n")
        csvRows.append("Metronome Alert Frequency,\(profile.metronomeAlertFrequency.rawValue),String,Alert frequency setting\n")
        
        // Audio Alert Settings
        csvRows.append("Audio Alerts Enabled,\(profile.audioAlertsEnabled),Bool,Audio alerts master switch\n")
        csvRows.append("Audio Alert Volume,\(profile.audioAlertVolume),Float,Audio alert volume level\n")
        csvRows.append("Speech Rate,\(profile.speechRate),Float,Voice speech rate\n")
        csvRows.append("Smart Voice Enabled,\(profile.smartVoiceEnabled),Bool,Smart voice feature enabled\n")
        
        // Distance Alerts
        csvRows.append("Distance Alert Enabled,\(profile.distanceAlertEnabled),Bool,Distance alerts enabled\n")
        csvRows.append("Distance Alert Interval,\(profile.distanceAlertInterval),Double,Distance alert interval\n")
        
        // Time Alerts
        csvRows.append("Time Alert Enabled,\(profile.timeAlertEnabled),Bool,Time alerts enabled\n")
        csvRows.append("Time Alert Interval,\(profile.timeAlertInterval),Double,Time alert interval in seconds\n")
        
        // Calorie Alerts
        csvRows.append("Calorie Alert Enabled,\(profile.calorieAlertEnabled),Bool,Calorie alerts enabled\n")
        csvRows.append("Calorie Alert Interval,\(profile.calorieAlertInterval),Double,Calorie alert interval\n")
        
        // Pace Alerts
        csvRows.append("Pace Alert Enabled,\(profile.paceAlertEnabled),Bool,Pace alerts enabled\n")
        csvRows.append("Pace Alert Interval,\(profile.paceAlertInterval),Double,Pace alert interval in seconds\n")
        
        // Voice Preferences (serialized as JSON string)
        let voicePrefsJSON = serializeVoicePreferences(profile.voicePreferences)
        csvRows.append("Voice Preferences,\"\(escapeCSVValue(voicePrefsJSON))\",JSON,Voice preferences by language\n")
        csvRows.append("Selected Voice Identifier,\"\(escapeCSVValue(profile.selectedVoiceIdentifier ?? ""))\",String,Current voice identifier\n")
        
        // System Data
        csvRows.append("Has Exceeded Trial Limit,\(profile.hasExceededTrialLimit),Bool,Trial limit status\n")
        csvRows.append("Last Activity Sport Type,\(profile.lastActivitySportType?.rawValue ?? ""),String,Last activity type for optimization\n")
        csvRows.append("Cached Activity Count,\(profile.cachedActivityCount),Int,Cached activity count\n")
        csvRows.append("Last Activity Count Update,\(profile.lastActivityCountUpdate?.ISO8601Format() ?? ""),Date,Last count update timestamp\n")
        csvRows.append("Last Modified Date,\(profile.lastModifiedDate.ISO8601Format()),Date,Profile last modified timestamp\n")
        
        return csvRows.joined()
    }
    
    /// Generate comprehensive CSV content for activities
    func generateActivitiesCSV(from activities: [RunActivity]) async -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm:ss"
        
        let isoFormatter = ISO8601DateFormatter()
        
        var csvRows: [String] = []
        
        // Comprehensive CSV Header
        csvRows.append("ID,Date,Start Time,End Time,Start Time ISO,End Time ISO,Sport Type,Duration Seconds,Active Running Time,Distance Meters,Stored Pace,Stored Distance,Stored Calories,Calories,Location,Coordinate Count,Coordinates JSON,Workout Stats JSON\n")
        
        for activity in activities {
            let id = activity.id.uuidString
            let date = dateFormatter.string(from: activity.startTime)
            let startTime = timeFormatter.string(from: activity.startTime)
            let endTime = timeFormatter.string(from: activity.endTime)
            let startTimeISO = isoFormatter.string(from: activity.startTime)
            let endTimeISO = isoFormatter.string(from: activity.endTime)
            let sportType = activity.sportType.rawValue
            let durationSeconds = activity.endTime.timeIntervalSince(activity.startTime)
            let activeRunningTime = activity.activeRunningTime
            let distanceMeters = activity.distance
            let storedPace = activity.storedPace
            let storedDistance = activity.storedDistance ?? 0.0
            let storedCalories = activity.storedCalories ?? 0.0
            let calories = activity.calories
            let location = escapeCSVValue(activity.location.isEmpty ? "Unknown Location" : activity.location)
            let coordinateCount = activity.coordinates.count
            
            // Serialize coordinates as JSON for complete data preservation
            let coordinatesJSON = serializeCoordinates(activity.coordinates)
            
            // Serialize workout stats if available
            let workoutStatsJSON = serializeWorkoutStats(activity.storedWorkoutStats)
            
            let row = "\(id),\(date),\(startTime),\(endTime),\(startTimeISO),\(endTimeISO),\(sportType),\(durationSeconds),\(activeRunningTime),\(distanceMeters),\(storedPace),\(storedDistance),\(storedCalories),\(calories),\"\(location)\",\(coordinateCount),\"\(escapeCSVValue(coordinatesJSON))\",\"\(escapeCSVValue(workoutStatsJSON))\"\n"
            
            csvRows.append(row)
        }
        
        return csvRows.joined()
    }
    
    /// Escape CSV values to handle quotes and commas
    func escapeCSVValue(_ value: String) -> String {
        return value.replacingOccurrences(of: "\"", with: "\"\"")
    }
    
    /// Serialize coordinates to JSON string for complete data preservation
    func serializeCoordinates(_ coordinates: [Coordinate]) -> String {
        do {
            let coordinateData = coordinates.map { coord in
                [
                    "latitude": coord.latitude,
                    "longitude": coord.longitude,
                    "altitude": coord.altitude,
                    "timestamp": coord.timestamp.timeIntervalSince1970
                ]
            }
            let jsonData = try JSONSerialization.data(withJSONObject: coordinateData)
            return String(data: jsonData, encoding: .utf8) ?? "[]"
        } catch {
            print("Error serializing coordinates: \(error)")
            return "[]"
        }
    }
    
    /// Serialize workout stats to JSON string
    func serializeWorkoutStats(_ workoutStats: WorkoutStats?) -> String {
        guard let stats = workoutStats else { return "{}" }
        
        let statsData: [String: Any] = [
            "averagePace": stats.averagePace,
            "bestPace": stats.bestPace,
            "averageSpeed": stats.averageSpeed,
            "maxSpeed": stats.maxSpeed,
            "totalTime": stats.totalTime,
            "activeTime": stats.activeTime,
            "pauseTime": stats.pauseTime,
            "activeCalories": stats.activeCalories,
            "pauseCalories": stats.pauseCalories,
            "totalCalories": stats.totalCalories,
            "metricLapsCount": stats.metricLaps.count,
            "imperialLapsCount": stats.imperialLaps.count
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: statsData)
            return String(data: jsonData, encoding: .utf8) ?? "{}"
        } catch {
            print("Error serializing workout stats: \(error)")
            return "{}"
        }
    }
    
    /// Serialize voice preferences to JSON string
    func serializeVoicePreferences(_ preferences: [String: String]) -> String {
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: preferences)
            return String(data: jsonData, encoding: .utf8) ?? "{}"
        } catch {
            print("Error serializing voice preferences: \(error)")
            return "{}"
        }
    }
} 