import Foundation

enum DataManagementError: Error, LocalizedError {
    case noDataToExport
    case documentsDirectoryNotFound
    case fileCreationFailed
    case invalidCSVFormat
    case invalidFileType
    case dataCorrupted
    case importFailed(String)
    case exportFailed(String)
    case deletionFailed(String)
    case profileNotFound
    case duplicateDetectionFailed
    
    var errorDescription: String? {
        switch self {
        case .noDataToExport:
            return NSLocalizedString("No data available to export", comment: "Error when there's no data to export")
        case .documentsDirectoryNotFound:
            return NSLocalizedString("Could not access documents directory", comment: "Error when documents directory is not accessible")
        case .fileCreationFailed:
            return NSLocalizedString("Failed to create export file", comment: "Error when file creation fails")
        case .invalidCSVFormat:
            return NSLocalizedString("Invalid CSV file format", comment: "Error when CSV format is invalid")
        case .invalidFileType:
            return NSLocalizedString("Invalid file type. Please select CSV files", comment: "Error when wrong file type is selected")
        case .dataCorrupted:
            return NSLocalizedString("Data appears to be corrupted", comment: "Error when data is corrupted")
        case .importFailed(let details):
            return NSLocalizedString("Import failed: \(details)", comment: "Error when import operation fails")
        case .exportFailed(let details):
            return NSLocalizedString("Export failed: \(details)", comment: "Error when export operation fails")
        case .deletionFailed(let details):
            return NSLocalizedString("Deletion failed: \(details)", comment: "Error when deletion operation fails")
        case .profileNotFound:
            return NSLocalizedString("User profile not found", comment: "Error when user profile is missing")
        case .duplicateDetectionFailed:
            return NSLocalizedString("Failed to detect duplicates", comment: "Error when duplicate detection fails")
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .noDataToExport:
            return NSLocalizedString("Create some activities first before exporting", comment: "Recovery suggestion for no data")
        case .documentsDirectoryNotFound:
            return NSLocalizedString("Check your device storage permissions", comment: "Recovery suggestion for directory access")
        case .fileCreationFailed:
            return NSLocalizedString("Check your device storage space", comment: "Recovery suggestion for file creation")
        case .invalidCSVFormat:
            return NSLocalizedString("Ensure you're selecting valid RunApp export files", comment: "Recovery suggestion for invalid CSV")
        case .invalidFileType:
            return NSLocalizedString("Select CSV files exported from RunApp", comment: "Recovery suggestion for invalid file type")
        case .dataCorrupted:
            return NSLocalizedString("Try exporting fresh data", comment: "Recovery suggestion for corrupted data")
        case .importFailed(_):
            return NSLocalizedString("Check the file format and try again", comment: "Recovery suggestion for import failure")
        case .exportFailed(_):
            return NSLocalizedString("Check device storage and try again", comment: "Recovery suggestion for export failure")
        case .deletionFailed(_):
            return NSLocalizedString("Restart the app and try again", comment: "Recovery suggestion for deletion failure")
        case .profileNotFound:
            return NSLocalizedString("Create a new profile in settings", comment: "Recovery suggestion for missing profile")
        case .duplicateDetectionFailed:
            return NSLocalizedString("Import may contain duplicate data", comment: "Recovery suggestion for duplicate detection failure")
        }
    }
} 