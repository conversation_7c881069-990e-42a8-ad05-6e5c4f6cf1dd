import Foundation
import SwiftUI
import CoreLocation

// MARK: - Speed Analysis Data Structures
struct SpeedAnalysis {
    let minSpeed: Double
    let maxSpeed: Double
    let validSpeeds: [Double]
    let quartiles: [Double]
    let hasValidData: Bool
}

struct SpeedRange {
    let min: Double
    let max: Double
    let quartiles: [Double]
    
    var median: Double {
        return quartiles.count >= 3 ? quartiles[2] : (min + max) / 2
    }
}

struct SpeedSegment {
    let coordinates: [CLLocationCoordinate2D]
    let color: Color
    let speed: Double?
}

// MARK: - Speed Analyzer
class SpeedAnalyzer {
    
    /// Analyzes route speed data and returns comprehensive speed analysis
    static func analyzeRouteSpeed(_ coordinates: [Coordinate]) -> SpeedAnalysis {
        let validSpeeds = filterValidSpeeds(coordinates)
        
        guard validSpeeds.count >= 2 else {
            return SpeedAnalysis(
                minSpeed: 0,
                maxSpeed: 0,
                validSpeeds: [],
                quartiles: [],
                hasValidData: false
            )
        }
        
        let sortedSpeeds = validSpeeds.sorted()
        let minSpeed = sortedSpeeds.first ?? 0
        let maxSpeed = sortedSpeeds.last ?? 0
        let quartiles = calculateQuartiles(sortedSpeeds)
        
        return SpeedAnalysis(
            minSpeed: minSpeed,
            maxSpeed: maxSpeed,
            validSpeeds: validSpeeds,
            quartiles: quartiles,
            hasValidData: true
        )
    }
    
    /// Filters valid speed data from coordinates
    static func filterValidSpeeds(_ coordinates: [Coordinate]) -> [Double] {
        return coordinates.compactMap { coordinate in
            guard let speed = coordinate.speed,
                  !coordinate.isPaused,
                  speed >= 0,
                  speed <= 13.89 else { // Filter out speeds > 50 km/h (13.89 m/s) as GPS errors
                return nil
            }
            return speed
        }
    }
    
    /// Calculates speed ranges including quartiles
    static func calculateSpeedRanges(speeds: [Double]) -> SpeedRange {
        guard !speeds.isEmpty else {
            return SpeedRange(min: 0, max: 0, quartiles: [])
        }
        
        let sortedSpeeds = speeds.sorted()
        let min = sortedSpeeds.first ?? 0
        let max = sortedSpeeds.last ?? 0
        let quartiles = calculateQuartiles(sortedSpeeds)
        
        return SpeedRange(min: min, max: max, quartiles: quartiles)
    }
    
    /// Calculates quartiles for speed distribution
    private static func calculateQuartiles(_ sortedSpeeds: [Double]) -> [Double] {
        guard sortedSpeeds.count >= 4 else {
            return sortedSpeeds
        }
        
        let count = sortedSpeeds.count
        
        // Calculate quartile positions
        let q1Index = count / 4
        let q2Index = count / 2  // Median
        let q3Index = (3 * count) / 4
        let q4Index = (9 * count) / 10  // 90th percentile
        
        return [
            sortedSpeeds[0],                    // Min (0th percentile)
            sortedSpeeds[q1Index],              // 25th percentile
            sortedSpeeds[q2Index],              // 50th percentile (median)
            sortedSpeeds[q3Index],              // 75th percentile
            sortedSpeeds[q4Index],              // 90th percentile
            sortedSpeeds[count - 1]             // Max (100th percentile)
        ]
    }
    
    /// Converts speed from meters per second to the specified unit system
    static func convertSpeed(_ metersPerSecond: Double, to unitSystem: UnitSystem) -> Double {
        switch unitSystem {
        case .metric:
            return metersPerSecond * 3.6  // m/s to km/h
        case .imperial:
            return metersPerSecond * 2.237  // m/s to mph
        }
    }
    
    /// Formats speed with appropriate unit
    static func formatSpeed(_ speed: Double, unit: UnitSystem) -> String {
        let convertedSpeed = convertSpeed(speed, to: unit)
        let unitLabel = unit == .metric ? "km/h" : "mph"
        return String(format: "%.1f %@", convertedSpeed, unitLabel)
    }
    
    /// Creates speed segments for route visualization
    static func createSpeedSegments(
        from coordinates: [Coordinate],
        using speedAnalysis: SpeedAnalysis
    ) -> [SpeedSegment] {
        guard speedAnalysis.hasValidData,
              coordinates.count > 1 else {
            // Fallback to blue segments for routes without speed data
            return createFallbackSegments(from: coordinates)
        }
        
        var segments: [SpeedSegment] = []
        
        for i in 0..<(coordinates.count - 1) {
            let start = coordinates[i]
            let end = coordinates[i + 1]
            
            let segmentCoordinates = [start.clCoordinate, end.clCoordinate]
            let segmentSpeed = start.speed
            
            let color = SpeedColorMapper.speedToColor(
                segmentSpeed,
                speedRange: SpeedRange(
                    min: speedAnalysis.minSpeed,
                    max: speedAnalysis.maxSpeed,
                    quartiles: speedAnalysis.quartiles
                )
            )
            
            let segment = SpeedSegment(
                coordinates: segmentCoordinates,
                color: start.isPaused ? color.opacity(0.3) : color,
                speed: segmentSpeed
            )
            
            segments.append(segment)
        }
        
        return segments
    }
    
    /// Creates fallback blue segments for activities without speed data
    private static func createFallbackSegments(from coordinates: [Coordinate]) -> [SpeedSegment] {
        var segments: [SpeedSegment] = []
        
        for i in 0..<(coordinates.count - 1) {
            let start = coordinates[i]
            let end = coordinates[i + 1]
            
            let segmentCoordinates = [start.clCoordinate, end.clCoordinate]
            let color = start.isPaused ? Color.blue.opacity(0.2) : Color.blue
            
            let segment = SpeedSegment(
                coordinates: segmentCoordinates,
                color: color,
                speed: nil
            )
            
            segments.append(segment)
        }
        
        return segments
    }
}