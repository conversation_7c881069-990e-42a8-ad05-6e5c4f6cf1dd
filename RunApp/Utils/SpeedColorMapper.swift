import Foundation
import SwiftUI

// MARK: - Speed Color Mapper
class SpeedColorMapper {
    
    /// Heatmap colors from slowest (blue) to fastest (red)
    static let heatmapColors: [Color] = [
        Color.blue,      // Slowest (0%)
        Color.cyan,      // 25%
        Color.green,     // 50% 
        Color.yellow,    // 75%
        Color.orange,    // 90%
        Color.red        // Fastest (100%)
    ]
    
    /// Maps a speed value to a color based on the speed range
    static func speedToColor(_ speed: Double?, speedRange: SpeedRange) -> Color {
        guard let speed = speed,
              speedRange.quartiles.count >= 6,
              speedRange.max > speedRange.min else {
            return Color.blue // Default fallback color
        }
        
        // Handle edge cases
        if speed <= speedRange.min {
            return heatmapColors[0] // Blue for minimum speed
        }
        if speed >= speedRange.max {
            return heatmapColors.last! // Red for maximum speed
        }
        
        // Map speed to quartile ranges
        let quartiles = speedRange.quartiles
        
        // Determine which quartile range the speed falls into
        let colorIndex: Int
        let interpolationFactor: Double
        
        if speed <= quartiles[1] { // 0-25th percentile
            colorIndex = 0
            interpolationFactor = (speed - quartiles[0]) / (quartiles[1] - quartiles[0])
        } else if speed <= quartiles[2] { // 25-50th percentile
            colorIndex = 1
            interpolationFactor = (speed - quartiles[1]) / (quartiles[2] - quartiles[1])
        } else if speed <= quartiles[3] { // 50-75th percentile
            colorIndex = 2
            interpolationFactor = (speed - quartiles[2]) / (quartiles[3] - quartiles[2])
        } else if speed <= quartiles[4] { // 75-90th percentile
            colorIndex = 3
            interpolationFactor = (speed - quartiles[3]) / (quartiles[4] - quartiles[3])
        } else { // 90-100th percentile
            colorIndex = 4
            interpolationFactor = (speed - quartiles[4]) / (quartiles[5] - quartiles[4])
        }
        
        // Interpolate between colors
        let startColor = heatmapColors[colorIndex]
        let endColor = heatmapColors[min(colorIndex + 1, heatmapColors.count - 1)]
        
        return interpolateColor(between: startColor, and: endColor, factor: interpolationFactor)
    }
    
    /// Interpolates between two colors based on a factor (0.0 to 1.0)
    static func interpolateColor(between startColor: Color, and endColor: Color, factor: Double) -> Color {
        let clampedFactor = max(0.0, min(1.0, factor))
        
        // Extract color components
        let startComponents = UIColor(startColor).cgColor.components ?? [0, 0, 0, 1]
        let endComponents = UIColor(endColor).cgColor.components ?? [0, 0, 0, 1]
        
        // Ensure we have at least RGB components
        guard startComponents.count >= 3, endComponents.count >= 3 else {
            return startColor
        }
        
        // Interpolate each component
        let red = startComponents[0] + (endComponents[0] - startComponents[0]) * clampedFactor
        let green = startComponents[1] + (endComponents[1] - startComponents[1]) * clampedFactor
        let blue = startComponents[2] + (endComponents[2] - startComponents[2]) * clampedFactor
        let alpha = (startComponents.count > 3 ? startComponents[3] : 1.0) + 
                   ((endComponents.count > 3 ? endComponents[3] : 1.0) - 
                    (startComponents.count > 3 ? startComponents[3] : 1.0)) * clampedFactor
        
        return Color(red: red, green: green, blue: blue, opacity: alpha)
    }
    
    /// Creates a gradient for the speed legend
    static func createHeatmapGradient() -> LinearGradient {
        return LinearGradient(
            colors: heatmapColors,
            startPoint: .bottom,
            endPoint: .top
        )
    }
    
    /// Gets the color for a specific percentile in the heatmap
    static func colorForPercentile(_ percentile: Double) -> Color {
        let clampedPercentile = max(0.0, min(100.0, percentile))
        let colorIndex = Int(clampedPercentile / 20) // Each color represents 20% range
        let remainderFactor = (clampedPercentile.truncatingRemainder(dividingBy: 20)) / 20
        
        let baseIndex = min(colorIndex, heatmapColors.count - 1)
        let nextIndex = min(baseIndex + 1, heatmapColors.count - 1)
        
        if baseIndex == nextIndex {
            return heatmapColors[baseIndex]
        }
        
        return interpolateColor(
            between: heatmapColors[baseIndex],
            and: heatmapColors[nextIndex],
            factor: remainderFactor
        )
    }
}