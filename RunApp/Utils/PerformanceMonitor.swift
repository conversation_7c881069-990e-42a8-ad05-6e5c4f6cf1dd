import Foundation
import os.log

/// Performance monitoring utility for HealthKit integration and user experience
class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    private let logger = Logger(subsystem: "com.runapp.performance", category: "HealthKit")
    private var startTimes: [String: Date] = [:]
    private var metrics: [String: [TimeInterval]] = [:]
    
    private init() {}
    
    /// Start timing a performance metric
    func startTiming(_ operation: String) {
        startTimes[operation] = Date()
        logger.info("⏱️ Started timing: \(operation)")
    }
    
    /// End timing and record the metric
    func endTiming(_ operation: String) {
        guard let startTime = startTimes[operation] else {
            logger.error("❌ No start time found for operation: \(operation)")
            return
        }
        
        let duration = Date().timeIntervalSince(startTime)
        
        // Store metric
        if metrics[operation] == nil {
            metrics[operation] = []
        }
        metrics[operation]?.append(duration)
        
        // Clean up
        startTimes.removeValue(forKey: operation)
        
        logger.info("✅ Completed \(operation): \(String(format: "%.3f", duration))s")
        
        // Log performance warnings
        checkPerformanceThresholds(operation: operation, duration: duration)
    }
    
    /// Get average performance for an operation
    func getAveragePerformance(for operation: String) -> TimeInterval? {
        guard let durations = metrics[operation], !durations.isEmpty else {
            return nil
        }
        return durations.reduce(0, +) / Double(durations.count)
    }
    
    /// Get performance summary
    func getPerformanceSummary() -> [String: (average: TimeInterval, count: Int, latest: TimeInterval?)] {
        var summary: [String: (average: TimeInterval, count: Int, latest: TimeInterval?)] = [:]
        
        for (operation, durations) in metrics {
            let average = durations.reduce(0, +) / Double(durations.count)
            let count = durations.count
            let latest = durations.last
            summary[operation] = (average: average, count: count, latest: latest)
        }
        
        return summary
    }
    
    /// Log performance summary
    func logPerformanceSummary() {
        let summary = getPerformanceSummary()
        
        logger.info("📊 Performance Summary:")
        for (operation, stats) in summary.sorted(by: { $0.key < $1.key }) {
            logger.info("  \(operation): avg=\(String(format: "%.3f", stats.average))s, count=\(stats.count), latest=\(String(format: "%.3f", stats.latest ?? 0))s")
        }
    }
    
    /// Check performance thresholds and log warnings
    private func checkPerformanceThresholds(operation: String, duration: TimeInterval) {
        let thresholds: [String: TimeInterval] = [
            "healthkit_workout_start": 2.0,
            "healthkit_workout_end": 5.0,
            "healthkit_route_retrieval": 3.0,
            "workout_processing_complete": 8.0,
            "activity_save": 1.0,
            "ui_update": 0.1
        ]
        
        if let threshold = thresholds[operation], duration > threshold {
            logger.warning("⚠️ Performance warning: \(operation) took \(String(format: "%.3f", duration))s (threshold: \(String(format: "%.3f", threshold))s)")
        }
    }
    
    /// Reset all metrics (useful for testing)
    func resetMetrics() {
        startTimes.removeAll()
        metrics.removeAll()
        logger.info("🔄 Performance metrics reset")
    }
}

/// Performance monitoring extensions for common operations
extension PerformanceMonitor {
    
    /// Monitor HealthKit workout lifecycle
    func monitorHealthKitWorkout<T>(_ operation: @escaping () async throws -> T) async rethrows -> T {
        startTiming("healthkit_workout_lifecycle")
        defer { endTiming("healthkit_workout_lifecycle") }
        return try await operation()
    }
    
    /// Monitor UI update performance
    func monitorUIUpdate<T>(_ operation: @escaping () -> T) -> T {
        startTiming("ui_update")
        defer { endTiming("ui_update") }
        return operation()
    }
    
    /// Monitor data processing performance
    func monitorDataProcessing<T>(_ operationName: String, _ operation: @escaping () throws -> T) rethrows -> T {
        startTiming("data_processing_\(operationName)")
        defer { endTiming("data_processing_\(operationName)") }
        return try operation()
    }
}

/// Performance metrics for HealthKit integration
struct HealthKitPerformanceMetrics {
    let workoutStartTime: TimeInterval
    let workoutEndTime: TimeInterval
    let routeRetrievalTime: TimeInterval
    let totalProcessingTime: TimeInterval
    let routePointCount: Int
    let dataQualityScore: Double // 0.0 to 1.0
    
    var isPerformant: Bool {
        return workoutStartTime < 2.0 && 
               workoutEndTime < 5.0 && 
               routeRetrievalTime < 3.0 &&
               totalProcessingTime < 8.0
    }
    
    var performanceGrade: String {
        if totalProcessingTime < 3.0 { return "A+" }
        if totalProcessingTime < 5.0 { return "A" }
        if totalProcessingTime < 8.0 { return "B" }
        if totalProcessingTime < 12.0 { return "C" }
        return "D"
    }
} 