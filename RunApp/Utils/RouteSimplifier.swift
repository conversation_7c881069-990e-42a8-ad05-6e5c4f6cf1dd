import Foundation
import CoreLocation

/// Display contexts for adaptive tolerance
enum SimplificationContext {
    case activityList        // Small preview maps in activity list
    case activityDetail      // Full-screen activity detail map
    case realTimeView        // Live tracking view
    case backgroundProcessing // Background pre-processing
}

/// Point structure that preserves original index for efficient mapping
private struct DPPoint {
    let latitude: Double
    let longitude: Double
    let altitude: Double // ✅ ADD ALTITUDE PRESERVATION
    let originalIndex: Int
    
    var clCoordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }
}

/// High-performance Douglas-<PERSON> algorithm for route simplification
struct RouteSimplifier {
    
    /// Main simplification method using <PERSON><PERSON> algorithm
    /// - Parameters:
    ///   - coordinates: Array of Coordinate objects to simplify
    ///   - tolerance: Distance tolerance in degrees (default: 0.0001 ≈ 11 meters)
    /// - Returns: Simplified array of coordinates
    static func simplify(coordinates: [Coordinate], tolerance: Double = 0.0001) -> [Coordinate] {
        guard coordinates.count > 2 else { return coordinates }
        
        // Convert to DPPoints with original indices
        let dpPoints = coordinatesToDPPoints(coordinates)
        let simplifiedIndices = chooseSimplificationMethod(points: dpPoints, tolerance: tolerance)
        
        // Map back using original indices - O(1) operation
        return simplifiedIndices.map { coordinates[$0] }
    }
    
    /// Simplify route with maximum point limit
    /// - Parameters:
    ///   - coordinates: Array of Coordinate objects to simplify
    ///   - maxPoints: Maximum number of points in result (default: 1000)
    ///   - tolerance: Optional tolerance override
    /// - Returns: Simplified array of coordinates with at most maxPoints
    static func simplifyRoute(_ coordinates: [Coordinate], maxPoints: Int = 1000, tolerance: Double? = nil) -> [Coordinate] {
        guard coordinates.count > maxPoints else { return coordinates }
        
        // Calculate adaptive tolerance if not provided
        let effectiveTolerance = tolerance ?? calculateAdaptiveTolerance(coordinates: coordinates, targetPoints: maxPoints)
        
        var result = simplify(coordinates: coordinates, tolerance: effectiveTolerance)
        
        // If still too many points, increase tolerance iteratively
        var currentTolerance = effectiveTolerance
        while result.count > maxPoints && currentTolerance < 0.01 {
            currentTolerance *= 1.5
            result = simplify(coordinates: coordinates, tolerance: currentTolerance)
        }
        
        return result
    }
    
    /// Calculate adaptive tolerance based on target point count
    private static func calculateAdaptiveTolerance(coordinates: [Coordinate], targetPoints: Int) -> Double {
        let reductionRatio = Double(targetPoints) / Double(coordinates.count)
        
        // Base tolerance increases as more reduction is needed
        let baseTolerance = 0.0001 // ~11 meters
        let scaleFactor = 1.0 / max(reductionRatio, 0.1) // Prevent division by very small numbers
        
        return min(baseTolerance * scaleFactor, 0.01) // Cap at 0.01 degrees (~1.1km)
    }
    
    /// Adaptive tolerance based on display context
    static func adaptiveTolerance(for context: SimplificationContext) -> Double {
        switch context {
        case .activityList:
            return 0.001      // Aggressive: ≈111m (for small preview maps)
        case .activityDetail:
            return 0.00003     // Moderate: ≈3.3m (for full-screen maps)
        case .realTimeView:
            return 0.00001    // Minimal: ≈1m (for active tracking)
        case .backgroundProcessing:
            return 0.0005     // Heavy: ≈55m (for background pre-processing)
        }
    }
    
    /// Convert Coordinate array to DPPoint array with original indices
    private static func coordinatesToDPPoints(_ coordinates: [Coordinate]) -> [DPPoint] {
        return coordinates.enumerated().map { index, coord in
            DPPoint(
                latitude: coord.latitude,
                longitude: coord.longitude,
                altitude: coord.altitude ?? 0.0, // ✅ HANDLE OPTIONAL ALTITUDE
                originalIndex: index
            )
        }
    }
    
    /// Core Douglas-Peucker algorithm implementation with index tracking
    private static func douglasPeucker(
        points: [DPPoint],
        tolerance: Double
    ) -> [Int] {
        guard points.count > 2 else {
            return points.map { $0.originalIndex }
        }
        
        let startPoint = points.first!
        let endPoint = points.last!
        
        // Find the point with maximum distance from the line
        var maxDistance = 0.0
        var maxIndex = 0
        
        for i in 1..<(points.count - 1) {
            let distance = perpendicularDistance(
                point: points[i].clCoordinate,
                lineStart: startPoint.clCoordinate,
                lineEnd: endPoint.clCoordinate
            )
            
            if distance > maxDistance {
                maxDistance = distance
                maxIndex = i
            }
        }
        
        // If the maximum distance is greater than tolerance, split the line
        if maxDistance > tolerance {
            // Recursively process the two segments
            let leftSegment = Array(points[0...maxIndex])
            let rightSegment = Array(points[maxIndex..<points.count])
            
            let leftResult = douglasPeucker(points: leftSegment, tolerance: tolerance)
            let rightResult = douglasPeucker(points: rightSegment, tolerance: tolerance)
            
            // Combine results, avoiding duplicate point at the junction
            var combinedResult = leftResult
            combinedResult.append(contentsOf: rightResult.dropFirst())
            
            return combinedResult.sorted()
        } else {
            // If no point is far enough, just return start and end points
            return [startPoint.originalIndex, endPoint.originalIndex].sorted()
        }
    }
    
    /// Calculate perpendicular distance from point to line using haversine-based cross-track distance
    private static func perpendicularDistance(
        point: CLLocationCoordinate2D,
        lineStart: CLLocationCoordinate2D,
        lineEnd: CLLocationCoordinate2D
    ) -> Double {
        // Handle degenerate case where line start and end are the same
        if abs(lineStart.latitude - lineEnd.latitude) < 1e-10 &&
           abs(lineStart.longitude - lineEnd.longitude) < 1e-10 {
            return haversineDistance(from: point, to: lineStart)
        }
        
        // For very short line segments, use simple haversine distance to closest endpoint
        let lineLength = haversineDistance(from: lineStart, to: lineEnd)
        if lineLength < 10.0 { // Less than 10 meters
            let distToStart = haversineDistance(from: point, to: lineStart)
            let distToEnd = haversineDistance(from: point, to: lineEnd)
            return min(distToStart, distToEnd)
        }
        
        // Use cross-track distance for accurate GPS perpendicular distance calculation
        return crossTrackDistance(point: point, lineStart: lineStart, lineEnd: lineEnd)
    }
    
    /// Fast haversine distance calculation between two coordinates
    private static func haversineDistance(
        from: CLLocationCoordinate2D,
        to: CLLocationCoordinate2D
    ) -> Double {
        // Convert degrees to radians
        let lat1 = from.latitude * .pi / 180.0
        let lon1 = from.longitude * .pi / 180.0
        let lat2 = to.latitude * .pi / 180.0
        let lon2 = to.longitude * .pi / 180.0
        
        // Haversine formula
        let dLat = lat2 - lat1
        let dLon = lon2 - lon1
        
        let a = sin(dLat / 2) * sin(dLat / 2) +
                cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2)
        let c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        // Earth's radius in meters
        let earthRadius = 6371000.0
        return earthRadius * c
    }
    
    /// Calculate cross-track distance (perpendicular distance from point to great circle path)
    private static func crossTrackDistance(
        point: CLLocationCoordinate2D,
        lineStart: CLLocationCoordinate2D,
        lineEnd: CLLocationCoordinate2D
    ) -> Double {
        // Convert to radians
        let lat1 = lineStart.latitude * .pi / 180.0
        let lon1 = lineStart.longitude * .pi / 180.0
        let lat2 = lineEnd.latitude * .pi / 180.0
        let lon2 = lineEnd.longitude * .pi / 180.0
        let lat3 = point.latitude * .pi / 180.0
        let lon3 = point.longitude * .pi / 180.0
        
        // Distance from start to point
        let dLat13 = lat3 - lat1
        let dLon13 = lon3 - lon1
        let a13 = sin(dLat13 / 2) * sin(dLat13 / 2) +
                  cos(lat1) * cos(lat3) * sin(dLon13 / 2) * sin(dLon13 / 2)
        let c13 = 2 * atan2(sqrt(a13), sqrt(1 - a13))
        
        // Earth's radius in meters
        let earthRadius = 6371000.0
        let dist13 = earthRadius * c13
        
        // Bearing from start to end
        let y = sin(lon2 - lon1) * cos(lat2)
        let x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(lon2 - lon1)
        let bearing12 = atan2(y, x)
        
        // Bearing from start to point
        let y2 = sin(lon3 - lon1) * cos(lat3)
        let x2 = cos(lat1) * sin(lat3) - sin(lat1) * cos(lat3) * cos(lon3 - lon1)
        let bearing13 = atan2(y2, x2)
        
        // Cross-track distance
        let crossTrack = asin(sin(dist13 / earthRadius) * sin(bearing13 - bearing12)) * earthRadius
        
        return abs(crossTrack)
    }
    
    /// Helper method to calculate distance between two coordinates (now using haversine)
    private static func calculateDistance(
        from: CLLocationCoordinate2D,
        to: CLLocationCoordinate2D
    ) -> Double {
        return haversineDistance(from: from, to: to)
    }
    /// Thread-safe configuration for algorithm selection
    struct SimplificationConfig {
        // Immutable configuration - thread-safe by design
        static let iterativeThreshold: Int = 5000
        static let enableHybridMode: Bool = true
        
        // For testing purposes only - inject configuration without global state
        private let testIterativeThreshold: Int?
        private let testEnableHybridMode: Bool?
        
        static let shared = SimplificationConfig()
        
        private init(testIterativeThreshold: Int? = nil, testEnableHybridMode: Bool? = nil) {
            self.testIterativeThreshold = testIterativeThreshold
            self.testEnableHybridMode = testEnableHybridMode
        }
        
        // Create test configuration without affecting global state
        static func testConfig(iterativeThreshold: Int? = nil, enableHybridMode: Bool? = nil) -> SimplificationConfig {
            return SimplificationConfig(testIterativeThreshold: iterativeThreshold, testEnableHybridMode: enableHybridMode)
        }
        
        var effectiveIterativeThreshold: Int {
            return testIterativeThreshold ?? SimplificationConfig.iterativeThreshold
        }
        
        var effectiveEnableHybridMode: Bool {
            return testEnableHybridMode ?? SimplificationConfig.enableHybridMode
        }
    }
    
    /// Automatically choose optimal algorithm based on dataset size (thread-safe)
    private static func chooseSimplificationMethod(
        points: [DPPoint],
        tolerance: Double,
        config: SimplificationConfig = SimplificationConfig.shared
    ) -> [Int] {
        
        // Check if hybrid mode is enabled (thread-safe access)
        guard config.effectiveEnableHybridMode else {
            return douglasPeucker(points: points, tolerance: tolerance)
        }
        
        // Performance threshold based on testing (thread-safe access)
        let threshold = config.effectiveIterativeThreshold
        
        if points.count >= threshold {
            return iterativeDouglasPeucker(points: points, tolerance: tolerance)
        } else {
            return douglasPeucker(points: points, tolerance: tolerance)
        }
    }
    
    /// Iterative Douglas-Peucker implementation for large datasets with index tracking
    private static func iterativeDouglasPeucker(
        points: [DPPoint],
        tolerance: Double
    ) -> [Int] {
        guard points.count > 2 else {
            return points.map { $0.originalIndex }
        }
        
        // Use explicit stack to avoid recursion
        var stack = [(start: Int, end: Int)]()
        stack.append((0, points.count - 1))
        
        // Track which points to keep
        var keep = [Bool](repeating: false, count: points.count)
        keep[0] = true
        keep[points.count - 1] = true
        
        // Safety limits for large datasets
        let maxStackSize = min(points.count / 2, 2000) // Conservative stack limit
        var iterationCount = 0
        let maxIterations = points.count * 2 // Prevent infinite loops
        
        while !stack.isEmpty && iterationCount < maxIterations {
            iterationCount += 1
            
            let (start, end) = stack.removeLast()
            
            // Skip degenerate segments (too small to process)
            if end <= start + 1 {
                continue
            }
            
            var maxDistance = 0.0
            var maxIndex = start
            
            // Find point with maximum distance from line
            for i in (start + 1)..<end {
                if keep[i] { continue }
                
                let distance = perpendicularDistance(
                    point: points[i].clCoordinate,
                    lineStart: points[start].clCoordinate,
                    lineEnd: points[end].clCoordinate
                )
                
                if distance > maxDistance {
                    maxDistance = distance
                    maxIndex = i
                }
            }
            
            // If point is far enough, split the line
            if maxDistance > tolerance && maxIndex > start {
                keep[maxIndex] = true
                
                // Stack overflow protection - only add if under limit
                if stack.count < maxStackSize {
                    stack.append((start, maxIndex))
                    stack.append((maxIndex, end))
                } else {
                    // Fallback: keep current point and continue with remaining segments
                    // This ensures we don't lose important points even if stack is full
                    continue
                }
            }
        }
        
        // Return original indices of points marked to keep
        return points.enumerated()
            .filter { keep[$0.offset] }
            .map { $0.element.originalIndex }
            .sorted()
    }
}

// MARK: - Safe Simplification with Error Handling
extension RouteSimplifier {
    /// Handle edge cases safely
    static func safeSimplify(coordinates: [Coordinate], tolerance: Double) -> [Coordinate] {
        // Edge Case 1: Empty or single point
        guard coordinates.count > 1 else { 
            return coordinates 
        }
        
        // Edge Case 2: All points identical (GPS stationary)
        let firstCoord = coordinates[0].clCoordinate
        let allIdentical = coordinates.allSatisfy { 
            abs($0.latitude - firstCoord.latitude) < 0.000001 &&
            abs($0.longitude - firstCoord.longitude) < 0.000001
        }
        if allIdentical {
            return [coordinates.first!, coordinates.last!]
        }
        
        // Edge Case 3: Invalid tolerance
        let safeTolerance = max(tolerance, 0.000001) // Minimum 0.1m tolerance
        
        // Edge Case 4: Very short routes
        if coordinates.count < 3 {
            return coordinates
        }
        
        let simplified = simplify(coordinates: coordinates, tolerance: safeTolerance)
        
        // Edge Case 5: Over-simplification
        if simplified.count < 2 {
            return [coordinates.first!, coordinates.last!]
        }
        
        return simplified
    }
}

// MARK: - Error Handling
extension RouteSimplifier {
    /// Error types for simplification
    enum SimplificationError: Error {
        case invalidInput
        case toleranceTooLarge
        case memoryLimitExceeded
        case processingTimeout
    }
    
    /// Safe simplification with comprehensive error handling
    static func safestSimplify(coordinates: [Coordinate], tolerance: Double) -> Result<[Coordinate], SimplificationError> {
        // Input validation
        guard !coordinates.isEmpty else {
            return .failure(.invalidInput)
        }
        
        guard tolerance > 0 && tolerance < 0.1 else {
            return .failure(.toleranceTooLarge)
        }
        
        // Memory check
        let estimatedMemoryUsage = coordinates.count * MemoryLayout<Coordinate>.size
        guard estimatedMemoryUsage < 50_000_000 else { // 50MB limit
            return .failure(.memoryLimitExceeded)
        }
        
        // Timeout protection
        let startTime = Date()
        let maxProcessingTime: TimeInterval = 5.0 // 5 seconds max
        
        let result = simplify(coordinates: coordinates, tolerance: tolerance)
        
        let processingTime = Date().timeIntervalSince(startTime)
        guard processingTime < maxProcessingTime else {
            return .failure(.processingTimeout)
        }
        
        return .success(result)
    }
}

// MARK: - Performance Monitoring
struct SimplificationMetrics {
    let originalPointCount: Int
    let simplifiedPointCount: Int
    let processingTime: TimeInterval
    let memoryUsage: Int
    let tolerance: Double
    let context: SimplificationContext
    let algorithmUsed: String  // "recursive" or "iterative"
    
    var reductionPercentage: Double {
        return (1.0 - Double(simplifiedPointCount) / Double(originalPointCount)) * 100.0
    }
}

extension RouteSimplifier {
    /// Simplify with performance monitoring (thread-safe, no memory measurement)
    static func simplifyWithMetrics(
        coordinates: [Coordinate],
        tolerance: Double,
        context: SimplificationContext,
        config: SimplificationConfig = SimplificationConfig.shared
    ) -> (result: [Coordinate], metrics: SimplificationMetrics) {
        let startTime = Date()
        
        // Determine which algorithm will be used (thread-safe)
        let algorithmUsed = (config.effectiveEnableHybridMode && coordinates.count >= config.effectiveIterativeThreshold) ? "iterative" : "recursive"
        
        let result = safeSimplify(coordinates: coordinates, tolerance: tolerance)
        
        let processingTime = Date().timeIntervalSince(startTime)
        
        let metrics = SimplificationMetrics(
            originalPointCount: coordinates.count,
            simplifiedPointCount: result.count,
            processingTime: processingTime,
            memoryUsage: 0, // Memory measurement removed for reliability
            tolerance: tolerance,
            context: context,
            algorithmUsed: algorithmUsed
        )
        
        logAlgorithmPerformance(
            pointCount: coordinates.count,
            algorithm: algorithmUsed,
            processingTime: processingTime,
            reductionPercentage: metrics.reductionPercentage
        )
        
        return (result, metrics)
    }
    
    /// Log algorithm performance for monitoring
    private static func logAlgorithmPerformance(
        pointCount: Int,
        algorithm: String,
        processingTime: TimeInterval,
        reductionPercentage: Double
    ) {
        print("RouteSimplifier: \(algorithm) processed \(pointCount) points in \(String(format: "%.2f", processingTime * 1000))ms (\(String(format: "%.1f", reductionPercentage))% reduction)")
    }
    
    // Memory measurement removed - was unreliable for RouteSimplifier-specific usage
    // SimplificationMetrics.memoryUsage now returns 0 for consistency
}

// MARK: - Testing
#if DEBUG
extension RouteSimplifier {
    /// Comprehensive test suite
    static func runTestSuite() {
        print("Starting Douglas-Peucker Test Suite...")
        
        // Test 1: Empty coordinates
        testEmptyCoordinates()
        
        // Test 2: Single point
        testSinglePoint()
        
        // Test 3: Two points
        testTwoPoints()
        
        // Test 4: Straight line
        testStraightLine()
        
        // Test 5: Large dataset (iterative algorithm)
        testLargeDataset()
        
        // Test 6: Algorithm selection
        testAlgorithmSelection()
        
        // Test 7: Performance comparison
        testPerformanceComparison()
        
        // Test 8: Memory usage
        testMemoryUsage()
        
        print("Douglas-Peucker Test Suite Completed ✅")
    }
    
    private static func testEmptyCoordinates() {
        let result = safeSimplify(coordinates: [], tolerance: 0.0001)
        assert(result.isEmpty, "Empty coordinates should return empty result")
        print("✅ Empty coordinates test passed")
    }
    
    private static func testSinglePoint() {
        let coord = Coordinate(latitude: 37.7749, longitude: -122.4194, altitude: 10.0, timestamp: Date(), isPaused: false, speed: 5.0)
        let result = safeSimplify(coordinates: [coord], tolerance: 0.0001)
        assert(result.count == 1, "Single coordinate should return single result")
        print("✅ Single point test passed")
    }
    
    private static func testTwoPoints() {
        let coord1 = Coordinate(latitude: 37.7749, longitude: -122.4194, altitude: 10.0, timestamp: Date(), isPaused: false, speed: 5.0)
        let coord2 = Coordinate(latitude: 37.7849, longitude: -122.4094, altitude: 15.0, timestamp: Date(), isPaused: false, speed: 5.0)
        let result = safeSimplify(coordinates: [coord1, coord2], tolerance: 0.0001)
        assert(result.count == 2, "Two coordinates should return two results")
        print("✅ Two points test passed")
    }
    
    private static func testStraightLine() {
        // Create a straight line with 5 points
        let coords = [
            Coordinate(latitude: 37.7749, longitude: -122.4194, altitude: 10.0, timestamp: Date(), isPaused: false, speed: 5.0),
            Coordinate(latitude: 37.7759, longitude: -122.4184, altitude: 12.0, timestamp: Date(), isPaused: false, speed: 5.0),
            Coordinate(latitude: 37.7769, longitude: -122.4174, altitude: 14.0, timestamp: Date(), isPaused: false, speed: 5.0),
            Coordinate(latitude: 37.7779, longitude: -122.4164, altitude: 16.0, timestamp: Date(), isPaused: false, speed: 5.0),
            Coordinate(latitude: 37.7789, longitude: -122.4154, altitude: 18.0, timestamp: Date(), isPaused: false, speed: 5.0)
        ]
        let result = safeSimplify(coordinates: coords, tolerance: 0.0001)
        assert(result.count <= coords.count, "Simplified result should have same or fewer points")
        print("✅ Straight line test passed: \(coords.count) → \(result.count) points")
    }
    
    private static func testLargeDataset() {
        // Create a large dataset with 10,000+ points to test iterative algorithm
        var coords: [Coordinate] = []
        let baseDate = Date()
        
        for i in 0..<10000 {
            let lat = 37.7749 + Double(i) * 0.0001
            let lon = -122.4194 + Double(i) * 0.0001
            let timestamp = baseDate.addingTimeInterval(Double(i))
            let coord = Coordinate(latitude: lat, longitude: lon, altitude: 10.0 + Double(i), timestamp: timestamp, isPaused: false, speed: 5.0)
            coords.append(coord)
        }
        
        let result = safeSimplify(coordinates: coords, tolerance: 0.0001)
        assert(!result.isEmpty, "Large dataset should return non-empty result")
        assert(result.count < coords.count, "Large dataset should be simplified")
        print("✅ Large dataset test passed: \(coords.count) → \(result.count) points")
    }
    
    private static func testAlgorithmSelection() {
        // Test that correct algorithm is chosen based on point count
        let smallDataset = Array(0..<1000).map { i in
            Coordinate(latitude: 37.7749 + Double(i) * 0.0001, longitude: -122.4194, altitude: 10.0, timestamp: Date(), isPaused: false, speed: 5.0)
        }
        
        let largeDataset = Array(0..<6000).map { i in
            Coordinate(latitude: 37.7749 + Double(i) * 0.0001, longitude: -122.4194, altitude: 10.0, timestamp: Date(), isPaused: false, speed: 5.0)
        }
        
        // Test with isolated configuration (no global state mutation)
        let defaultConfig = SimplificationConfig.shared
        let (_, smallMetrics) = simplifyWithMetrics(coordinates: smallDataset, tolerance: 0.0001, context: .activityDetail, config: defaultConfig)
        let (_, largeMetrics) = simplifyWithMetrics(coordinates: largeDataset, tolerance: 0.0001, context: .activityDetail, config: defaultConfig)
        
        assert(smallMetrics.algorithmUsed == "recursive", "Small dataset should use recursive algorithm")
        assert(largeMetrics.algorithmUsed == "iterative", "Large dataset should use iterative algorithm")
        print("✅ Algorithm selection test passed: small=\(smallMetrics.algorithmUsed), large=\(largeMetrics.algorithmUsed)")
    }
    
    private static func testPerformanceComparison() {
        // Create a moderately large dataset for performance comparison
        let coords = Array(0..<5000).map { i in
            Coordinate(latitude: 37.7749 + Double(i) * 0.0001, longitude: -122.4194, altitude: 10.0, timestamp: Date(), isPaused: false, speed: 5.0)
        }
        
        // Test both algorithms using isolated configurations (no global state mutation)
        
        // Test recursive algorithm with isolated config
        let recursiveConfig = SimplificationConfig.testConfig(enableHybridMode: false)
        let recursiveStart = Date()
        let recursiveResult = simplifyWithIsolatedConfig(coordinates: coords, tolerance: 0.0001, config: recursiveConfig)
        let recursiveTime = Date().timeIntervalSince(recursiveStart)
        
        // Test iterative algorithm with isolated config
        let iterativeConfig = SimplificationConfig.testConfig(iterativeThreshold: 0, enableHybridMode: true)
        let iterativeStart = Date()
        let iterativeResult = simplifyWithIsolatedConfig(coordinates: coords, tolerance: 0.0001, config: iterativeConfig)
        let iterativeTime = Date().timeIntervalSince(iterativeStart)
        
        assert(recursiveResult.count == iterativeResult.count, "Both algorithms should produce same result count")
        print("✅ Performance comparison: recursive=\(String(format: "%.2f", recursiveTime * 1000))ms, iterative=\(String(format: "%.2f", iterativeTime * 1000))ms")
    }
    
    // Helper method for isolated testing
    private static func simplifyWithIsolatedConfig(coordinates: [Coordinate], tolerance: Double, config: SimplificationConfig) -> [Coordinate] {
        guard coordinates.count > 2 else { return coordinates }
        
        let dpPoints = coordinatesToDPPoints(coordinates)
        let simplifiedIndices = chooseSimplificationMethod(points: dpPoints, tolerance: tolerance, config: config)
        
        return simplifiedIndices.map { coordinates[$0] }
    }
    
    private static func testMemoryUsage() {
        // Test processing capability with large dataset (memory measurement removed)
        let coords = Array(0..<8000).map { i in
            Coordinate(latitude: 37.7749 + Double(i) * 0.0001, longitude: -122.4194, altitude: 10.0, timestamp: Date(), isPaused: false, speed: 5.0)
        }
        
        let startTime = Date()
        let result = safeSimplify(coordinates: coords, tolerance: 0.0001)
        let processingTime = Date().timeIntervalSince(startTime)
        
        assert(!result.isEmpty, "Large dataset test should return valid result")
        assert(result.count < coords.count, "Large dataset should be simplified")
        print("✅ Large dataset processing test passed: \(coords.count) → \(result.count) points in \(String(format: "%.2f", processingTime * 1000))ms")
    }
}
#endif