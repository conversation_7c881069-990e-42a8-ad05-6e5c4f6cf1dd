import Foundation
import SwiftData

/// Utility class for importing user data from CSV format
/// Handles profile and activity data with validation and duplicate detection
@MainActor
final class DataImporter {
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - Public Import Methods
    
    /// Import profile data from CSV content
    /// - Parameter csvContent: CSV content string
    /// - Throws: DataManagementError for validation or import failures
    func importProfileFromCSV(_ csvContent: String) async throws {
        try await Task.detached { [modelContext, self] in
            let backgroundContext = ModelContext(modelContext.container)
            
            // Parse CSV content
            let profileData = try await self.parseProfileCSV(csvContent)
            
            // Get or create profile
            let descriptor = FetchDescriptor<UserProfile>()
            let profiles = try backgroundContext.fetch(descriptor)
            let profile = profiles.first ?? UserProfile()
            
            // Update profile with imported data
            await self.updateProfileWithData(profile, data: profileData)
            
            // Save or insert profile
            if profiles.isEmpty {
                backgroundContext.insert(profile)
            }
            
            try backgroundContext.save()
        }.value
    }
    
    /// Import activities from CSV content
    /// - Parameter csvContent: CSV content string
    /// - Throws: DataManagementError for validation or import failures
    func importActivitiesFromCSV(_ csvContent: String) async throws {
        try await Task.detached { [modelContext, self] in
            let backgroundContext = ModelContext(modelContext.container)
            
            // Parse CSV content
            let activitiesData = try await self.parseActivitiesCSV(csvContent)
            
            // Get existing activities for duplicate detection
            let existingDescriptor = FetchDescriptor<RunActivity>()
            let existingActivities = try backgroundContext.fetch(existingDescriptor)
            let existingIDs = Set(existingActivities.map { $0.id })
            
            var importedCount = 0
            var skippedCount = 0
            
            // Process each activity
            for activityData in activitiesData {
                // Check for duplicates
                if existingIDs.contains(activityData.id) {
                    skippedCount += 1
                    print("Skipping duplicate activity: \(activityData.id)")
                    continue
                }
                
                // Create new activity
                let activity = await self.createActivityFromData(activityData)
                backgroundContext.insert(activity)
                importedCount += 1
            }
            
            try backgroundContext.save()
            print("Import completed: \(importedCount) activities imported, \(skippedCount) duplicates skipped")
        }.value
    }
}

// MARK: - CSV Parsing Methods

private extension DataImporter {
    
    /// Parse profile CSV content into structured data
    func parseProfileCSV(_ csvContent: String) async throws -> [String: String] {
        let lines = csvContent.components(separatedBy: .newlines).filter { !$0.isEmpty }
        guard lines.count > 1 else {
            throw DataManagementError.invalidCSVFormat
        }
        
        // Validate header
        let header = lines[0]
        guard header.contains("Field,Value") || header.contains("Field,Value,Type") else {
            throw DataManagementError.invalidCSVFormat
        }
        
        var profileData: [String: String] = [:]
        
        // Parse data rows
        for line in lines.dropFirst() {
            let components = parseCSVLine(line)
            guard components.count >= 2 else { continue }
            
            let field = components[0].trimmingCharacters(in: .whitespaces)
            let value = components[1].trimmingCharacters(in: .whitespaces)
            profileData[field] = value
        }
        
        return profileData
    }
    
    /// Parse activities CSV content into structured data
    func parseActivitiesCSV(_ csvContent: String) async throws -> [ActivityImportData] {
        let lines = csvContent.components(separatedBy: .newlines).filter { !$0.isEmpty }
        guard lines.count > 1 else {
            throw DataManagementError.invalidCSVFormat
        }
        
        // Validate header
        let header = lines[0]
        guard header.contains("ID") && header.contains("Date") && header.contains("Sport Type") else {
            throw DataManagementError.invalidCSVFormat
        }
        
        var activities: [ActivityImportData] = []
        
        // Parse data rows
        for line in lines.dropFirst() {
            do {
                let activityData = try parseActivityLine(line)
                activities.append(activityData)
            } catch {
                print("Skipping invalid activity line: \(line)")
                continue
            }
        }
        
        return activities
    }
    
    /// Parse a single CSV line handling quoted values
    func parseCSVLine(_ line: String) -> [String] {
        var components: [String] = []
        var currentComponent = ""
        var insideQuotes = false
        var i = line.startIndex
        
        while i < line.endIndex {
            let char = line[i]
            
            if char == "\"" {
                if insideQuotes && i < line.index(before: line.endIndex) && line[line.index(after: i)] == "\"" {
                    // Handle escaped quotes
                    currentComponent += "\""
                    i = line.index(i, offsetBy: 2)
                    continue
                } else {
                    insideQuotes.toggle()
                }
            } else if char == "," && !insideQuotes {
                components.append(currentComponent)
                currentComponent = ""
                i = line.index(after: i)
                continue
            } else {
                currentComponent += String(char)
            }
            
            i = line.index(after: i)
        }
        
        components.append(currentComponent)
        return components
    }
    
    /// Parse a single activity CSV line
    func parseActivityLine(_ line: String) throws -> ActivityImportData {
        let components = parseCSVLine(line)
        
        // Minimum required fields check
        guard components.count >= 10 else {
            throw DataManagementError.invalidCSVFormat
        }
        
        // Parse required fields
        guard let id = UUID(uuidString: components[0]) else {
            throw DataManagementError.dataCorrupted
        }
        
        let dateString = components[1]
        let startTimeString = components[2]
        let endTimeString = components[3]
        
        // Parse dates - try ISO format first, then fallback to date + time combination
        let startTime: Date
        let endTime: Date
        
        if components.count > 4 && !components[4].isEmpty {
            // Use ISO format if available
            let isoFormatter = ISO8601DateFormatter()
            guard let startISO = isoFormatter.date(from: components[4]),
                  let endISO = isoFormatter.date(from: components[5]) else {
                throw DataManagementError.dataCorrupted
            }
            startTime = startISO
            endTime = endISO
        } else {
            // Fallback to date + time parsing
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
            let combinedStartString = "\(dateString) \(startTimeString)"
            let combinedEndString = "\(dateString) \(endTimeString)"
            
            guard let start = dateFormatter.date(from: combinedStartString),
                  let end = dateFormatter.date(from: combinedEndString) else {
                throw DataManagementError.dataCorrupted
            }
            startTime = start
            endTime = end
        }
        
        // Parse sport type
        let sportTypeIndex = components.count > 6 ? 6 : 4
        guard let sportType = SportType(rawValue: components[sportTypeIndex]) else {
            throw DataManagementError.dataCorrupted
        }
        
        // Parse numeric values with defaults
        let durationSeconds = Double(components.count > 7 ? components[7] : "0") ?? 0
        let activeRunningTime = Double(components.count > 8 ? components[8] : "\(durationSeconds)") ?? durationSeconds
        let distanceMeters = Double(components.count > 9 ? components[9] : "0") ?? 0
        let storedPace = Double(components.count > 10 ? components[10] : "0") ?? 0
        let calories = Double(components.count > 13 ? components[13] : "0") ?? 0
        
        // Parse location
        let locationIndex = components.count > 14 ? 14 : 10
        let location = components.count > locationIndex ? components[locationIndex].trimmingCharacters(in: CharacterSet(charactersIn: "\"")) : "Unknown Location"
        
        // Parse coordinates
        var coordinates: [Coordinate] = []
        let coordinatesIndex = components.count > 16 ? 16 : 11
        if components.count > coordinatesIndex && !components[coordinatesIndex].isEmpty {
            coordinates = parseCoordinatesJSON(components[coordinatesIndex])
        }
        
        return ActivityImportData(
            id: id,
            startTime: startTime,
            endTime: endTime,
            sportType: sportType,
            activeRunningTime: activeRunningTime,
            distanceMeters: distanceMeters,
            storedPace: storedPace,
            calories: calories,
            location: location,
            coordinates: coordinates
        )
    }
    
    /// Parse coordinates from JSON string
    func parseCoordinatesJSON(_ jsonString: String) -> [Coordinate] {
        let cleanedJSON = jsonString.trimmingCharacters(in: CharacterSet(charactersIn: "\""))
        guard let data = cleanedJSON.data(using: .utf8),
              let coordinateArray = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] else {
            return []
        }
        
        var coordinates: [Coordinate] = []
        for dict in coordinateArray {
            guard let lat = dict["latitude"] as? Double,
                  let lon = dict["longitude"] as? Double else {
                continue
            }
            
            let altitude = dict["altitude"] as? Double ?? 0.0
            let timestamp = dict["timestamp"] as? TimeInterval ?? Date().timeIntervalSince1970
            
            coordinates.append(Coordinate(
                latitude: lat,
                longitude: lon,
                altitude: altitude,
                timestamp: Date(timeIntervalSince1970: timestamp),
                isPaused: false,
                speed: nil
            ))
        }
        return coordinates
    }
}

// MARK: - Data Update Methods

private extension DataImporter {
    
    /// Update profile with imported data
    func updateProfileWithData(_ profile: UserProfile, data: [String: String]) async {
        // Basic information
        if let name = data["Name"], !name.isEmpty {
            profile.name = name
        }
        if let email = data["Email"], !email.isEmpty {
            profile.email = email
        }
        if let weightString = data["Weight"], let weight = Double(weightString), weight > 0 {
            profile.weight = weight
        }
        if let heightString = data["Height"], let height = Double(heightString), height > 0 {
            profile.height = height
        }
        if let genderString = data["Gender"], let gender = Gender(rawValue: genderString) {
            profile.gender = gender
        }
        if let birthDateString = data["Birth Date"], !birthDateString.isEmpty {
            let formatter = ISO8601DateFormatter()
            profile.birthDate = formatter.date(from: birthDateString)
        }
        
        // Preferences
        if let unitsString = data["Units"], let units = UnitSystem(rawValue: unitsString) {
            profile.units = units
        }
        if let languageString = data["Language"], let language = Language(rawValue: languageString) {
            profile.updateLanguage(language)
        }
        
        // Metronome settings
        if let bpmString = data["Metronome BPM"], let bpm = Int(bpmString) {
            profile.metronomeBPM = bpm
        }
        if let enabledString = data["Metronome Enabled"], let enabled = Bool(enabledString) {
            profile.metronomeEnabled = enabled
        }
        if let volumeString = data["Metronome Volume"], let volume = Float(volumeString) {
            profile.metronomeVolume = volume
        }
        
        // Audio alert settings
        if let enabledString = data["Audio Alerts Enabled"], let enabled = Bool(enabledString) {
            profile.audioAlertsEnabled = enabled
        }
        if let rateString = data["Speech Rate"], let rate = Float(rateString) {
            profile.speechRate = rate
        }
        
        // Alert intervals
        if let enabledString = data["Distance Alert Enabled"], let enabled = Bool(enabledString) {
            profile.distanceAlertEnabled = enabled
        }
        if let intervalString = data["Distance Alert Interval"], let interval = Double(intervalString) {
            profile.distanceAlertInterval = interval
        }
        
        // Voice preferences
        if let voicePrefsString = data["Voice Preferences"], !voicePrefsString.isEmpty,
           let data = voicePrefsString.data(using: .utf8),
           let voicePrefs = try? JSONSerialization.jsonObject(with: data) as? [String: String] {
            profile.voicePreferences = voicePrefs
        }
        
        profile.lastModifiedDate = Date()
    }
    
    /// Create activity from imported data
    func createActivityFromData(_ data: ActivityImportData) async -> RunActivity {
        let activity = RunActivity(
            id: data.id,
            startTime: data.startTime,
            endTime: data.endTime,
            location: data.location,
            coordinates: data.coordinates,
            averagePace: data.storedPace,
            weight: 70.0, // Default weight - will be updated if profile weight is available
            activeRunningTime: data.activeRunningTime,
            sportType: data.sportType
        )
        
        // Set stored values
        activity.storedDistance = data.distanceMeters
        activity.storedCalories = data.calories
        
        return activity
    }
}

// MARK: - Supporting Types

private struct ActivityImportData {
    let id: UUID
    let startTime: Date
    let endTime: Date
    let sportType: SportType
    let activeRunningTime: TimeInterval
    let distanceMeters: Double
    let storedPace: Double
    let calories: Double
    let location: String
    let coordinates: [Coordinate]
} 