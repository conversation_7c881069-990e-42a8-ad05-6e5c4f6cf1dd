import SwiftUI

enum Theme: String, CaseIterable {
    case system
    case light
    case dark
    
    var displayName: String {
        switch self {
        case .system: return "system_default".localized
        case .light: return "light".localized
        case .dark: return "dark".localized
        }
    }
}

@Observable
@MainActor
final class ThemeManager {
    static let shared = ThemeManager()
    
    var currentTheme: Theme {
        didSet {
            UserDefaults.standard.setValue(currentTheme.rawValue, forKey: "selectedTheme")
            NotificationCenter.default.post(name: .themeDidChange, object: nil)
        }
    }
    
    private init() {
        let savedTheme = UserDefaults.standard.string(forKey: "selectedTheme")
        currentTheme = Theme(rawValue: savedTheme ?? "") ?? .system
    }
    
    var colorScheme: ColorScheme? {
        switch currentTheme {
        case .system:
            return nil
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
    
    @MainActor
    func setTheme(_ theme: Theme) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentTheme = theme
        }
    }
}

extension Notification.Name {
    static let themeDidChange = Notification.Name("themeDidChange")
}
