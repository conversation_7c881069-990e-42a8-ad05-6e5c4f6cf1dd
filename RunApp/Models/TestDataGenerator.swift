import Foundation
import CoreLocation
import SwiftData
import MapKit
@preconcurrency import _PhotosUI_SwiftUI

@MainActor
class TestDataGenerator {
    private let modelContext: ModelContext
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    func generateActivities(startDate: Date, endDate: Date, skipDates: [Date] = []) async {
        let calendar = Calendar.current
        var currentDate = startDate
        
        while currentDate <= endDate {
            // Skip specified dates
            if skipDates.contains(where: { calendar.isDate($0, inSameDayAs: currentDate) }) {
                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
                continue
            }
            
            // 70% chance of having an activity on any given day
            if Double.random(in: 0...1) <= 0.7 {
                await generateActivityForDate(currentDate)
            }
            
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
    }
    
    private func generateActivityForDate(_ date: Date) async {
        let sportType = SportType.allCases.randomElement() ?? .run
        
        // Generate start time between 1 AM and 23 PM
        let calendar = Calendar.current
        var startComponents = calendar.dateComponents([.year, .month, .day], from: date)
        startComponents.hour = Int.random(in: 1...23)
        startComponents.minute = Int.random(in: 0...59)
        
        guard let startTime = calendar.date(from: startComponents) else { return }
        
        // Duration between 1 and 180 minutes
        let durationMinutes = Double.random(in: 1...180)
        let endTime = startTime.addingTimeInterval(durationMinutes * 60)
        
        // Calculate realistic pace based on sport type
        let basePaceMinPerKm: Double
        switch sportType {
        case .run:
            basePaceMinPerKm = Double.random(in: 5...7)
        case .walk:
            basePaceMinPerKm = Double.random(in: 9...12)
        case .hike:
            basePaceMinPerKm = Double.random(in: 12...15)
        case .bike:
            basePaceMinPerKm = Double.random(in: 3...4)
        }
        
        // Calculate distance using duration and pace
        let distanceInKm = durationMinutes / basePaceMinPerKm
        
        // Generate synthetic route with approximate target distance
        let coordinates = generateSyntheticRoute(duration: durationMinutes, targetDistance: distanceInKm)
        
        let activity = RunActivity(
            startTime: startTime,
            endTime: endTime,
            location: "Test Location",
            coordinates: coordinates,
            averagePace: basePaceMinPerKm,
            weight: Double.random(in: 65...85),
            activeRunningTime: durationMinutes * 60,
            sportType: sportType
        )
        
        modelContext.insert(activity)
    }
    
    private func generateSyntheticRoute(duration: Double, targetDistance: Double) -> [Coordinate] {
        let baseLat = 41.8781  // Chicago latitude
        let baseLong = -87.6298 // Chicago longitude
        
        var coordinates: [Coordinate] = []
        let numberOfPoints = Int(duration * 2) // 2 points per minute
        
        // Calculate step size to achieve target distance
        // Approximate conversion: 1 degree latitude ≈ 111 km
        let stepSize = (targetDistance / 111.0) / Double(numberOfPoints)
        
        for i in 0..<numberOfPoints {
            // Create variations to achieve approximate target distance
            let latVariation = Double.random(in: -stepSize...stepSize)
            let longVariation = Double.random(in: -stepSize...stepSize)
            
            let coordinate = Coordinate(
                latitude: baseLat + latVariation,
                longitude: baseLong + longVariation,
                altitude: Double.random(in: 10.0...50.0), // Random altitude between 10-50 meters
                timestamp: Date().addingTimeInterval(Double(i) * 30),
                isPaused: false,
                speed: Double.random(in: 1.0...5.0) // Random speed between 1-5 m/s
            )
            
            coordinates.append(coordinate)
        }
        
        return coordinates
    }
    
    /// Generate activities with large location data for deletion testing
    func generateLargeLocationActivity(coordinateCount: Int = 5000) async -> RunActivity {
        let startTime = Date().addingTimeInterval(-3600) // 1 hour ago
        let endTime = Date()
        
        // Generate large coordinate array
        var coordinates: [Coordinate] = []
        let baseLatitude = 37.7749 // San Francisco
        let baseLongitude = -122.4194
        
        for i in 0..<coordinateCount {
            let offset = Double(i) * 0.0001
            coordinates.append(Coordinate(
                latitude: baseLatitude + offset,
                longitude: baseLongitude + offset,
                altitude: Double.random(in: 10.0...100.0), // Random altitude between 10-100 meters
                timestamp: startTime.addingTimeInterval(Double(i) * 0.72), // Every 0.72 seconds
                isPaused: false,
                speed: Double.random(in: 2.0...5.0)
            ))
        }
        
        let activity = RunActivity(
            startTime: startTime,
            endTime: endTime,
            location: "Test Location - Large Data",
            coordinates: coordinates,
            averagePace: 5.0,
            weight: 70.0,
            activeRunningTime: 3600,
            sportType: .run
        )
        
        modelContext.insert(activity)
        try? modelContext.save()
        
        return activity
    }
    
    /// Generate multiple large activities for stress testing
    func generateLargeActivitiesForTesting() async {
        print("TestDataGenerator: Creating large activities for deletion testing...")
        
        // Create activities with different sizes
        let testSizes = [1000, 5000, 10000]
        
        for size in testSizes {
            let activity = await generateLargeLocationActivity(coordinateCount: size)
            print("TestDataGenerator: Created activity with \(activity.coordinates.count) coordinates")
        }
        
        print("TestDataGenerator: Completed large activity generation")
    }
}