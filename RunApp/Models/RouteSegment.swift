import Foundation
import CoreLocation

struct RouteSegment: Codable {
    let coordinates: [CLLocation]
    let isPaused: Bool
    
    // MARK: - Codable Implementation
    
    private enum CodingKeys: String, CodingKey {
        case coordinates
        case isPaused
    }
    
    // MARK: - Location Data Structure
    private struct LocationData: Codable {
        let latitude: Double
        let longitude: Double
        let timestamp: Date
        let horizontalAccuracy: Double
        let verticalAccuracy: Double
        let altitude: Double
        let speed: Double
        let course: Double
    }
    
    // Custom encoding to handle CLLocation serialization
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        // Convert CLLocation array to encodable format
        let locationData = coordinates.map { location in
            LocationData(
                latitude: location.coordinate.latitude,
                longitude: location.coordinate.longitude,
                timestamp: location.timestamp,
                horizontalAccuracy: location.horizontalAccuracy,
                verticalAccuracy: location.verticalAccuracy,
                altitude: location.altitude,
                speed: location.speed,
                course: location.course
            )
        }
        
        try container.encode(locationData, forKey: .coordinates)
        try container.encode(isPaused, forKey: .isPaused)
    }
    
    // Custom decoding to reconstruct CLLocation objects
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        let locationData = try container.decode([LocationData].self, forKey: .coordinates)
        self.coordinates = locationData.map { data in
            CLLocation(
                coordinate: CLLocationCoordinate2D(
                    latitude: data.latitude,
                    longitude: data.longitude
                ),
                altitude: data.altitude,
                horizontalAccuracy: data.horizontalAccuracy,
                verticalAccuracy: data.verticalAccuracy,
                course: data.course,
                speed: data.speed,
                timestamp: data.timestamp
            )
        }
        
        self.isPaused = try container.decode(Bool.self, forKey: .isPaused)
    }
    
    // Standard initializer
    init(coordinates: [CLLocation], isPaused: Bool) {
        self.coordinates = coordinates
        self.isPaused = isPaused
    }
}