import CoreLocation
import Foundation
import SwiftData
import SwiftUI
import UIKit

// MARK: - Enhanced Analytics Data Structures

enum LapUnit: String, Codable, CaseIterable {
  case metric = "km"
  case imperial = "mi"

  var distance: Double {
    switch self {
    case .metric: return 1000.0  // 1 km in meters
    case .imperial: return 1609.34  // 1 mile in meters
    }
  }
}

struct LapData: Codable {
  let lapNumber: Int
  let distance: Double  // Actual distance covered in this lap
  let time: TimeInterval  // Time taken for this lap
  let averagePace: Double  // min/km or min/mi
  let averageSpeed: Double  // m/s
  let startTime: Date
  let endTime: Date
  let startCoordinateIndex: Int  // Index in coordinates array
  let endCoordinateIndex: Int  // Index in coordinates array
  let unit: LapUnit  // .metric (1km) or .imperial (1mi)
}

struct WorkoutStats: Codable {
  // Pace Analytics
  let averagePace: Double  // min/km
  let bestPace: Double  // min/km (fastest sustained pace)

  // Speed Analytics
  let averageSpeed: Double  // m/s
  let maxSpeed: Double  // m/s

  // Time Analytics
  let totalTime: TimeInterval  // Including pauses
  let activeTime: TimeInterval  // Excluding pauses
  let pauseTime: TimeInterval  // Total pause duration

  // Calorie Analytics
  let activeCalories: Double  // Calories burned during active movement
  let pauseCalories: Double  // Calories burned during pauses
  let totalCalories: Double  // activeCalories + pauseCalories

  // Lap Data
  let metricLaps: [LapData]  // 1km laps
  let imperialLaps: [LapData]  // 1 mile laps
}

@Model
final class RunActivity {
  var id: UUID = UUID()
  var startTime: Date = Date()
  var endTime: Date = Date()
  var location: String = ""
  var coordinates: [Coordinate] = []  // Raw GPS data (NEVER MODIFY)
  var storedPace: Double = 0.0  // in minutes per kilometer
  var weight: Double = 0.0  // in kg
  var activeRunningTime: TimeInterval = 0.0  // Store actual running time
  var sportType: SportType = SportType.run  // Default to .run

  // --- PERFORMANCE FIX: ADD STORED PROPERTIES FOR EXPENSIVE CALCULATIONS ---
  var storedDistance: Double?
  var storedCalories: Double?

  // --- ENHANCED OPTIMIZATION: COMPREHENSIVE PRE-CALCULATED DATA ---
  // Note: SpeedSegments will be calculated on-demand from pre-simplified coordinates
  // since Color isn't Codable for SwiftData storage

  @Attribute(.externalStorage)
  var storedWorkoutStats: WorkoutStats? = nil

  // TRANSIENT CACHING SYSTEM (Memory only - not stored in SwiftData)
  @Transient private var simplificationCache: [String: [Coordinate]] = [:]
  @Transient private var cacheTimestamp: Date?
  @Transient private var maxCacheSize: Int = 5  // Limit memory usage

  init(
    id: UUID = UUID(),
    startTime: Date = Date(),
    endTime: Date = Date(),
    location: String = "",
    coordinates: [Coordinate] = [],
    averagePace: Double = 0.0,  // This will set storedPace
    weight: Double = 0.0,
    activeRunningTime: TimeInterval = 0.0,
    sportType: SportType = SportType.run
  ) {
    self.id = id
    self.startTime = startTime
    self.endTime = endTime
    self.location = location
    self.coordinates = coordinates
    self.storedPace = averagePace  // Initialize storedPace with averagePace parameter
    self.weight = weight
    self.activeRunningTime = activeRunningTime
    self.sportType = sportType
  }

  /// Optimized initializer: calculates comprehensive stats from raw data, stores simplified coordinates
  /// This method avoids side effects by using static helper methods for calculations
  init(
    rawCoordinates: [Coordinate],
    simplificationTolerance: Double = 0.0001,
    startTime: Date = Date(),
    endTime: Date = Date(),
    location: String = "",
    weight: Double = 0.0,
    activeRunningTime: TimeInterval = 0.0,
    sportType: SportType = SportType.run
  ) {
    // Step 1: Calculate comprehensive stats from raw data using static methods
    let workoutStats = Self.calculateWorkoutStats(
      from: rawCoordinates,
      startTime: startTime,
      endTime: endTime,
      activeRunningTime: activeRunningTime,
      weight: weight,
      sportType: sportType
    )

    // Step 2: Apply Douglas-Peucker simplification
    let simplifiedCoords = RouteSimplifier.safeSimplify(
      coordinates: rawCoordinates,
      tolerance: simplificationTolerance
    )

    // Step 3: Initialize all properties with their final values (no side effects)
    self.id = UUID()
    self.startTime = startTime
    self.endTime = endTime
    self.location = location
    self.coordinates = simplifiedCoords
    self.storedPace = workoutStats.averagePace
    self.weight = weight
    self.activeRunningTime = activeRunningTime
    self.sportType = sportType
    self.storedDistance = Self.calculateDistanceStatic(from: rawCoordinates)
    self.storedCalories = workoutStats.totalCalories
    self.storedWorkoutStats = workoutStats
  }

  var route: [Coordinate] {
    coordinates
  }

  var duration: TimeInterval {
    // Use active running time instead of total time
    activeRunningTime
  }

  var calories: Double {
    if let storedCalories = storedCalories {
      return storedCalories
    }
    // Fallback for older activities or if not finalized
    let value = calculateCalories()
    self.storedCalories = value
    return value
  }

  // MARK: - Enhanced Analytics Accessors

  /// Get comprehensive workout stats without recalculation
  var workoutStats: WorkoutStats {
    if let stored = storedWorkoutStats {
      print(
        "🔥 CALORIE DEBUG: workoutStats using stored data - activeCalories: \(stored.activeCalories)"
      )
      return stored  // ✅ Use pre-calculated stats
    }
    // Legacy fallback
    print("🔥 CALORIE DEBUG: workoutStats using legacy fallback")
    let fallback = calculateWorkoutStatsLegacy()
    print("🔥 CALORIE DEBUG: legacy fallback activeCalories: \(fallback.activeCalories)")
    return fallback
  }

  /// Get speed segments without recalculation (calculated from pre-simplified coordinates)
  var speedSegments: [SpeedSegment] {
    // Calculate from already-simplified coordinates - much faster than original raw data
    let speedAnalysis = SpeedAnalyzer.analyzeRouteSpeed(coordinates)
    return SpeedAnalyzer.createSpeedSegments(from: coordinates, using: speedAnalysis)
  }

  // Convenient accessors for UI
  var averagePaceEnhanced: Double { workoutStats.averagePace }
  var bestPace: Double { workoutStats.bestPace }
  var averageSpeed: Double { workoutStats.averageSpeed }
  var maxSpeed: Double { workoutStats.maxSpeed }
  var totalTime: TimeInterval { workoutStats.totalTime }
  var activeTime: TimeInterval { workoutStats.activeTime }
  var pauseTime: TimeInterval { workoutStats.pauseTime }
  var activeCalories: Double {
    let value = workoutStats.activeCalories
    print(
      "🔥 CALORIE DEBUG: RunActivity.activeCalories computed property called, returning: \(value)")
    return value
  }
  var pauseCalories: Double { workoutStats.pauseCalories }
  var totalCalories: Double { workoutStats.totalCalories }
  var metricLaps: [LapData] { workoutStats.metricLaps }
  var imperialLaps: [LapData] { workoutStats.imperialLaps }

  var name: String {
    let calendar = Calendar.current
    let hour = calendar.component(.hour, from: endTime)
    let activityType = sportType.rawValue.capitalized

    switch hour {
    case 5..<12:
      return "Morning \(activityType)"
    case 12..<17:
      return "Afternoon \(activityType)"
    case 17..<21:
      return "Evening \(activityType)"
    default:
      return "Night \(activityType)"
    }
  }

  var timeOfDay: String {
    let hour = Calendar.current.component(.hour, from: startTime)
    switch hour {
    case 5..<12:
      return "morning"
    case 12..<17:
      return "afternoon"
    case 17..<21:
      return "evening"
    default:
      return "night"
    }
  }

  var formattedStartTime: String {
    let formatter = DateFormatter()
    formatter.dateStyle = .none
    formatter.timeStyle = .short
    formatter.locale = Locale.current
    return formatter.string(from: startTime)
  }

  var formattedEndTime: String {
    let formatter = DateFormatter()
    formatter.dateStyle = .none
    formatter.timeStyle = .short
    formatter.locale = Locale.current
    return formatter.string(from: endTime)
  }

  var formattedDate: String {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    formatter.timeStyle = .none
    formatter.locale = Locale.current
    return formatter.string(from: startTime)
  }

  var formattedDuration: String {
    let hours = Int(duration) / 3600
    let minutes = Int(duration) / 60 % 60
    let seconds = Int(duration) % 60

    if hours > 0 {
      return String(format: "%d:%02d:%02d", hours, minutes, seconds)
    } else {
      return String(format: "%d:%02d", minutes, seconds)
    }
  }

  var formattedTotalTime: String {
    let totalTimeValue = workoutStats.totalTime
    let hours = Int(totalTimeValue) / 3600
    let minutes = Int(totalTimeValue) / 60 % 60
    let seconds = Int(totalTimeValue) % 60

    if hours > 0 {
      return String(format: "%d:%02d:%02d", hours, minutes, seconds)
    } else {
      return String(format: "%d:%02d", minutes, seconds)
    }
  }

  // Convert pace between unit systems
  func convertPace(pace: Double, from sourceUnit: UnitSystem, to targetUnit: UnitSystem) -> Double {
    guard sourceUnit != targetUnit else { return pace }

    if sourceUnit == .metric && targetUnit == .imperial {
      // Convert min/km to min/mi
      return pace * 1.60934  // km to mile conversion factor
    } else {
      // Convert min/mi to min/km
      return pace / 1.60934
    }
  }

  // PERFORMANCE FIX: Removed expensive SwiftData fetch from computed property
  // This method now accepts unitSystem as parameter instead of fetching it
  func averagePace(for unitSystem: UnitSystem) -> Double {
    // Return stored pace in the requested unit
    return unitSystem == .metric
      ? storedPace : convertPace(pace: storedPace, from: .metric, to: .imperial)
  }

  // Set average pace for the specified unit system
  func setAveragePace(_ newValue: Double, for unitSystem: UnitSystem) {
    // Store the new pace in minutes per kilometer (metric) format
    storedPace =
      unitSystem == .metric ? newValue : convertPace(pace: newValue, from: .imperial, to: .metric)
  }

  // Legacy computed property for backward compatibility - uses .metric as default
  var averagePace: Double {
    get { averagePace(for: .metric) }
    set { setAveragePace(newValue, for: .metric) }
  }

  // PERFORMANCE FIX: Method that accepts unitSystem parameter instead of fetching it
  func formattedPace(for unitSystem: UnitSystem) -> String {
    let paceValue = averagePace(for: unitSystem)
    let minutes = Int(paceValue)
    let seconds = Int((paceValue - Double(minutes)) * 60)

    // Format pace according to unit system
    let unit = unitSystem == .metric ? "/km" : "/mi"
    return String(format: "%d'%02d\"%@", minutes, seconds, unit)
  }

  // PERFORMANCE FIX: Method that accepts unitSystem parameter instead of fetching it
  func formattedDistance(for unitSystem: UnitSystem) -> String {
    // Use the formatDistance method from UnitSystem
    return unitSystem.formatDistance(distance, abbreviated: true)
  }

  // Legacy computed properties for backward compatibility - use .metric as default
  var formattedPace: String {
    formattedPace(for: .metric)
  }

  var formattedDistance: String {
    formattedDistance(for: .metric)
  }

  // Calculate total distance using only active coordinates
  var distance: Double {
    if let storedDistance = storedDistance {
      return storedDistance
    }
    // Fallback for older activities or if not finalized
    let value = calculateDistance()
    self.storedDistance = value
    return value
  }

  /// Performs all final, expensive calculations and stores the results.
  /// Call this once before inserting the activity into SwiftData.
  func finalizeActivity() {
    self.storedDistance = calculateDistance()
    self.storedCalories = calculateCalories()
  }

  /// Private method that performs the actual distance calculation
  private func calculateDistance() -> Double {
    var totalDistance = 0.0
    // Group coordinates into segments by isPaused status
    var currentSegment: [Coordinate] = []
    var segments: [[Coordinate]] = []

    for coordinate in coordinates {
      if currentSegment.isEmpty {
        currentSegment.append(coordinate)
      } else if currentSegment[0].isPaused == coordinate.isPaused {
        currentSegment.append(coordinate)
      } else {
        segments.append(currentSegment)
        currentSegment = [coordinate]
      }
    }
    if !currentSegment.isEmpty {
      segments.append(currentSegment)
    }

    // Only calculate distances for active segments
    let activeSegments = segments.filter { !$0.isEmpty && !$0[0].isPaused }

    for segment in activeSegments {
      guard segment.count > 1 else { continue }

      for i in 0..<(segment.count - 1) {
        let loc1 = CLLocation(
          latitude: segment[i].latitude,
          longitude: segment[i].longitude)
        let loc2 = CLLocation(
          latitude: segment[i + 1].latitude,
          longitude: segment[i + 1].longitude)

        // Calculate distance between consecutive points
        let segmentDistance = loc1.distance(from: loc2)

        // Filter out unrealistic jumps (e.g., GPS errors)
        if segmentDistance < 100 {  // 100 meters threshold for realistic movement
          totalDistance += segmentDistance
        }
      }
    }

    return totalDistance
  }

  /// Private method that performs the actual calorie calculation
  private func calculateCalories() -> Double {
    return CalorieCalculator.calculateCalories(
      weight: weight,
      sportType: sportType,
      activeTime: activeRunningTime,
      storedPace: storedPace,
      hasMovement: hadMeaningfulMovement()
    )
  }

  // MARK: - Enhanced Stats Calculation (Static Methods)

  /// Calculate comprehensive workout stats from raw data
  static func calculateWorkoutStats(
    from coordinates: [Coordinate],
    startTime: Date,
    endTime: Date,
    activeRunningTime: TimeInterval,
    weight: Double,
    sportType: SportType
  ) -> WorkoutStats {

    // 1. Separate active and paused segments
    let segments = groupCoordinatesByPauseStatus(coordinates)
    let activeSegments = segments.filter { !$0.isEmpty && !$0[0].isPaused }
    let pausedSegments = segments.filter { !$0.isEmpty && $0[0].isPaused }

    // 2. Calculate pace and speed analytics
    let paceStats = calculatePaceAndSpeedStats(from: activeSegments)

    // 3. Calculate time analytics
    let totalTime = endTime.timeIntervalSince(startTime)
    let pauseTime = totalTime - activeRunningTime

    // 4. Calculate calorie analytics using pre-calculated pace stats
    let calorieStats = calculateCalorieStats(
      activeSegments: activeSegments,
      pausedSegments: pausedSegments,
      activeTime: activeRunningTime,
      pauseTime: pauseTime,
      weight: weight,
      sportType: sportType,
      paceStats: paceStats
    )

    // 5. Calculate lap data
    let metricLaps = calculateLaps(from: coordinates, unit: .metric, startTime: startTime)
    let imperialLaps = calculateLaps(from: coordinates, unit: .imperial, startTime: startTime)

    return WorkoutStats(
      averagePace: paceStats.averagePace,
      bestPace: paceStats.bestPace,
      averageSpeed: paceStats.averageSpeed,
      maxSpeed: paceStats.maxSpeed,
      totalTime: totalTime,
      activeTime: activeRunningTime,
      pauseTime: pauseTime,
      activeCalories: calorieStats.activeCalories,
      pauseCalories: calorieStats.pauseCalories,
      totalCalories: calorieStats.totalCalories,
      metricLaps: metricLaps,
      imperialLaps: imperialLaps
    )
  }

  /// Group coordinates by pause status
  static func groupCoordinatesByPauseStatus(_ coordinates: [Coordinate]) -> [[Coordinate]] {
    var segments: [[Coordinate]] = []
    var currentSegment: [Coordinate] = []

    for coordinate in coordinates {
      if currentSegment.isEmpty {
        currentSegment.append(coordinate)
      } else if currentSegment[0].isPaused == coordinate.isPaused {
        currentSegment.append(coordinate)
      } else {
        segments.append(currentSegment)
        currentSegment = [coordinate]
      }
    }
    if !currentSegment.isEmpty {
      segments.append(currentSegment)
    }

    return segments
  }

  /// Calculate pace and speed statistics
  static func calculatePaceAndSpeedStats(from activeSegments: [[Coordinate]]) -> (
    averagePace: Double, bestPace: Double, averageSpeed: Double, maxSpeed: Double
  ) {
    // BUGFIX: Handle empty segments case immediately
    guard !activeSegments.isEmpty else {
      return (averagePace: 0, bestPace: 0, averageSpeed: 0, maxSpeed: 0)
    }

    var totalDistance = 0.0
    var totalTime: TimeInterval = 0
    var maxSpeed = 0.0
    var bestPace = Double.greatestFiniteMagnitude
    var validSpeedReadings: [Double] = []

    for segment in activeSegments {
      guard segment.count > 1 else { continue }

      for i in 0..<(segment.count - 1) {
        let loc1 = CLLocation(latitude: segment[i].latitude, longitude: segment[i].longitude)
        let loc2 = CLLocation(
          latitude: segment[i + 1].latitude, longitude: segment[i + 1].longitude)

        let segmentDistance = loc1.distance(from: loc2)
        if segmentDistance < 100 {  // Filter GPS errors
          totalDistance += segmentDistance

          let timeDiff = segment[i + 1].timestamp.timeIntervalSince(segment[i].timestamp)
          totalTime += timeDiff

          // Calculate instantaneous speed and pace
          if timeDiff > 0 {
            let speed = segmentDistance / timeDiff
            validSpeedReadings.append(speed)
            maxSpeed = max(maxSpeed, speed)

            if speed > 0.5 {  // Minimum speed threshold for valid pace
              let pace = (1000.0 / speed) / 60.0  // min/km
              if pace < 30.0 {  // Maximum reasonable pace
                bestPace = min(bestPace, pace)
              }
            }
          }
        }
      }
    }

    let averageSpeed =
      validSpeedReadings.isEmpty
      ? 0 : validSpeedReadings.reduce(0, +) / Double(validSpeedReadings.count)
    let averagePace = averageSpeed > 0 ? (1000.0 / averageSpeed) / 60.0 : 0

    // BUGFIX: Ensure bestPace is valid - reset to 0 if no valid readings were found
    let safeBestPace =
      (bestPace == Double.greatestFiniteMagnitude || validSpeedReadings.isEmpty) ? 0 : bestPace

    return (
      averagePace: averagePace,
      bestPace: safeBestPace,
      averageSpeed: averageSpeed,
      maxSpeed: maxSpeed
    )
  }

  /// Calculate calorie breakdown for active vs pause periods using pre-calculated pace and distance
  static func calculateCalorieStats(
    activeSegments: [[Coordinate]],
    pausedSegments: [[Coordinate]],
    activeTime: TimeInterval,
    pauseTime: TimeInterval,
    weight: Double,
    sportType: SportType,
    paceStats: (averagePace: Double, bestPace: Double, averageSpeed: Double, maxSpeed: Double)
  ) -> (activeCalories: Double, pauseCalories: Double, totalCalories: Double) {

    // BUGFIX: Handle edge case with no data - prevent crashes with zero workout
    guard weight > 0 && (activeTime > 0 || pauseTime > 0) else {
      return (activeCalories: 0, pauseCalories: 0, totalCalories: 0)
    }

    // Calculate active distance from active segments
    var activeDistance = 0.0
    for segment in activeSegments {
      guard segment.count > 1 else { continue }

      for i in 0..<(segment.count - 1) {
        let loc1 = CLLocation(latitude: segment[i].latitude, longitude: segment[i].longitude)
        let loc2 = CLLocation(
          latitude: segment[i + 1].latitude, longitude: segment[i + 1].longitude)

        let segmentDistance = loc1.distance(from: loc2)
        if segmentDistance < 100 {  // Filter GPS errors
          activeDistance += segmentDistance
        }
      }
    }

    // Calculate pause distance from paused segments
    var pauseDistance = 0.0
    for segment in pausedSegments {
      guard segment.count > 1 else { continue }

      for i in 0..<(segment.count - 1) {
        let loc1 = CLLocation(latitude: segment[i].latitude, longitude: segment[i].longitude)
        let loc2 = CLLocation(
          latitude: segment[i + 1].latitude, longitude: segment[i + 1].longitude)

        let segmentDistance = loc1.distance(from: loc2)
        if segmentDistance < 100 {  // Filter GPS errors
          pauseDistance += segmentDistance
        }
      }
    }

    // Calculate active calories using active distance and average pace (unchanged)
    let activeCalories = CalorieCalculator.calculateCalories(
      weight: weight,
      sportType: sportType,
      activeTime: activeTime,
      distance: activeDistance,
      storedPace: paceStats.averagePace,
      hasMovement: activeDistance > 10
    )

    // PHASE 2: Enhanced calorie calculation using unified dynamic MET system
    var pauseCalories = 0.0
    if pauseTime > 0 {
      // NEW APPROACH: Calculate calories segment-by-segment using dynamic MET
      pauseCalories = calculateDynamicPauseCalories(
        pausedSegments: pausedSegments,
        weight: weight,
        sportType: sportType
      )
    }

    // Ensure non-negative values
    let finalActiveCalories = max(0, activeCalories)
    let finalPauseCalories = max(0, pauseCalories)
    let totalCalories = finalActiveCalories + finalPauseCalories

    return (
      activeCalories: finalActiveCalories,
      pauseCalories: finalPauseCalories,
      totalCalories: totalCalories
    )
  }

  /// PHASE 2: Calculate pause calories using dynamic MET system (same logic as real-time tracking)
  /// This ensures consistency between real-time display and saved activity calculations
  private static func calculateDynamicPauseCalories(
    pausedSegments: [[Coordinate]],
    weight: Double,
    sportType: SportType
  ) -> Double {
    var totalPauseCalories = 0.0

    for segment in pausedSegments {
      guard segment.count > 1 else {
        // Single point segment - treat as brief stationary period
        if segment.first != nil {
          let duration = 5.0  // Assume 5 seconds for single point
          let stationaryCalories = CalorieCalculator.calculateCalories(
            weight: weight,
            sportType: sportType,
            activeTime: duration,
            storedPace: 20.0  // Stationary pace triggers resting MET
          )
          totalPauseCalories += stationaryCalories
        }
        continue
      }

      // Calculate segment distance and time
      var segmentDistance = 0.0
      var segmentTime: TimeInterval = 0

      for i in 0..<(segment.count - 1) {
        let loc1 = CLLocation(latitude: segment[i].latitude, longitude: segment[i].longitude)
        let loc2 = CLLocation(
          latitude: segment[i + 1].latitude, longitude: segment[i + 1].longitude)

        let pointDistance = loc1.distance(from: loc2)
        if pointDistance < 100 {  // Filter GPS errors
          segmentDistance += pointDistance
        }

        segmentTime += segment[i + 1].timestamp.timeIntervalSince(segment[i].timestamp)
      }

      // Calculate segment pace using same logic as real-time tracking
      var segmentPace: Double
      if segmentDistance > 10 && segmentTime > 5 {
        // Meaningful movement detected - calculate actual pace
        let distanceKm = segmentDistance / 1000.0
        let timeMinutes = segmentTime / 60.0
        segmentPace = timeMinutes / distanceKm

        // Validate pace range (same as real-time validation)
        if segmentPace > 60.0 {
          // GPS error resulted in unrealistic slow pace - treat as stationary
          segmentPace = 20.0
        } else if segmentPace < 0.5 {
          // GPS error resulted in unrealistic fast pace - use moderate pace
          segmentPace = 6.0
        }
      } else {
        // Little/no movement - use stationary pace (triggers resting MET)
        segmentPace = 20.0
      }

      // Use unified CalorieCalculator with dynamic MET (same as real-time)
      let segmentCalories = CalorieCalculator.calculateCalories(
        weight: weight,
        sportType: sportType,  // Use original sport type, not forced .walk
        activeTime: segmentTime,
        distance: segmentDistance,
        storedPace: segmentPace
      )

      totalPauseCalories += segmentCalories
    }

    return totalPauseCalories
  }

  /// Calculate lap data for given unit (metric/imperial)
  static func calculateLaps(from coordinates: [Coordinate], unit: LapUnit, startTime: Date)
    -> [LapData]
  {
    guard !coordinates.isEmpty else { return [] }

    var laps: [LapData] = []
    var currentLapDistance = 0.0
    var lapStartIndex = 0
    var lapStartTime = startTime
    var lapNumber = 1

    // Filter to active coordinates only
    let activeCoordinates = coordinates.filter { !$0.isPaused }
    guard activeCoordinates.count > 1 else { return [] }

    for i in 0..<(activeCoordinates.count - 1) {
      let loc1 = CLLocation(
        latitude: activeCoordinates[i].latitude, longitude: activeCoordinates[i].longitude)
      let loc2 = CLLocation(
        latitude: activeCoordinates[i + 1].latitude, longitude: activeCoordinates[i + 1].longitude)

      let segmentDistance = loc1.distance(from: loc2)
      if segmentDistance < 100 {  // Filter GPS errors
        currentLapDistance += segmentDistance

        // Check if we've completed a lap
        if currentLapDistance >= unit.distance {
          let lapEndTime = activeCoordinates[i + 1].timestamp
          let lapTime = lapEndTime.timeIntervalSince(lapStartTime)
          let lapAverageSpeed = currentLapDistance / lapTime
          let lapAveragePace = lapAverageSpeed > 0 ? (1000.0 / lapAverageSpeed) / 60.0 : 0

          let lap = LapData(
            lapNumber: lapNumber,
            distance: currentLapDistance,
            time: lapTime,
            averagePace: lapAveragePace,
            averageSpeed: lapAverageSpeed,
            startTime: lapStartTime,
            endTime: lapEndTime,
            startCoordinateIndex: lapStartIndex,
            endCoordinateIndex: i + 1,
            unit: unit
          )

          laps.append(lap)

          // Reset for next lap
          currentLapDistance = 0.0
          lapStartIndex = i + 1
          lapStartTime = lapEndTime
          lapNumber += 1
        }
      }
    }

    return laps
  }

  /// Calculate distance from coordinate array without requiring instance state
  static func calculateDistanceStatic(from coordinates: [Coordinate]) -> Double {
    let segments = groupCoordinatesByPauseStatus(coordinates)
    let activeSegments = segments.filter { !$0.isEmpty && !$0[0].isPaused }

    var totalDistance = 0.0
    for segment in activeSegments {
      guard segment.count > 1 else { continue }

      for i in 0..<(segment.count - 1) {
        let loc1 = CLLocation(latitude: segment[i].latitude, longitude: segment[i].longitude)
        let loc2 = CLLocation(
          latitude: segment[i + 1].latitude, longitude: segment[i + 1].longitude)

        let segmentDistance = loc1.distance(from: loc2)
        if segmentDistance < 100 {  // Filter out unrealistic jumps
          totalDistance += segmentDistance
        }
      }
    }

    return totalDistance
  }

  /// Legacy workout stats calculation for backward compatibility
  private func calculateWorkoutStatsLegacy() -> WorkoutStats {
    // Try to calculate enhanced stats from existing coordinate data if available
    if !coordinates.isEmpty && coordinates.count > 1 {
      let enhancedStats = Self.calculateWorkoutStats(
        from: coordinates,
        startTime: startTime,
        endTime: endTime,
        activeRunningTime: activeRunningTime,
        weight: weight,
        sportType: sportType
      )

      // Store the calculated stats for future use
      self.storedWorkoutStats = enhancedStats

      return enhancedStats
    }

    // Fallback for activities with insufficient coordinate data
    let totalCaloriesValue = storedCalories ?? calculateCalories()
    let totalTime = endTime.timeIntervalSince(startTime)
    let pauseTime = totalTime - activeRunningTime

    // Estimate active vs pause calories split for legacy activities based on time ratio
    let activeTimeRatio = totalTime > 0 ? activeRunningTime / totalTime : 1.0

    // Most calories come from active time, but use time-based proportion
    // Apply a minimum 80% to active time even if most time was paused
    let minActiveRatio = 0.8
    let adjustedActiveRatio = max(activeTimeRatio, minActiveRatio)
    let adjustedPauseRatio = 1.0 - adjustedActiveRatio

    let activeCaloriesEstimate = totalCaloriesValue * adjustedActiveRatio
    let pauseCaloriesEstimate = totalCaloriesValue * adjustedPauseRatio

    return WorkoutStats(
      averagePace: storedPace,
      bestPace: 0,  // Not available in legacy data
      averageSpeed: storedPace > 0 ? 1000.0 / (storedPace * 60.0) : 0,
      maxSpeed: 0,  // Not available in legacy data
      totalTime: totalTime,
      activeTime: activeRunningTime,
      pauseTime: pauseTime,
      activeCalories: activeCaloriesEstimate,
      pauseCalories: pauseCaloriesEstimate,
      totalCalories: totalCaloriesValue,
      metricLaps: [],  // Not available in legacy data
      imperialLaps: []  // Not available in legacy data
    )
  }

  /// Optimized simplified coordinates for UI display (max 1000 points)
  var simplifiedCoordinates: [Coordinate] {
    if coordinates.count <= 1000 {
      return coordinates
    }
    return RouteSimplifier.simplifyRoute(coordinates, maxPoints: 1000)
  }

  /// High-precision simplified coordinates for detailed analysis (max 500 points)
  var detailedSimplifiedCoordinates: [Coordinate] {
    if coordinates.count <= 500 {
      return coordinates
    }
    return RouteSimplifier.simplifyRoute(coordinates, maxPoints: 500)
  }

  /// Get simplified route with intelligent caching
  func getSimplifiedRoute(tolerance: Double = 0.0001) -> [Coordinate] {
    let cacheKey = String(format: "%.6f", tolerance)

    // Check cache validity
    if let cached = simplificationCache[cacheKey],
      let timestamp = cacheTimestamp,
      Date().timeIntervalSince(timestamp) < 300
    {  // 5-minute cache
      return cached
    }

    // Generate simplified route
    let simplified = RouteSimplifier.safeSimplify(coordinates: coordinates, tolerance: tolerance)

    // Update cache with size management
    updateCache(key: cacheKey, value: simplified)

    return simplified
  }

  /// Context-aware route simplification
  func getRouteForDisplay(context: SimplificationContext) -> [Coordinate] {
    let tolerance = RouteSimplifier.adaptiveTolerance(for: context)
    return getSimplifiedRoute(tolerance: tolerance)
  }

  /// Get simplified route with custom point limit
  func getRouteWithPointLimit(maxPoints: Int) -> [Coordinate] {
    if coordinates.count <= maxPoints {
      return coordinates
    }

    // Calculate tolerance to achieve target point count
    var tolerance = 0.0001
    var result = coordinates

    while result.count > maxPoints && tolerance < 0.01 {
      tolerance *= 1.5
      result = RouteSimplifier.safeSimplify(coordinates: coordinates, tolerance: tolerance)
    }

    return result
  }

  /// Smart cache management
  private func updateCache(key: String, value: [Coordinate]) {
    // Remove oldest cache entries if limit exceeded
    if simplificationCache.count >= maxCacheSize {
      let keysToRemove = Array(
        simplificationCache.keys.prefix(simplificationCache.count - maxCacheSize + 1))
      keysToRemove.forEach { simplificationCache.removeValue(forKey: $0) }
    }

    simplificationCache[key] = value
    cacheTimestamp = Date()
  }

  /// Clear cache to free memory
  func clearSimplificationCache() {
    simplificationCache.removeAll()
    cacheTimestamp = nil
  }

  /// Get cache statistics for debugging
  var cacheStats: String {
    return "Cache entries: \(simplificationCache.count), Size: \(maxCacheSize)"
  }

  // Helper method to determine if the activity had meaningful movement
  func hadMeaningfulMovement() -> Bool {
    // Check if we have a minimum distance threshold
    if distance > 10 {  // 10 meters as a minimum threshold for meaningful movement
      return true
    }

    // If distance is small, check if we have any active segments with speed
    var hasActiveMovement = false

    // Group coordinates into segments by isPaused status
    var currentSegment: [Coordinate] = []
    var segments: [[Coordinate]] = []

    for coordinate in coordinates {
      if currentSegment.isEmpty {
        currentSegment.append(coordinate)
      } else if currentSegment[0].isPaused == coordinate.isPaused {
        currentSegment.append(coordinate)
      } else {
        segments.append(currentSegment)
        currentSegment = [coordinate]
      }
    }
    if !currentSegment.isEmpty {
      segments.append(currentSegment)
    }

    // Only check active segments
    let activeSegments = segments.filter { !$0.isEmpty && !$0[0].isPaused }

    for segment in activeSegments {
      guard segment.count > 1 else { continue }

      // Check if there's any meaningful speed in this segment
      for i in 0..<(segment.count - 1) {
        if let speed1 = segment[i].speed, let speed2 = segment[i + 1].speed,
          speed1 > 0.5 || speed2 > 0.5
        {  // 0.5 m/s threshold (slow walking)
          hasActiveMovement = true
          break
        }
      }

      if hasActiveMovement {
        break
      }
    }

    return hasActiveMovement
  }
}

// Coordinate model for storing location data
struct Coordinate: Codable {
  let latitude: Double
  let longitude: Double
  let altitude: Double?  // ✅ OPTIONAL FOR BACKWARD COMPATIBILITY
  let timestamp: Date
  let isPaused: Bool
  let speed: Double?

  var clCoordinate: CLLocationCoordinate2D {
    CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
  }

  // Backward compatible initializer
  init(
    latitude: Double, longitude: Double, altitude: Double? = nil, timestamp: Date, isPaused: Bool,
    speed: Double? = nil
  ) {
    self.latitude = latitude
    self.longitude = longitude
    self.altitude = altitude
    self.timestamp = timestamp
    self.isPaused = isPaused
    self.speed = speed
  }
}

// MARK: - Memory Management
extension RunActivity {
  /// Automatic cache cleanup on memory pressure
  @objc private func handleMemoryWarning() {
    clearSimplificationCache()
    print("RunActivity: Cleared simplification cache due to memory pressure")
  }

  /// Setup memory warning observer
  private func setupMemoryManagement() {
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleMemoryWarning),
      name: UIApplication.didReceiveMemoryWarningNotification,
      object: nil
    )
  }
}

// MARK: - Deletion Preparation
extension RunActivity {
  /// Prepare for deletion by clearing memory-intensive cached data
  func prepareForDeletion() {
    clearSimplificationCache()

    // --- CRITICAL: Clear the massive coordinates array to prevent main thread blocking ---
    // This is the most important step to release memory pressure.
    coordinates.removeAll()

    // Clear workout stats data to reduce memory pressure
    // Note: WorkoutStats is immutable, so we set it to nil instead of mutating
    storedWorkoutStats = nil

    print("RunActivity: Prepared for deletion - cleared coordinates and large data structures")
  }

  /// Estimate memory usage for logging purposes
  var estimatedMemoryUsage: Int {
    let coordinateSize = MemoryLayout<Coordinate>.size
    let baseSize = MemoryLayout<RunActivity>.size
    let cacheSize = simplificationCache.values.reduce(0) { $0 + $1.count * coordinateSize }

    // Include lap data in memory estimation
    var lapDataSize = 0
    if let workoutStats = storedWorkoutStats {
      lapDataSize = workoutStats.metricLaps.count * MemoryLayout<LapData>.size
      lapDataSize += workoutStats.imperialLaps.count * MemoryLayout<LapData>.size
    }

    return baseSize + (coordinates.count * coordinateSize) + cacheSize + lapDataSize
  }

  /// Check if this activity has large location data that might cause performance issues
  var hasLargeLocationData: Bool {
    return coordinates.count > 1000  // Threshold for "large" data
  }
}
