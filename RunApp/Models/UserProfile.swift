import Foundation
import SwiftData

enum Language: String, Codable, CaseIterable {
    case arabic = "ar"
    case chineseSimplified = "zh-Hans"
    case chineseTraditional = "zh-Hant"
    case german = "de"
    case english = "en"
    case french = "fr"
    case indonesian = "id"
    case italian = "it"
    case japanese = "ja"
    case korean = "ko"
    case portuguese = "pt"
    case russian = "ru"
    case spanish = "es"
    
    // Custom initializer to handle legacy values stored in database
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let stringValue = try container.decode(String.self)
        
        // Handle legacy display name values that might be stored in database
        switch stringValue {
        case "English":
            self = .english
        case "Arabic", "العربية":
            self = .arabic
        case "Chinese Simplified", "简体中文":
            self = .chineseSimplified
        case "Chinese Traditional", "繁體中文":
            self = .chineseTraditional
        case "German", "Deutsch":
            self = .german
        case "French", "Français":
            self = .french
        case "Indonesian", "Bahasa Indonesia":
            self = .indonesian
        case "Italian", "Italiano":
            self = .italian
        case "Japanese", "日本語":
            self = .japanese
        case "Korean", "한국어":
            self = .korean
        case "Portuguese", "Português":
            self = .portuguese
        case "Russian", "Русский":
            self = .russian
        case "Spanish", "Español":
            self = .spanish
        default:
            // Try to initialize with current raw values (language codes)
            if let language = Language(rawValue: stringValue) {
                self = language
            } else {
                // Fallback to english if unknown value
                print("Language enum: Unknown value '\(stringValue)', falling back to English")
                self = .english
            }
        }
    }
    
    // Standard encoding uses the raw value (language code)
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(self.rawValue)
    }
    
    // Computed property to get display name for UI
    var displayName: String {
        switch self {
        case .arabic: return "العربية"
        case .chineseSimplified: return "简体中文"
        case .chineseTraditional: return "繁體中文"
        case .german: return "Deutsch"
        case .english: return "English"
        case .french: return "Français"
        case .indonesian: return "Bahasa Indonesia"
        case .italian: return "Italiano"
        case .japanese: return "日本語"
        case .korean: return "한국어"
        case .portuguese: return "Português"
        case .russian: return "Русский"
        case .spanish: return "Español"
        }
    }
}

enum Gender: String, Codable, CaseIterable {
    case male = "Male"
    case female = "Female"
    case other = "Other"
    case preferNotToSay = "Prefer not to say"

    // Add this computed property
    var localizationKey: String {
        switch self {
        case .male: return "male"
        case .female: return "female"
        case .other: return "other"
        case .preferNotToSay: return "prefer_not_to_say"
        }
    }
}


extension Gender {
    /// Returns the localized string for the gender.
    var localized: String {
        switch self {
        case .male:
            return "male".localized
        case .female:
            return "female".localized
        case .other:
            return "other".localized
        case .preferNotToSay:
            return "prefer_not_to_say".localized
        }
    }
}


enum MetronomeAlertFrequency: Int, CaseIterable, Codable {
    case everyBeat = 1
    case everyOtherBeat = 2
    case every4thBeat = 4
    case every6thBeat = 6
    
    var description: String {
        switch self {
        case .everyBeat:
            return "every_beat".localized
        case .everyOtherBeat:
            return "every_other_beat".localized
        case .every4thBeat:
            return "every_4th_beat".localized
        case .every6thBeat:
            return "every_6th_beat".localized
        }
    }
}

@Model
class UserProfile {
    var weight: Double?
    var birthDate: Date?
    var height: Double?
    var name: String?
    var email: String?
    var units: UnitSystem = UnitSystem.metric
    var weightUnit: String?
    var heightUnit: String?
    var image: Data?
    var gender: Gender = Gender.preferNotToSay
    var language: Language = Language.english
    var hasExceededTrialLimit: Bool = false // Flag for trial limit persistence
    
    // Metronome settings
    var metronomeBPM: Int = 180
    var metronomeSound: Int = 1103
    var metronomeSoundEnabled: Bool = true
    var metronomeVibrationEnabled: Bool = false
    // Removed metronomeVibrationStrength
    var metronomeEnabled: Bool = true
    var metronomeAlertFrequency: MetronomeAlertFrequency = AppConstants.defaultMetronomeAlertFrequency
    
    // Volume settings
    var metronomeVolume: Float = 0.65 // Default 60% volume
    var audioAlertVolume: Float = AppConstants.defaultAudioPromptVolume
    
    // Speech settings
    var speechRate: Float = AppConstants.defaultAudioPromptRate
    
    // Static default values for reference by other components
    // This static var is not used anymore, as AppConstants now holds the defaults.
    // static let defaultSpeechRate: Float = AppConstants.defaultAudioPromptRate // Kept for reference, can be removed.
    
    // Audio Alert settings
    var audioAlertsEnabled: Bool = true
    var distanceAlertEnabled: Bool = true
    var distanceAlertInterval: Double = 1.0 // Default 1 km/mile
    var timeAlertEnabled: Bool = true
    var timeAlertInterval: Double = 300.0 // Default 5 minutes (300 seconds)
    var calorieAlertEnabled: Bool = true
    var calorieAlertInterval: Double = 100.0 // Default 100 calories
    var paceAlertEnabled: Bool = true
    var paceAlertInterval: Double = 300.0 // Default 5 minutes (300 seconds)
    
    // Smart Voice settings
    var smartVoiceEnabled: Bool = true // Default ON for natural and motivational audio prompts
    
    // Voice selection settings
    var voicePreferences: [String: String] = [:] // Maps language codes to voice identifiers
    var selectedVoiceIdentifier: String? = nil // Current selected voice for current language
    
    // Performance optimization: Track last activity sport type for AnalysisView
    var lastActivitySportType: SportType? = nil
    
    // 🚀 PERFORMANCE: Cache activity count to avoid loading all activities in SettingsView
    var cachedActivityCount: Int = 0
    var lastActivityCountUpdate: Date?
    
    var lastModifiedDate: Date = Date() // Added for iCloud de-duplication

    var age: Int? {
        guard let birthDate = birthDate else { return nil }
        return Calendar.current.dateComponents([.year], from: birthDate, to: Date()).year
    }
    
    // MARK: - Voice Preference Management
    
    /// Get the selected voice identifier for a specific language
    func getVoiceForLanguage(_ languageCode: String) -> String? {
        return voicePreferences[languageCode]
    }
    
    /// Set the voice preference for a specific language
    func setVoiceForLanguage(_ languageCode: String, voiceIdentifier: String?) {
        if let voiceIdentifier = voiceIdentifier {
            voicePreferences[languageCode] = voiceIdentifier
        } else {
            voicePreferences.removeValue(forKey: languageCode)
        }
        
        // Update selectedVoiceIdentifier if this is the current language
        if languageCode == language.rawValue {
            selectedVoiceIdentifier = voiceIdentifier
        }
        
        // Update lastModifiedDate for iCloud sync
        lastModifiedDate = Date()
    }
    
    /// Get the current voice identifier for the user's current language
    func getCurrentLanguageVoice() -> String? {
        return getVoiceForLanguage(language.rawValue)
    }
    
    /// Update the current language and sync the selected voice
    func updateLanguage(_ newLanguage: Language) {
        // Save current voice preference for old language
        if let currentVoice = selectedVoiceIdentifier {
            voicePreferences[language.rawValue] = currentVoice
        }
        
        // Switch to new language
        language = newLanguage
        
        // Load voice preference for new language
        selectedVoiceIdentifier = voicePreferences[newLanguage.rawValue]
        
        // Update lastModifiedDate for iCloud sync
        lastModifiedDate = Date()
    }
    
    /// Clear all voice preferences (useful for reset functionality)
    func clearVoicePreferences() {
        voicePreferences.removeAll()
        selectedVoiceIdentifier = nil
        lastModifiedDate = Date()
    }
    
    /// Check if user has any custom voice preferences set
    func hasCustomVoicePreferences() -> Bool {
        return !voicePreferences.isEmpty || selectedVoiceIdentifier != nil
    }
    
    init(weight: Double? = nil,
         birthDate: Date? = nil, 
         height: Double? = nil, 
         units: UnitSystem = .metric, 
         name: String? = nil, 
         email: String? = nil, 
         image: Data? = nil,
         gender: Gender = .preferNotToSay,
         language: Language = .english,
         hasExceededTrialLimit: Bool = false,
         lastActivitySportType: SportType? = nil,
         cachedActivityCount: Int = 0,
         lastActivityCountUpdate: Date? = nil,
         lastModifiedDate: Date = Date()) { // Add to init parameters
        self.weight = weight
        self.birthDate = birthDate
        self.height = height
        self.units = units
        self.name = name
        self.email = email
        self.weightUnit = units == .metric ? "kg" : "lbs"
        self.heightUnit = units == .metric ? "cm" : "ft/in"
        self.image = image
        self.gender = gender
        self.language = language
        self.hasExceededTrialLimit = hasExceededTrialLimit // Initialize the new property
        self.lastActivitySportType = lastActivitySportType // Initialize the new property
        self.cachedActivityCount = cachedActivityCount // Initialize the new property
        self.lastActivityCountUpdate = lastActivityCountUpdate // Initialize the new property
        self.lastModifiedDate = lastModifiedDate // Initialize the new property
        
        // Metronome settings are initialized by their declaration defaults
        // self.metronomeBPM = 180 // No longer needed here
        // self.metronomeEnabled = true // No longer needed here
        // self.metronomeSoundEnabled = true // No longer needed here
        // self.metronomeVibrationEnabled = false // No longer needed here
        // self.metronomeSound = 1103 // No longer needed here
        // self.metronomeAlertFrequency = MetronomeAlertFrequency.everyOtherBeat // No longer needed here
    }
}

enum UnitSystem: String, Codable, CaseIterable {
    case metric
    case imperial

    var weightUnit: String {
        switch self {
        case .metric: return "kg"
        case .imperial: return "lbs"
        }
    }

    var heightUnit: String {
        switch self {
        case .metric: return "cm"
        case .imperial: return "ft/in"
        }
    }
    
    var speedUnit: String {
        switch self {
        case .metric: return "km/h"
        case .imperial: return "mph"
        }
    }
    
    var paceUnit: String {
        switch self {
        case .metric: return "min/km"
        case .imperial: return "min/mi"
        }
    }
    
    func formatDistance(_ meters: Double, abbreviated: Bool = false) -> String {
        switch self {
        case .metric:
            let km = meters / 1000.0
            return String(format: "%.2f %@", km, abbreviated ? "km" : "kilometers")
        case .imperial:
            let miles = meters / 1609.34
            return String(format: "%.2f %@", miles, abbreviated ? "mi" : "miles")
        }
    }
    
    var distanceUnit: String {
        switch self {
        case .metric: return "km"
        case .imperial: return "mi"
        }
    }
    
    func convertDistance(_ meters: Double) -> Double {
        switch self {
        case .metric:
            return meters / 1000.0
        case .imperial:
            return meters / 1609.34
        }
    }
}
