import Foundation
import HealthKit

enum SportType: String, CaseIterable, Codable {
    case run, walk, hike, bike
    
    var iconName: String {
        switch self {
        case .run: return "figure.run"
        case .walk: return "figure.walk"
        case .hike: return "figure.hiking"
        case .bike: return "figure.outdoor.cycle"
        }
    }
    
    /// Maps SportType to HealthKit workout activity type for iPhone 16 iOS 17+ integration
    var healthKitActivityType: HKWorkoutActivityType {
        switch self {
        case .run: return .running
        case .walk: return .walking
        case .hike: return .hiking
        case .bike: return .cycling
        }
    }
    
    private func interpolate(pace: Double, pace1: Double, met1: Double, pace2: Double, met2: Double) -> Double {
        return met1 + ((pace - pace1) / (pace2 - pace1)) * (met2 - met1)
    }
    
    /// Calculate MET value based purely on pace/speed (no more binary active/resting distinction)
    /// - Parameter paceMinPerKm: Current pace in minutes per kilometer
    /// - Returns: MET value based on actual movement speed
    func getMET(forPace paceMinPerKm: Double) -> Double {
        // Define speed thresholds for resting vs active states
        // Very slow movement (>18 min/km = <3.33 km/h = <0.93 m/s) considered "resting"
        let restingPaceThreshold: Double = 18.0
        
        // If moving very slowly or stationary, use resting MET values
        if paceMinPerKm >= restingPaceThreshold {
            switch self {
            case .run: return 1.5  // Standing/resting
            case .walk: return 1.3
            case .hike: return 1.5
            case .bike: return 1.3
            }
        }
        
        // For all other speeds, use dynamic pace-based MET calculation
        switch self {
        case .run:
            switch paceMinPerKm {
            case ..<2.5: return 30.0  // Elite sprinting
            case 2.5..<3.0: return interpolate(pace: paceMinPerKm, pace1: 2.5, met1: 24.0, pace2: 3.0, met2: 19.0)
            case 3.0..<3.5: return interpolate(pace: paceMinPerKm, pace1: 3.0, met1: 19.0, pace2: 3.5, met2: 16.0)
            case 3.5..<4.0: return interpolate(pace: paceMinPerKm, pace1: 3.5, met1: 16.0, pace2: 4.0, met2: 12.5)
            case 4.0..<4.5: return interpolate(pace: paceMinPerKm, pace1: 4.0, met1: 12.5, pace2: 4.5, met2: 11.8)
            case 4.5..<5.0: return interpolate(pace: paceMinPerKm, pace1: 4.5, met1: 11.8, pace2: 5.0, met2: 10.5)
            case 5.0..<5.5: return interpolate(pace: paceMinPerKm, pace1: 5.0, met1: 10.5, pace2: 5.5, met2: 10.0)
            case 5.5..<6.0: return interpolate(pace: paceMinPerKm, pace1: 5.5, met1: 10.0, pace2: 6.0, met2: 9.3)
            case 6.0..<6.5: return interpolate(pace: paceMinPerKm, pace1: 6.0, met1: 9.3, pace2: 6.5, met2: 9.5)
            case 6.5..<7.0: return interpolate(pace: paceMinPerKm, pace1: 6.5, met1: 9.5, pace2: 7.0, met2: 8.9)
            case 7.0..<7.5: return interpolate(pace: paceMinPerKm, pace1: 7.0, met1: 8.9, pace2: 7.5, met2: 8.5)
            case 7.5..<8.0: return interpolate(pace: paceMinPerKm, pace1: 7.5, met1: 8.5, pace2: 8.0, met2: 8.0)
            case 8.0..<8.5: return interpolate(pace: paceMinPerKm, pace1: 8.0, met1: 8.0, pace2: 8.5, met2: 7.4)
            case 8.5..<9.0: return interpolate(pace: paceMinPerKm, pace1: 8.5, met1: 7.4, pace2: 9.0, met2: 6.9)
            case 9.0..<9.5: return interpolate(pace: paceMinPerKm, pace1: 9.0, met1: 6.9, pace2: 9.5, met2: 6.5)
            case 9.5..<10.0: return interpolate(pace: paceMinPerKm, pace1: 9.5, met1: 6.5, pace2: 10.0, met2: 6.0)
            case 10.0..<10.5: return interpolate(pace: paceMinPerKm, pace1: 10.0, met1: 6.0, pace2: 10.5, met2: 5.5)
            case 10.5..<11.0: return interpolate(pace: paceMinPerKm, pace1: 10.5, met1: 5.5, pace2: 11.0, met2: 5.0)
            case 11.0..<11.5: return interpolate(pace: paceMinPerKm, pace1: 11.0, met1: 5.0, pace2: 11.5, met2: 4.7)
            case 11.5..<12.0: return interpolate(pace: paceMinPerKm, pace1: 11.5, met1: 4.7, pace2: 12.0, met2: 4.4)
            case 12.0..<12.5: return interpolate(pace: paceMinPerKm, pace1: 12.0, met1: 4.4, pace2: 12.5, met2: 4.1)
            case 12.5..<13.0: return interpolate(pace: paceMinPerKm, pace1: 12.5, met1: 4.1, pace2: 13.0, met2: 3.8)
            case 13.0..<13.5: return interpolate(pace: paceMinPerKm, pace1: 13.0, met1: 3.8, pace2: 13.5, met2: 3.6)
            case 13.5..<14.0: return interpolate(pace: paceMinPerKm, pace1: 13.5, met1: 3.6, pace2: 14.0, met2: 3.4)
            case 14.0..<14.5: return interpolate(pace: paceMinPerKm, pace1: 14.0, met1: 3.4, pace2: 14.5, met2: 3.2)
            case 14.5..<15.0: return interpolate(pace: paceMinPerKm, pace1: 14.5, met1: 3.2, pace2: 15.0, met2: 3.0)
            case 15.0..<15.5: return interpolate(pace: paceMinPerKm, pace1: 15.0, met1: 3.0, pace2: 15.5, met2: 2.8)
            case 15.5..<16.0: return interpolate(pace: paceMinPerKm, pace1: 15.5, met1: 2.8, pace2: 16.0, met2: 2.8)
            case 16.0..<16.5: return interpolate(pace: paceMinPerKm, pace1: 16.0, met1: 2.8, pace2: 16.5, met2: 2.7)
            case 16.5..<17.0: return interpolate(pace: paceMinPerKm, pace1: 16.5, met1: 2.7, pace2: 17.0, met2: 2.6)
            case 17.0..<17.5: return interpolate(pace: paceMinPerKm, pace1: 17.0, met1: 2.6, pace2: 17.5, met2: 2.5)
            case 17.5..<18.0: return interpolate(pace: paceMinPerKm, pace1: 17.5, met1: 2.5, pace2: 18.0, met2: 2.4)
            default: return 1.5  // Very slow walking → resting MET
            }
            
        case .walk:
            switch paceMinPerKm {
            case ..<2.5: return 30.0  // Elite sprinting
            case 2.5..<3.0: return interpolate(pace: paceMinPerKm, pace1: 2.5, met1: 24.0, pace2: 3.0, met2: 19.0)
            case 3.0..<3.5: return interpolate(pace: paceMinPerKm, pace1: 3.0, met1: 19.0, pace2: 3.5, met2: 16.0)
            case 3.5..<4.0: return interpolate(pace: paceMinPerKm, pace1: 3.5, met1: 16.0, pace2: 4.0, met2: 12.5)
            case 4.0..<4.5: return interpolate(pace: paceMinPerKm, pace1: 4.0, met1: 12.5, pace2: 4.5, met2: 11.8)
            case 4.5..<5.0: return interpolate(pace: paceMinPerKm, pace1: 4.5, met1: 11.8, pace2: 5.0, met2: 10.5)
            case 5.0..<5.5: return interpolate(pace: paceMinPerKm, pace1: 5.0, met1: 10.5, pace2: 5.5, met2: 10.0)
            case 5.5..<6.0: return interpolate(pace: paceMinPerKm, pace1: 5.5, met1: 10.0, pace2: 6.0, met2: 9.3)
            case 6.0..<6.5: return interpolate(pace: paceMinPerKm, pace1: 6.0, met1: 9.3, pace2: 6.5, met2: 9.5)
            case 6.5..<7.0: return interpolate(pace: paceMinPerKm, pace1: 6.5, met1: 9.5, pace2: 7.0, met2: 8.9)
            case 7.0..<7.5: return interpolate(pace: paceMinPerKm, pace1: 7.0, met1: 8.9, pace2: 7.5, met2: 8.5)
            case 7.5..<8.0: return interpolate(pace: paceMinPerKm, pace1: 7.5, met1: 8.5, pace2: 8.0, met2: 8.0)
            case 8.0..<8.5: return interpolate(pace: paceMinPerKm, pace1: 8.0, met1: 8.0, pace2: 8.5, met2: 7.4)
            case 8.5..<9.0: return interpolate(pace: paceMinPerKm, pace1: 8.5, met1: 7.4, pace2: 9.0, met2: 6.9)
            case 9.0..<9.5: return interpolate(pace: paceMinPerKm, pace1: 9.0, met1: 6.9, pace2: 9.5, met2: 6.5)
            case 9.5..<10.0: return interpolate(pace: paceMinPerKm, pace1: 9.5, met1: 6.5, pace2: 10.0, met2: 6.0)
            case 10.0..<10.5: return interpolate(pace: paceMinPerKm, pace1: 10.0, met1: 6.0, pace2: 10.5, met2: 5.5)
            case 10.5..<11.0: return interpolate(pace: paceMinPerKm, pace1: 10.5, met1: 5.5, pace2: 11.0, met2: 5.0)
            case 11.0..<11.5: return interpolate(pace: paceMinPerKm, pace1: 11.0, met1: 5.0, pace2: 11.5, met2: 4.7)
            case 11.5..<12.0: return interpolate(pace: paceMinPerKm, pace1: 11.5, met1: 4.7, pace2: 12.0, met2: 4.4)
            case 12.0..<12.5: return interpolate(pace: paceMinPerKm, pace1: 12.0, met1: 4.4, pace2: 12.5, met2: 4.1)
            case 12.5..<13.0: return interpolate(pace: paceMinPerKm, pace1: 12.5, met1: 4.1, pace2: 13.0, met2: 3.8)
            case 13.0..<13.5: return interpolate(pace: paceMinPerKm, pace1: 13.0, met1: 3.8, pace2: 13.5, met2: 3.6)
            case 13.5..<14.0: return interpolate(pace: paceMinPerKm, pace1: 13.5, met1: 3.6, pace2: 14.0, met2: 3.4)
            case 14.0..<14.5: return interpolate(pace: paceMinPerKm, pace1: 14.0, met1: 3.4, pace2: 14.5, met2: 3.2)
            case 14.5..<15.0: return interpolate(pace: paceMinPerKm, pace1: 14.5, met1: 3.2, pace2: 15.0, met2: 3.0)
            case 15.0..<15.5: return interpolate(pace: paceMinPerKm, pace1: 15.0, met1: 3.0, pace2: 15.5, met2: 2.8)
            case 15.5..<16.0: return interpolate(pace: paceMinPerKm, pace1: 15.5, met1: 2.8, pace2: 16.0, met2: 2.8)
            case 16.0..<16.5: return interpolate(pace: paceMinPerKm, pace1: 16.0, met1: 2.8, pace2: 16.5, met2: 2.7)
            case 16.5..<17.0: return interpolate(pace: paceMinPerKm, pace1: 16.5, met1: 2.7, pace2: 17.0, met2: 2.6)
            case 17.0..<17.5: return interpolate(pace: paceMinPerKm, pace1: 17.0, met1: 2.6, pace2: 17.5, met2: 2.5)
            case 17.5..<18.0: return interpolate(pace: paceMinPerKm, pace1: 17.5, met1: 2.5, pace2: 18.0, met2: 2.4)
            default: return 1.3  // Very slow walking → resting MET
            }
            
        case .hike:
            switch paceMinPerKm {
            case ..<8.57: return 10.5  // Technical terrain with pack
            case 8.57..<10.0: return interpolate(pace: paceMinPerKm, pace1: 8.57, met1: 10.5, pace2: 10.0, met2: 9.0)
            case 10.0..<12.0: return interpolate(pace: paceMinPerKm, pace1: 10.0, met1: 9.0, pace2: 12.0, met2: 7.8)
            case 12.0..<15.0: return interpolate(pace: paceMinPerKm, pace1: 12.0, met1: 7.8, pace2: 15.0, met2: 6.5)
            case 15.0..<20.0: return interpolate(pace: paceMinPerKm, pace1: 15.0, met1: 6.5, pace2: 20.0, met2: 5.0)
            case 20.0..<30.0: return interpolate(pace: paceMinPerKm, pace1: 20.0, met1: 5.0, pace2: 30.0, met2: 4.5)
            default: return 1.5  // Very slow walking → resting MET
            }
            
        case .bike:
            switch paceMinPerKm {
            case ..<1.5: return 22.0  // Pro sprint finish
            case 1.5..<1.71: return interpolate(pace: paceMinPerKm, pace1: 1.5, met1: 22.0, pace2: 1.71, met2: 19.0)
            case 1.71..<2.0: return interpolate(pace: paceMinPerKm, pace1: 1.71, met1: 19.0, pace2: 2.0, met2: 16.0)
            case 2.0..<2.4: return interpolate(pace: paceMinPerKm, pace1: 2.0, met1: 16.0, pace2: 2.4, met2: 13.5)
            case 2.4..<3.0: return interpolate(pace: paceMinPerKm, pace1: 2.4, met1: 13.5, pace2: 3.0, met2: 10.0)
            case 3.0..<4.0: return interpolate(pace: paceMinPerKm, pace1: 3.0, met1: 10.0, pace2: 4.0, met2: 6.0)
            case 4.0..<6.0: return interpolate(pace: paceMinPerKm, pace1: 4.0, met1: 6.0, pace2: 6.0, met2: 4.0)
            default: return 1.3  // Very slow walking → resting MET
            }
        }
    }
    
    // Legacy method for backward compatibility - now calls the simplified version
    func getMET(forPace paceMinPerKm: Double, isActive: Bool = true) -> Double {
        // For backward compatibility, ignore isActive parameter and use purely pace-based calculation
        return getMET(forPace: paceMinPerKm)
    }
    
    // Deprecated - use getMET(forPace:) instead
    var met: Double {
        getMET(forPace: 7.5) // Default to moderate pace
    }
}
