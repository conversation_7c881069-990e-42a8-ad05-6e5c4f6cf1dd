//
//  Notification+Extensions.swift
//  RunApp
//
//  Created by Cline on 1/24/25.
//

import Foundation

extension Notification.Name {
    /// Posted when the app should enter hibernation mode to conserve battery
    static let appShouldHibernate = Notification.Name("appShouldHibernate")
    
    /// Posted when the app should wake up from hibernation mode
    static let appShouldWakeUp = Notification.Name("appShouldWakeUp")
}
