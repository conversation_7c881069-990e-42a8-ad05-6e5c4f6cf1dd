import Foundation

/// Shared calorie calculation utility to ensure consistency between real-time and activity summary calculations
struct CalorieCalculator {
    
    /// Calculate calories burned for an activity
    /// - Parameters:
    ///   - weight: User's weight in kg (uses 70.0kg fallback if not provided)
    ///   - sportType: Type of sport/activity
    ///   - activeTime: Active time in seconds
    ///   - distance: Distance covered in meters (optional for pace calculation)
    ///   - storedPace: Pre-calculated pace in minutes/km (optional, takes precedence over distance calculation)
    ///   - hasMovement: Whether meaningful movement was detected (optional, defaults to true - DEPRECATED)
    /// - Returns: Calculated calories burned
    static func calculateCalories(
        weight: Double?,
        sportType: SportType,
        activeTime: TimeInterval,
        distance: Double? = nil,
        storedPace: Double? = nil,
        hasMovement: Bool? = nil
    ) -> Double {
        // Use provided weight or fallback to 70kg default
        let effectiveWeight = weight ?? 70.0
        guard effectiveWeight > 0 else { return 0 }
        guard activeTime > 0 else { return 0 }
        
        // Calculate or use provided pace
        let paceMinPerKm: Double
        if let stored = storedPace {
            paceMinPerKm = stored
        } else if let dist = distance, dist > 0 {
            // Calculate real-time pace
            let distanceKm = dist / 1000
            let timeMinutes = activeTime / 60
            paceMinPerKm = timeMinutes / distanceKm
            
            // Validate realistic pace (filter out GPS errors)
            guard paceMinPerKm <= 60 else { return 0 } // 60 min/km = very slow walking
        } else {
            // No pace data available - use very slow pace (will result in resting MET)
            paceMinPerKm = 20.0 // 20 min/km = stationary/very slow
        }
        
        // Get MET value based purely on pace (no more isActive parameter needed)
        let metValue = sportType.getMET(forPace: paceMinPerKm)
        
        // Calculate calories: MET × weight (kg) × time (hours)
        let hoursSpent = activeTime / 3600
        let calculatedCalories = metValue * effectiveWeight * hoursSpent
        
        // Apply minimum threshold to avoid tiny values (only for very short durations)
        if activeTime < 30 && calculatedCalories < 0.5 {
            return 0
        }
        
        return calculatedCalories
    }
    
    /// Calculate active calories for sports activities (simplified - now uses same logic as calculateCalories)
    static func calculateActiveCalories(
        weight: Double,
        sportType: SportType,
        activeTime: TimeInterval
    ) -> Double {
        guard weight > 0, activeTime > 0 else { return 0 }
        
        // Use moderate pace MET value for active time
        return calculateCalories(
            weight: weight,
            sportType: sportType,
            activeTime: activeTime,
            storedPace: 6.0 // Assume moderate 6 min/km pace
        )
    }
    
    /// Calculate resting calories (BMR equivalent) - DEPRECATED, use calculateCalories with slow pace instead
    static func calculateRestingCalories(
        weight: Double,
        time: TimeInterval
    ) -> Double {
        guard weight > 0, time > 0 else { return 0 }
        
        // Use slow pace to trigger resting MET calculation
        return calculateCalories(
            weight: weight,
            sportType: .walk,
            activeTime: time,
            storedPace: 20.0 // 20 min/km = resting pace
        )
    }
}