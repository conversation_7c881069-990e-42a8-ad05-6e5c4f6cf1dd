// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		EA1EBCFB2DA44DD900EF5020 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EA1EBCFA2DA44DD900EF5020 /* StoreKit.framework */; };
		EA205EF32D922C1F003C8C10 /* RevenueCat in Frameworks */ = {isa = PBXBuildFile; productRef = EA205EF22D922C1F003C8C10 /* RevenueCat */; };
		EA205EF52D922C1F003C8C10 /* RevenueCatUI in Frameworks */ = {isa = PBXBuildFile; productRef = EA205EF42D922C1F003C8C10 /* RevenueCatUI */; };
		EA9CBADB2DBBE9C300EF899D /* RunApp: Run with Metronome.storekit in Resources */ = {isa = PBXBuildFile; fileRef = EA9CBADA2DBBE9C300EF899D /* RunApp: Run with Metronome.storekit */; };
		EA9CBADD2DBBEA1100EF899D /* StoreKitTestCertificate.cer in Resources */ = {isa = PBXBuildFile; fileRef = EA9CBADC2DBBEA1100EF899D /* StoreKitTestCertificate.cer */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		EA1EBCFA2DA44DD900EF5020 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		EA9CBADA2DBBE9C300EF899D /* RunApp: Run with Metronome.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = "RunApp: Run with Metronome.storekit"; sourceTree = "<group>"; };
		EA9CBADC2DBBEA1100EF899D /* StoreKitTestCertificate.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = StoreKitTestCertificate.cer; sourceTree = "<group>"; };
		EAF026E72D32C98B0086FE10 /* RunApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RunApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		EA66B08C2D3440A200F40E13 /* Exceptions for "RunApp" folder in "RunApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = EAF026E62D32C98B0086FE10 /* RunApp */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		EAF026E92D32C98B0086FE10 /* RunApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				EA66B08C2D3440A200F40E13 /* Exceptions for "RunApp" folder in "RunApp" target */,
			);
			path = RunApp;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		EAF026E42D32C98B0086FE10 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EA1EBCFB2DA44DD900EF5020 /* StoreKit.framework in Frameworks */,
				EA205EF52D922C1F003C8C10 /* RevenueCatUI in Frameworks */,
				EA205EF32D922C1F003C8C10 /* RevenueCat in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		EA1EBCF92DA44DD900EF5020 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				EA1EBCFA2DA44DD900EF5020 /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		EAF026DE2D32C98B0086FE10 = {
			isa = PBXGroup;
			children = (
				EA9CBADA2DBBE9C300EF899D /* RunApp: Run with Metronome.storekit */,
				EA9CBADC2DBBEA1100EF899D /* StoreKitTestCertificate.cer */,
				EAF026E92D32C98B0086FE10 /* RunApp */,
				EA1EBCF92DA44DD900EF5020 /* Frameworks */,
				EAF026E82D32C98B0086FE10 /* Products */,
			);
			sourceTree = "<group>";
		};
		EAF026E82D32C98B0086FE10 /* Products */ = {
			isa = PBXGroup;
			children = (
				EAF026E72D32C98B0086FE10 /* RunApp.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		EAF026E62D32C98B0086FE10 /* RunApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EAF026F52D32C98C0086FE10 /* Build configuration list for PBXNativeTarget "RunApp" */;
			buildPhases = (
				EAF026E32D32C98B0086FE10 /* Sources */,
				EAF026E42D32C98B0086FE10 /* Frameworks */,
				EAF026E52D32C98B0086FE10 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				EAF026E92D32C98B0086FE10 /* RunApp */,
			);
			name = RunApp;
			packageProductDependencies = (
				EA205EF22D922C1F003C8C10 /* RevenueCat */,
				EA205EF42D922C1F003C8C10 /* RevenueCatUI */,
			);
			productName = RunApp;
			productReference = EAF026E72D32C98B0086FE10 /* RunApp.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		EAF026DF2D32C98B0086FE10 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					EAF026E62D32C98B0086FE10 = {
						CreatedOnToolsVersion = 16.1;
					};
				};
			};
			buildConfigurationList = EAF026E22D32C98B0086FE10 /* Build configuration list for PBXProject "RunApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ar,
				de,
				es,
				fr,
				id,
				it,
				ja,
				ko,
				pt,
				ru,
				"zh-Hant",
				"zh-Hans",
			);
			mainGroup = EAF026DE2D32C98B0086FE10;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				EA205EF12D922C1F003C8C10 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = EAF026E82D32C98B0086FE10 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				EAF026E62D32C98B0086FE10 /* RunApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		EAF026E52D32C98B0086FE10 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EA9CBADD2DBBEA1100EF899D /* StoreKitTestCertificate.cer in Resources */,
				EA9CBADB2DBBE9C300EF899D /* RunApp: Run with Metronome.storekit in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		EAF026E32D32C98B0086FE10 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		EAF026F32D32C98C0086FE10 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 555TQ6945R;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		EAF026F42D32C98C0086FE10 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 555TQ6945R;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		EAF026F62D32C98C0086FE10 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = RunApp/RunApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_ASSET_PATHS = "\"RunApp/Preview Content\"";
				DEVELOPMENT_TEAM = 555TQ6945R;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = RunApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = RunApp;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Enable \"Always Allow\" to keep tracking your activity when the screen is off or the app is in background, similar to maps navigation.";
				INFOPLIST_KEY_NSLocationAlwaysUsageDescription = "RunApp needs background location access to continue tracking your workouts even when the screen is locked or the app is in the background.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "RunApp uses your location to track runs, map routes, and measure distance. Your data stays private. Allow access?";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = app.isotropic.runapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		EAF026F72D32C98C0086FE10 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = RunApp/RunApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 3;
				DEVELOPMENT_ASSET_PATHS = "\"RunApp/Preview Content\"";
				DEVELOPMENT_TEAM = 555TQ6945R;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = RunApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = RunApp;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Enable \"Always Allow\" to keep tracking your activity when the screen is off or the app is in background, similar to maps navigation.";
				INFOPLIST_KEY_NSLocationAlwaysUsageDescription = "RunApp needs background location access to continue tracking your workouts even when the screen is locked or the app is in the background.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "RunApp uses your location to track runs, map routes, and measure distance. Your data stays private. Allow access?";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiresFullScreen = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 17;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = app.isotropic.runapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		EAF026E22D32C98B0086FE10 /* Build configuration list for PBXProject "RunApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EAF026F32D32C98C0086FE10 /* Debug */,
				EAF026F42D32C98C0086FE10 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EAF026F52D32C98C0086FE10 /* Build configuration list for PBXNativeTarget "RunApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EAF026F62D32C98C0086FE10 /* Debug */,
				EAF026F72D32C98C0086FE10 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		EA205EF12D922C1F003C8C10 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RevenueCat/purchases-ios-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		EA205EF22D922C1F003C8C10 /* RevenueCat */ = {
			isa = XCSwiftPackageProductDependency;
			package = EA205EF12D922C1F003C8C10 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCat;
		};
		EA205EF42D922C1F003C8C10 /* RevenueCatUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = EA205EF12D922C1F003C8C10 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCatUI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = EAF026DF2D32C98B0086FE10 /* Project object */;
}
