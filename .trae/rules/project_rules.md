USE SWIFTUI FOR ALL USER INTERFACES AND SWIFTDATA FOR PERSISTENCE!

---

# iOS SwiftUI MVVM Guidelines

## 1. MVVM Roles  
- **Model**: Data and business logic.  
- **View**: UI rendering (no logic).  
- **ViewModel**: Mediates between View and Model.  

---

## 2. Keep Views Small  
- Break UI into reusable components.  
- Avoid logic in Views; delegate to ViewModel.  

**Example**:  
```swift
struct UserProfileView: View {
    @StateObject var viewModel: UserProfileViewModel
    var body: some View {
        VStack {
            ProfileHeader(user: viewModel.user)
            ProfileDetails(details: viewModel.userDetails)
        }
    }
}
```

---

## 3. Reusable Components  
- Build generic, configurable components.  

**Example**:  
```swift
struct PrimaryButton: View {
    var title: String
    var action: () -> Void
    var body: some View {
        Button(action: action) {
            Text(title).padding().background(Color.blue).foregroundColor(.white).cornerRadius(8)
        }
    }
}
```

---

## 4. ViewModel Best Practices  
- Use `@Published` for state updates.  
- Expose only necessary data to Views.  

**Example**:  
```swift
class UserProfileViewModel: ObservableObject {
    @Published var user: User
    init(user: User) { self.user = user }
    func updateUser() { /* Logic */ }
}
```

---

## 5. Dependency Injection  
- Pass ViewModels or services to Views.  

**Example**:  
```swift
struct UserProfileView: View {
    @StateObject var viewModel: UserProfileViewModel
    init(user: User) {
        _viewModel = StateObject(wrappedValue: UserProfileViewModel(user: user))
    }
    var body: some View { /* View code */ }
}
```

---

## 6. Separate Concerns  
- Move networking, state, and constants into separate files.  

---

## 7. Testing  
- Write unit tests for ViewModels.  
- Use SwiftUI previews for UI testing.  

**Example**:  
```swift
class UserProfileViewModelTests: XCTestCase {
    func testUpdateUser() {
        let user = User(name: "John")
        let viewModel = UserProfileViewModel(user: user)
        viewModel.updateUser()
        XCTAssertEqual(viewModel.user.name, "John Updated")
    }
}
```

---

## 8. Folder Structure  
```
/Features  
  /UserProfile  
    /Models  
    /ViewModels  
    /Views  
/Shared  
  /Components  
  /Services  
```

---

## 9. Avoid Over-Engineering  
- Start simple; refactor as needed.  

---

### Example Project  
```swift
// Model  
struct User { let name: String }  

// ViewModel  
class UserProfileViewModel: ObservableObject {  
    @Published var user: User  
    init(user: User) { self.user = user }  
}  

// View  
struct UserProfileView: View {  
    @StateObject var viewModel: UserProfileViewModel  
    var body: some View {  
        VStack {  
            Text("Hello, \(viewModel.user.name)!")  
            PrimaryButton(title: "Update", action: {})  
        }  
    }  
}  

// Reusable Component  
struct PrimaryButton: View {  
    var title: String  
    var action: () -> Void  
    var body: some View {  
        Button(action: action) {  
            Text(title).padding().background(Color.blue).foregroundColor(.white).cornerRadius(8)  
        }  
    }  
}  

// Preview  
struct UserProfileView_Previews: PreviewProvider {  
    static var previews: some View {  
        UserProfileView(viewModel: UserProfileViewModel(user: User(name: "John")))  
    }  
}
```

---
